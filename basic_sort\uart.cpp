﻿/********************************************************************************
* Copyright (C) 2025, JD.COM, Inc.
* All rights reserved.
* FileName    : uart.c
* Author      : wangmenghu   Version: V1.0   Data:2018-08-21
* Description : realize the uart function lib.
********************************************************************************/

#include "uart.hpp"
#include <spdlog/spdlog.h>
/*******************************************************************************
* Function Name		 : open_uart
* Description	     : open the uart device
* Input 		     : pathname:the name of the uart
* Output		     : NONE
* Return		     : file descriptor if OK, -1 on error 
*******************************************************************************/
int open_uart(const char *pPathName)
{
	int iFd = 0;
	int iRet = 0;
	
	if (pPathName == NULL) {
		return -1;
	}

	iFd = open(pPathName, O_RDWR | O_NOCTTY | O_NDELAY);
	if (iFd < 0) {
		SPDLOG_INFO("open :{} is failed\n", pPathName);
		return -1;
	}
	
	iRet = fcntl(iFd, F_SETFL, 0);
	if (iRet < 0)	//set block
	{
		SPDLOG_INFO("fcntl failed!\n");
		close(iFd);
		return -1;
	}
	
	//if input parameter(file descriptor) is terminal device, return 1; else return 0
	//if (0 == isatty(STDIN_FILENO))
	//{
	//	LOG_ERR("standard input is not a terminal device\n");
	//	close(iFd);
	//	return -1;
	//}

	return iFd;
}

/*******************************************************************************
* Function Name      : set_uart_opt
* Description	     : set the option of the uart
* Input 		     : iFd:the fd of opened uart; iBaudrate:the baudrate of the uart
					   cBits:bit of byte,such as DATA_BIT_7;cCheck:the style of check ,such asPARITY_ODD
					   cStop:the bit of stop,such as STOP_BIT_1
* Output		     : NONE
* Return		     : 0 if OK, -1 on error
*******************************************************************************/
int set_uart_opt(int iFd, int iBaudrate, char cBits, char cCheck, char cStop)
{
	struct termios stNewTio, stOldTio;

	if (0 != tcgetattr(iFd, &stOldTio))
	{
		SPDLOG_INFO("set the state of fd into *TERMIOS_P failure. error:{}\n", strerror(errno));
		return -1;
	}

	bzero(&stNewTio, sizeof(stNewTio));

	///ensure the uart port cannot be disturb by other port
	stNewTio.c_cflag |= CLOCAL | CREAD;
	stNewTio.c_cflag &= ~CSIZE;

	if (DATA_BIT_7 == cBits)
	{
		stNewTio.c_cflag |= CS7;
	}
	else
	{
		stNewTio.c_cflag |= CS8;
	}

	switch (cCheck)
	{
		case PARITY_ODD:
			stNewTio.c_cflag |= PARENB;
			stNewTio.c_cflag |= PARODD;
			stNewTio.c_iflag |= (INPCK | ISTRIP);
			break;
		case PARITY_EVEN:
			stNewTio.c_iflag |= (INPCK | ISTRIP);
			stNewTio.c_cflag |= PARENB;
			stNewTio.c_cflag &= ~PARODD;
			break;
		case PARITY_NONE:
			stNewTio.c_cflag &= ~PARENB;
			break;
		default:
			stNewTio.c_cflag &= ~PARENB;
			break;
	}

	switch (iBaudrate)
	{
		case  BAUD_1200:
			cfsetispeed(&stNewTio, B1200);
			cfsetospeed(&stNewTio, B1200);
			break;
		case  BAUD_2400:
			cfsetispeed(&stNewTio, B2400);
			cfsetospeed(&stNewTio, B2400);
			break;
		case  BAUD_4800:
			cfsetispeed(&stNewTio, B4800);
			cfsetospeed(&stNewTio, B4800);
			break;
		case  BAUD_9600:
			cfsetispeed(&stNewTio, B9600);
			cfsetospeed(&stNewTio, B9600);
			break;
		case  BAUD_19200:
			cfsetispeed(&stNewTio, B19200);
			cfsetospeed(&stNewTio, B19200);
			break;
		case  BAUD_38400:
			cfsetispeed(&stNewTio, B38400);
			cfsetospeed(&stNewTio, B38400);
			break;
		case  BAUD_57600:
			cfsetispeed(&stNewTio, B57600);
			cfsetospeed(&stNewTio, B57600);
			break;
		case  BAUD_115200:
			cfsetispeed(&stNewTio, B115200);
			cfsetospeed(&stNewTio, B115200);
			break;
		case BAUD_460800:
			cfsetispeed(&stNewTio, B460800);
			cfsetospeed(&stNewTio, B460800);
			break;
		default:
			cfsetispeed(&stNewTio, B9600);
			cfsetospeed(&stNewTio, B9600);
			break;
	}

	if (STOP_BIT_1 == cStop)
	{
		stNewTio.c_cflag &= ~CSTOPB;
	}		
	else
	{
		stNewTio.c_cflag |= CSTOPB;
	}

	stNewTio.c_cc[VTIME] = 2; //read immediately with block
	stNewTio.c_cc[VMIN] = 50;
	tcflush(iFd,TCIFLUSH);

	if ( 0 != (tcsetattr(iFd, TCSANOW, &stNewTio))) //with immediate effect
	{
		SPDLOG_INFO("set attr of uart failure, error:{}", strerror(errno));
		return -1;
	}

	SPDLOG_INFO("uart'parameter:[Baudrate=:{}, DataBit=:{}, ParityBit=:{}, StopBit=:{}]\n", iBaudrate, cBits, cCheck, cStop);

	return 0;
}

/*******************************************************************************
* Function Name      : init_uart
* Description	     : init the uart
* Input 		     : NONE
* Output		     : NONE
* Return		     : fd if OK, -1 on error
*******************************************************************************/
int init_uart(uart_attr_t *pUartAttar)
{
	int iFd = 0;
	int iRet = 0;

	if (pUartAttar == NULL) {
		return -1;
	}

	iFd = open_uart(pUartAttar->pUartName);
	if (iFd < 0)
	{
		SPDLOG_INFO("open_uart failed\n");
		return -1;
	}

	iRet = set_uart_opt(iFd, pUartAttar->iBaud, pUartAttar->cDateBit, pUartAttar->cParity, pUartAttar->cStopBit);
	if (iRet < 0)
	{
		SPDLOG_INFO("set uart opt failure\n");
		close_uart(iFd);
		return -1;
	}

	return iFd;
}

/*******************************************************************************
* Function Name      : send_uart
* Description	     : send the data by uart
* Input 		     : iFd:the fd of opened uart; pBuf:the data to be send
					   iLen:the length of the pBuf
* Output		     : NONE
* Return		     : number of bytes written if OK , -1 on error
*******************************************************************************/
int send_uart(int iFd, char *pBuf, int iLen)
{
	int iRet = 0;

	iRet = write(iFd, pBuf, iLen);
	if (iRet < 0)
	{
		SPDLOG_INFO("write device failure, error:{}\n", strerror(errno));
		return iRet;
	}

	return iRet;
}

/*******************************************************************************
* Function Name      : read_uart
* Description	     : send the data by uart,the uart is block
* Input 		     : fd:the fd of opened uart; pBuf:the data to be send
iLen:the length of the pBuf
* Output		     : NONE
* Return		     : number of bytes written if OK , -1 on error
*******************************************************************************/
size_t recv_uart(int iFd, char *pRcvBuf, ssize_t iLen)
{
	int iByte = 0;

	iByte = read(iFd, pRcvBuf, iLen);
	if(0 >= iByte)
		return -1;
	
	return iByte;
}

/*******************************************************************************
* Function Name      : close_uart
* Description	     : close uart
* Input 		     : iFd:the fd of opened uart;
* Output		     : NONE
* Return		     :0 if OK , -1 on error
*******************************************************************************/
int close_uart(int iFd)
{
	if (0 != close(iFd))
	{
		SPDLOG_INFO("close uart fd failure, error:{}\n", strerror(errno));
		return -1;
	}

	return 0;
}
