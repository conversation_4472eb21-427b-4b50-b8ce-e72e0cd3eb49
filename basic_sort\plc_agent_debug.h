﻿

#ifndef __VEHICLE_AGENT_DEBUG_H__
#define __VEHICLE_AGENT_DEBUG_H__


//#define  TCP_SOCKET_DEBUG
//#define  EPOLL_POLLER_DEBUG

//#define  PROTOCOL_DEBUG	

//#define  SCHEDULER_MSG_DEBUG

//#define  PLC_MANAGE_DEBUG

//#define  PLC_AGENT_DEBUG


#define  OUTPUT_ERR				(std::cout << "error>>: " << __FILE__ <<"--"<< __FUNCTION__<<"--" << __LINE__ << std::endl)

#define  OUTPUT_LOG				(std::cout << "log>>: " << __FILE__ <<"--"<< __FUNCTION__<<"--" << __LINE__ << std::endl)


#define  PLC_NET_EVENT_LOG		
#define  PLC_PROTOCOL_LOG		
#define  PLC_ZMQ_LOG		
#define  PLC_DEVICE_LOG		
#define  PLC_HEART_BEAT_LOG		


#endif
