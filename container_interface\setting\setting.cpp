#include <fstream>      // std::ifstream

#include "setting.hpp"

int setting::load_setting(const char *file_name)
{
	int ret;

	SPDLOG_INFO("load setting from:{}", file_name);

	try
	{
		ret = load_container_setting(file_name);
	}
	catch (nlohmann::detail::exception &e)
	{
		SPDLOG_ERROR("json throw an error:{}", e.what());	
		ret = 0;
	}

	return ret;
}

int setting::load_container_setting(const char *file_name)
{
	std::ifstream setting_file(file_name, std::ifstream::in);
	if (!setting_file)
	{
		SPDLOG_ERROR("create default settings");
		settings.rfid_prefix = "";
        settings.rfid_format = "%015llu";
        settings.rfid_value_offset = 0;
	}
	else
	{
    	json root;
		setting_file >> root;
		if (root.count("rfid_prefix") != 0)
			settings.rfid_prefix = root["rfid_prefix"];
		else
			settings.rfid_prefix = "";

		if (root.count("rfid_format") != 0)
			settings.rfid_format = root["rfid_format"];
		else
			settings.rfid_format = "%015llu";

		if (root.count("rfid_value_offset") != 0)
			settings.rfid_value_offset = root["rfid_value_offset"];
		else
			settings.rfid_value_offset = 0;

		if (root.count("version") != 0)
			settings.version = root["version"];
		else
			settings.version = "";

		if (root.count("has_rfid_limit") != 0)
			settings.has_rfid_limit = root["has_rfid_limit"];
		else
			settings.has_rfid_limit = false;

		if (root.count("rfid_lower_limit") != 0)
			settings.rfid_lower_limit = root["rfid_lower_limit"];
		else
			settings.rfid_lower_limit = 0;

		if (root.count("rfid_upper_limit") != 0)
			settings.rfid_upper_limit = root["rfid_upper_limit"];
		else
			settings.rfid_upper_limit = 10000000;

		if (root.count("server_ip") != 0)
			settings.server_ip = root["server_ip"];
		else
			settings.server_ip = "************";

		if (root.count("unbind_interval") != 0)
			settings.unbind_interval = root["unbind_interval"];
		else
			settings.unbind_interval = 1500;

		if (root.count("query_time") != 0)
			settings.query_time = root["query_time"];
		else
			settings.query_time = 1000;

		if (root.count("led_repet_control_time") != 0)
			settings.led_repet_control_time = root["led_repet_control_time"];
		else
			settings.led_repet_control_time = 1000;

		if (root.count("led_repet_control_count") != 0)
			settings.led_repet_control_count = root["led_repet_control_count"];
		else
			settings.led_repet_control_count = 0;

			
	}

    return 1;
}

