/********************************************************************************
* Copyright (C) 2025, JD.COM, Inc.
* All rights reserved.
* FileName: can.h
* Author: <PERSON><PERSON><PERSON><PERSON>   Version: V1.0   Data:2017-06-23
* Description:
*            : set can para cmd:
*            : ip link set can0 up type can bitrate 500000
*            : ip link set can0 down
*            : ip link set can0 up
********************************************************************************/
#ifndef __CAN_H__
#define __CAN_H__

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>

#include <net/if.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <errno.h>
#include <fcntl.h>
#include <linux/can.h>
#include <linux/can/raw.h>

#define BLOCK						0
#define NONBLOCK					1

#define CAN0						0
#define CAN1						1

#define CAN0_FILTER_SIZE	36
#define CAN1_FILTER_SIZE	36

#define TEST_CANID					0x7EE

#define PGV_CANID_START 			0x000
#define PGV_CANID_TxPDO1			0x188
#define PGV_CANID_TxPDO2			0x288
#define PGV_CANID_TxPDO3			0x388
#define PGV_CANID_TxPDO4			0x488
#define PGV_CANID_TxTEST			0x608
#define PGV_CANID_TxTEST_ACK		0x708

#define BATT_CANID_REQ0X16			0x16
#define BATT_CANID_REQ0X20			0x20
#define BATT_CANID_ACK0X10			0x10
#define BATT_CANID_ACK0X15			0x15
typedef struct agv_parm
{
	int iCanSocketFd0;
	int iCanSocketFd1;
	int iTaskSocketFd;		// ����ͨ����socketFD
	int iCtrlSocketFd;		// ����ͨ����socketFD
	int iInfSocketFd;
	int iCfgSocketFd;
	int iUartFd;
	int iRs485Fd1;
	int iRs485Fd2;
    
	int  iMsgRecvServerPort;
	int  iMsgSendServerPort;
	int  iInfRecvServerPort;
	int  iInfSendServerPort;
	int  iCfgRecvServerPort;
	int  iCfgSendServerPort;
} agv_parm_t;

extern struct can_filter g_stCan0Filter[CAN0_FILTER_SIZE];
extern struct can_filter g_stCan1Filter[CAN1_FILTER_SIZE];

extern int init_can_socket(int iDevNum, int iMode, int iBitRate, void *pFilter, int iFilterSize);
extern int close_can_socket(int iSockFd);
extern int init_can(int iDevNum, int iMod); //  CAN0 or CAN1  BLOCK or NONBLOCK
extern int send_can(int iDevNum, struct can_frame *pCanFrame);
extern int recv_can(int iDevNum, struct can_frame *pCanFrame);
extern int close_can(int iDevNum);
extern int set_can_filter(int iDevNum, void *pfilter, int iSize);

#endif /*__CAN_H__*/