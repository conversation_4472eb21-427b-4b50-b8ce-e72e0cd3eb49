#include <thread>
#include <unistd.h>

#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"
#include "share/pb/idl/data_map.pb.h"

#include <spdlog/spdlog.h>
#include "containers.hpp"
#include "can.hpp"

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include "controller.hpp"

/*
1、从data_base读取地图，地图信息里有格口信息（多少个格口，格口号）
2、每个格口对应一个RFID模块，每个RFID模块对应一个CAN地址，
      同时包含三部分：RFID读写器、LED指示灯、按键
3、每次启动初始化时，需要读取所有RFID状态，有卡则报“绑框”无卡则报“解绑”。
4、LED灯接收wcs_agent指令，转发给模块控制
5、正常运行中，每一次按键后读框状态，有框则报绑框，无框报解绑。
6、对内部接口为：一个pub向外发送“绑框”/“解绑”消息，
一个sub接收LED 控制指令*/

int container_intf::send_single_box_state()  //发生绑框/解绑后扫描一次框的状态 并发送
{
#if 1

	box_info_multiple container_state;
	container_state.boxes_count = 1;
	container_state.boxes[0] = box_state;

	SPDLOG_DEBUG("No.{} container state changed", box_state.box_id);
	if (box_state.box_st == box_state_BIND)
	{
		controller::get_instance()->send_container_state(container_state);
		SPDLOG_DEBUG("exist box, now publish box_state, id:{}, st:{}, RFID{}", box_state.box_id, box_state.box_st, box_state.RFID);
	}
	else if (box_state.box_st == box_state_UNBIND)
	{
		controller::get_instance()->send_container_state(container_state);
		SPDLOG_DEBUG("no box, now publish box_state, id:{}, st:{}", box_state.box_id, box_state.box_st);
	}
	else if (box_state.box_st == box_state_ERROR)
	{
		SPDLOG_ERROR("No.{} container state is error", box_state.box_id);
	}
#else

	SPDLOG_DEBUG("No.{} container state changed", box_state.box_id);

	if (box_state.box_st == box_state_BIND)
	{
		SPDLOG_DEBUG("exist box, now publish box_state, id:{}, st:{}, RFID{}", box_state.box_id, box_state.box_st, box_state.RFID);
		led_info led_tmp;
		led_tmp.id = box_state.box_id;
		led_tmp.color = 2;
		led_tmp.flash_freq = 0;
		control_led(led_tmp);
	}
	else if (box_state.box_st == box_state_UNBIND)
	{
		SPDLOG_DEBUG("no box, now publish box_state, id:{}, st:{}", box_state.box_id, box_state.box_st);
		led_info led_tmp;
		led_tmp.id = box_state.box_id;
		led_tmp.color = 4;
		led_tmp.flash_freq = 0;
		control_led(led_tmp);
	}
	else if (box_state.box_st == box_state_ERROR)
	{
		SPDLOG_ERROR("No.{} container state is error", box_state.box_id);
		led_info led_tmp;
		led_tmp.id = box_state.box_id;
		led_tmp.color = 1;
		led_tmp.flash_freq = 0;
		control_led(led_tmp);
	}
#endif

	return 0;
}

int container_intf::control_query()
{
	can_frame can_send_msg;

	can_send_msg.can_id = can_id;
	can_send_msg.can_dlc = DLC;

	can_send_msg.data[0] = MASTER_REQUEST_READ_SLAVE_ID;
	can_send_msg.data[1] = 0;
	can_send_msg.data[2] = 0;
	can_send_msg.data[3] = 0;
	can_send_msg.data[4] = 0;
	can_send_msg.data[5] = 0;
	can_send_msg.data[6] = 0;
	can_send_msg.data[7] = 0;

#ifdef SURESORT_HORIZONTAL_VERSION
//	can_dev.can_to_net_send(&can_send_msg);
	int ret = can_dev.can_to_net_send(&can_send_msg);

#endif

#ifdef SURESORT_VERTICAL_VERSION
	int ret = can_dev.can_send(&can_send_msg);
#endif
	//SPDLOG_DEBUG("container CAN send ret = {}", ret);

	if (ret > 0)//修改为tcp发送
	{
		overtime.query_start_time = std::chrono::high_resolution_clock::now();
		overtime.wait_query_reply_num++;

		query_times++;
		reply_tms_tmp++;

		if (can_state == ERR)		//can发送成功，若有异常的话则恢复
		{
			SPDLOG_DEBUG("No.{} container CAN send error recover", box_state.box_id);
			controller::get_instance()->report_can_send_fail_recover(box_state.box_id);
			can_state = NORMAL;
		}

		return 1;
	}
	else
	{
		if (can_state == NORMAL)
		{
			SPDLOG_DEBUG("can send to container {} error", box_state.box_id);
			controller::get_instance()->report_can_send_fail_error(box_state.box_id);
			can_state = ERR;
			last_box_state.box_st = box_state_ERROR;
		}

		return 0;
	}
}

int container_intf::check_query_is_success(__u8 data[CAN_MAX_DLEN])
{
	if ((data[1] == 0) && (data[2] == 0) && (data[3] == 0) 
		&& (data[4] == 0) && (data[5] == 0) && (data[6] == 0) && (data[7] == 0))
	{
		return 0;
	}
	else
		return 1;   //success
}

int container_intf::update_box_state(__u8 data[CAN_MAX_DLEN])  //读卡后，模块更新格口状态
{
	uint64_t frame_rfid = 0;
	__u8 data_temp[CAN_MAX_DLEN - 1];

#if 0
	if ()
	{
		query_fail_times++; //总次数
		fail_query_tmp_times++;  //读卡失败1次或连续两次后，读到无卡或有卡，失败次数清零
		if (fail_query_tmp_times >= 3)
		{
			if (box_state.box_st != box_state_ERROR)
			{
				box_state.box_st = box_state_ERROR;
			}
			if (rfid_recognized_err != ERR)
			{
				rfid_recognized_err = ERR;
			}
			controller::get_instance()->report_rfid_unrecognized_error(box_state.box_id);
			SPDLOG_DEBUG("container {} query all zero {} times, set error", box_state.box_id, fail_query_tmp_times);
		}
		else
			SPDLOG_DEBUG("container {} query all zero {} times", box_state.box_id, fail_query_tmp_times);
	}
#endif
	if (((data[1] == 0xff) && (data[2] == 0xff) && (data[3] == 0xff) && (data[4] == 0xff)) ||
		((data[1] == 0) && (data[2] == 0) && (data[3] == 0) && (data[4] == 0)))
	{
		if (rfid_recognized_err == ERR)
		{
			rfid_recognized_err = NORMAL;
			SPDLOG_DEBUG("container {} rfid unrecognized error recover", box_state.box_id);
			controller::get_instance()->report_rfid_unrecognized_recover(box_state.box_id);
		}

		if (last_box_state.box_st == box_state_BIND && unbind_timer.caculate_timing == false)
		{
			unbind_timer.start();
			//unbind_timer.caculate_timing = true;
		}

		if (unbind_timer.caculate_timing == true && unbind_timer.execute_time() > setting::get_instance()->get_setting().unbind_interval)
		{
			box_state.box_st = box_state_UNBIND;
			strcpy(box_state.RFID, "0");
			fail_query_tmp_times = 0;
			unbind_timer.caculate_timing = false;
		}
	}
	else
	{
		if (rfid_recognized_err == ERR)
		{
			rfid_recognized_err = NORMAL;
			SPDLOG_DEBUG("container {} rfid unrecognized error recover", box_state.box_id);
			controller::get_instance()->report_rfid_unrecognized_recover(box_state.box_id);
		}

		box_state.box_st = box_state_BIND;
		unbind_timer.caculate_timing = false;

		for (int j = 0; j < 5; j++)
		{
			data_temp[j] = data[5 - j];
			frame_rfid = (frame_rfid *256 + int(data_temp[j]));
		}

		if (!setting::get_instance()->get_setting().has_rfid_limit ||
			(frame_rfid <= uint64_t(setting::get_instance()->get_setting().rfid_upper_limit) &&
			frame_rfid >= uint64_t(setting::get_instance()->get_setting().rfid_lower_limit)))
		{
			frame_rfid = frame_rfid + rfid_offset;
			char RFID_temp[16];
			sprintf(RFID_temp, rfid_format.c_str(), frame_rfid);
			sprintf(box_state.RFID, "%s%s", rfid_prefix.c_str(), RFID_temp);
			card_exist_times++;
			fail_query_tmp_times = 0;
		}
	}

	auto last_satr_state = saturation_state;
	if (data[6] != 0x00)
	{
		if ((data[7] & 0x0f) == 0x00)
			saturation_state = EMPTY;
		else if ((data[7] & 0x0f) == 0x01)
			saturation_state = FULL;
		if (saturation_state != last_satr_state)
		{
			if (saturation_state == EMPTY)
				controller::get_instance()->send_container_empty(box_state.box_id);
			else if (saturation_state == FULL)
				controller::get_instance()->send_container_full(box_state.box_id);
		}
	}

	if (setting::get_instance()->get_setting().version == "TJ_envelope")
	{
		//SPDLOG_DEBUG("query seal state: 0x{:x}", data[7]);
		update_seal_state(data[7]);
	}

	int tmp = 0;
	if ((last_box_state.box_st != box_state.box_st) || (last_box_state.box_id != box_state.box_id) || strcmp(last_box_state.RFID, box_state.RFID))
		tmp = 1;

	last_box_state = box_state;

	return tmp;
}

int container_intf::update_seal_state(uint8_t &data)
{
	//SPDLOG_DEBUG("seal state: No.{} container received", box_state.box_id, data[7]);
	container_seal_state seal_state_temp;
	uint8_t temp = seal_data;
	switch ((data & 0xf0))
	{
	case CONTAINER_SEAL_IDEL:
		seal_state_temp = container_seal_state_IDLE;
		temp = (data & 0xf0)>>4;
		break;
	case CONTAINER_SEAL_DONE:
	case CONTAINER_SEAL_FULL:
	case CONTAINER_SEAL_T_SEAL:
		seal_state_temp = container_seal_state_SEAL;
		temp = (data & 0xf0)>>4;
		break;
	case CONTAINER_SEAL_CONTAIN:
		seal_state_temp = container_seal_state_CONTAIN;
		temp = (data & 0xf0)>>4;
		break;

	default:
		temp = seal_data;
		SPDLOG_WARN("recv illegal seal data: 0x{:x}", data);
		break;
	}

	if (set_seal_state(seal_state_temp))
	{
		set_seal_data(temp);
		SPDLOG_DEBUG("container {} seal state changed to {} {} {}", box_state.box_id, seal_state,data,seal_data);
		controller::get_instance()->send_container_seal_state({box_state.box_id, seal_state});
	}else if(set_seal_data(temp))
	{
		SPDLOG_DEBUG("container {} seal state changed to {} {} {}", box_state.box_id, seal_state,data,seal_data);
		controller::get_instance()->send_container_seal_state({box_state.box_id, seal_state});
	}
	return 0;
}

int container_intf::process_seal_state(uint8_t &data)
{
	//SPDLOG_DEBUG("seal state: No.{} container received", box_state.box_id, data[7]);
	container_seal_state seal_state_temp;
	uint8_t temp = seal_data;
	switch (data)
	{
	case CONTAINER_SEAL_IDEL_RECV:
		seal_state_temp = container_seal_state_IDLE;
		temp = data & 0x0f;
		break;
	case CONTAINER_SEAL_DONE_RECV:
	case CONTAINER_SEAL_FULL_RECV:
	case CONTAINER_SEAL_T_SEAL_RECV:
		seal_state_temp = container_seal_state_SEAL;
		temp = data & 0x0f;
		break;
	case CONTAINER_SEAL_CONTAIN_RECV:
		seal_state_temp = container_seal_state_CONTAIN;
		temp = data & 0x0f;
		break;

	default:
		SPDLOG_WARN("recv illegal seal data: 0x{:x}", data);
		break;
	}

	if (set_seal_state(seal_state_temp))
	{
		set_seal_data(temp);
		SPDLOG_DEBUG("container {} seal state changed to {} {} {}", box_state.box_id, seal_state,data,seal_data);
		controller::get_instance()->send_container_seal_state({box_state.box_id, seal_state});
	}
	else if(set_seal_data(temp))
	{
		SPDLOG_DEBUG("container {} seal state changed to {} {} {}", box_state.box_id, seal_state,data,seal_data);
		controller::get_instance()->send_container_seal_state({box_state.box_id, seal_state});
	}
	return 0;
}

int container_intf::control_led(led_info &st)  //接收service指令发送灯控指令
{
	can_frame can_send_msg;

	can_send_msg.can_id = can_id;
	can_send_msg.can_dlc = DLC;

	int freq = st.flash_freq;
	int led_color;

	if (setting::get_instance()->get_setting().version == "")
		led_color = st.color;
	else if (setting::get_instance()->get_setting().version == std::string("yulang"))
		led_color = 7;
	else if (setting::get_instance()->get_setting().version == std::string("msm"))
	{
		led_color = st.color;
		if (led_color == 2)
			led_color = 1;
		else if (led_color == 1)
			led_color = 2;
		else if(led_color == 5)
			led_color = 6;
		else if(led_color == 6)
			led_color = 5;
	}else if(setting::get_instance()->get_setting().version == std::string("america"))
	{
		led_color = st.color;
		if((led_color == 2)&&(saturation_state == FULL))	 //20240607 根据WCS需求增加绿灯时，设备自己检测满箱，满箱绿灯直接绿闪
		{
			freq = 4;  //增加灯闪
			SPDLOG_INFO("container No.{} send G+flash receive led color.{} slot.{} -----------", st.id,led_color,saturation_state);
		}	
	}else{
		led_color = st.color;
	}

	can_send_msg.data[0] = MASTER_REQUEST_CONTROL_SLAVE_LED;
	can_send_msg.data[1] = led_color;
	can_send_msg.data[2] = freq;
	can_send_msg.data[3] = 0;
	can_send_msg.data[4] = 0;
	can_send_msg.data[5] = 0;
	can_send_msg.data[6] = 0;
	can_send_msg.data[7] = 0;

	overtime.ledcontrol_start_time = std::chrono::high_resolution_clock::now();
#ifdef SURESORT_HORIZONTAL_VERSION
	int ret = can_dev.can_to_net_send(&can_send_msg);
#endif

#ifdef SURESORT_VERTICAL_VERSION
	int ret = can_dev.can_send(&can_send_msg);
#endif

	if (ret > 0)//修改为tcp发送
	{
		reply_tms_tmp++;
		//overtime.wait_led_reply_num++;
		ledcontroll_times++;
		last_led_state = st;

		if (can_state == ERR)		//can发送成功，若有异常的话则恢复
		{
			SPDLOG_DEBUG("No.{} container CAN send error recover", box_state.box_id);
			controller::get_instance()->report_can_send_fail_recover(box_state.box_id);
			can_state = NORMAL;
		}

		return 1;
	}
	else
	{
		if (can_state == NORMAL)
		{
			SPDLOG_DEBUG("can send to container {} error", box_state.box_id);
			controller::get_instance()->report_can_send_fail_error(box_state.box_id);
			can_state = ERR;
			last_box_state.box_st = box_state_ERROR;
		}
		return 0;
	}

	return 0;
}

int container_intf::control_contain(container_seal_state state)
{
	if (set_seal_state(state))
		SPDLOG_DEBUG("container {} send thingtalk seal state changed to {}", box_state.box_id, state);
	can_frame can_send_msg;
	
	can_send_msg.can_id = can_id;
	can_send_msg.can_dlc = DLC;

	can_send_msg.data[0] = MASTER_REQUEST_CONTROL_SLAVE_CONTAIN;
	can_send_msg.data[1] = state;
	can_send_msg.data[2] = 0;
	can_send_msg.data[3] = 0;
	can_send_msg.data[4] = 0;
	can_send_msg.data[5] = 0;
	can_send_msg.data[6] = 0;
	can_send_msg.data[7] = 0;
    SPDLOG_DEBUG("container {} send thingtalk seal state changed to {}", box_state.box_id, state);
	int ret = can_dev.can_to_net_send(&can_send_msg);
	if (ret > 0)//修改为tcp发送
	{
		return 1;
	}
	else
	{
		return 0;
	}
}

int container_intf::listen_recv_msg(__u8 data[CAN_MAX_DLEN])
{
	//SPDLOG_DEBUG("data[0] = 0x{:x}", data[0]);
	switch (data[0])
	{
	case CONTAINER_FULL:
		SPDLOG_DEBUG("No.{} container received container full", box_state.box_id);
		controller::get_instance()->send_container_full(box_state.box_id);
		saturation_state = FULL;
		overtime.overtm_ledcontrol_tmp = 0;
		overtime.overtm_query_tmp = 0;
		break;

	case RASTER_TRIGGER:				//接收到后开启计时，controller中计算是否满箱
		SPDLOG_DEBUG("No.{} container received raster trigger", box_state.box_id);
		if (raster_valid_timer.caculate_timing)
		{
			if (raster_valid_timer.execute_time() < RASTER_CHECK_VALID_TIMER)
			{
				SPDLOG_DEBUG("No. {} container receive raster trigger too fast", box_state.box_id);
				break;
			}
			else
				raster_valid_timer.caculate_timing = false;
		}
		else
			raster_valid_timer.start();
		controller::get_instance()->send_container_raster_trigger(box_state.box_id);
		saturation_state = RASTER_TRIGGERING;
		raster_trigger_timer.start();
		overtime.overtm_ledcontrol_tmp = 0;
		overtime.overtm_query_tmp = 0;
		break;

	case CONTAINER_EMPTY:
		SPDLOG_DEBUG("No.{} container received container empty", box_state.box_id);
		saturation_state = EMPTY;
		controller::get_instance()->send_container_empty(box_state.box_id);

		break;

	case SLAVE_REPLY_LED_COLOR:
		SPDLOG_DEBUG("No.{} container received ledcontrol reply", box_state.box_id);
		//overtime.wait_led_reply_num--;
		ledcontrol_reply_times++;
		overtime.overtm_ledcontrol_tmp = 0;
		//controller::get_instance()->led_control_list_del(box_state.box_id);
		//SPDLOG_DEBUG("No.{} container deletfrom ledcontrol list", box_state.box_id);
		ledcontrol_reply_time_parse();
		break;

	case SLAVE_REPLY_QUERY:
		overtime.wait_query_reply_num--;
		query_reply_times++;
		overtime.overtm_query_tmp = 0;
		init_finish = true;
		if (update_box_state(data) && controller::get_instance()->is_sys_init_done())
		{
			send_single_box_state();
		}
		query_reply_time_parse();
		break;

	case SLAVE_SEAL_STATE:
//		SPDLOG_DEBUG("recv seal state: 0x{:x}", data[7]);
		process_seal_state(data[1]);
		break;

	case SLAVE_SEAL_CMD_REPLY:
		SPDLOG_DEBUG("No.{} container received seal cmd reply", box_state.box_id);
		break;

	case SLAVE_REPLY_ERROR:
		SPDLOG_DEBUG("container {} recv error, data1:{}, data2:{}, data3:{}, data4:{}, data5:{}, data6:{}, data7:{}", box_state.box_id, data[1], data[2], data[3], data[4], data[5], data[6], data[7]);
		break;

	case SLAVE_REPLY_REQUERY_TIMES_SET:
		if (data[1] == 1)
			SPDLOG_DEBUG("container {} set erquery times success", box_state.box_id);
		else
			SPDLOG_DEBUG("container {} set erquery times failed", box_state.box_id);
		break;

	case SLAVE_REPLY_BAUDRATE_SET:
		if (data[1] == 1)
			SPDLOG_DEBUG("container {} set baudrate success", box_state.box_id);
		else
			SPDLOG_DEBUG("container {} set baudrate failed", box_state.box_id);
		break;

	default:
		SPDLOG_DEBUG("container {} recv wrong msg: data: 0x{:x}, 0x{:x}, 0x{:x}, 0x{:x}, 0x{:x}, 0x{:x}, 0x{:x}, 0x{:x}",
			box_state.box_id, data[0], data[1], data[2], data[3], data[4], data[5], data[6], data[7]);
		break;
	}

	if (on_raster_trigger_timer())
	{
		SPDLOG_DEBUG("on raster timer, container {} full", box_state.box_id);
		controller::get_instance()->send_container_full(box_state.box_id);
	}

	return 0;
}

int container_intf::is_query_overtime(int64_t tm)
{
	if ((tm > 1000) && (overtime.wait_query_reply_num > 0))
	{
		overtime.overtm_times++;

		if (overtime.overtm_query_tmp > 0)
		{
			if (overtime.overtime_interval() > 5000)  //如果两次超时的间隔时间长，认为是偶发事件
			{
				overtime.overtm_query_tmp = 0;
			}
			else
			{
				overtime.overtm_query_tmp++;
			}
		}
		else
		{
			overtime.overtm_query_tmp++;
			overtime.overtime_start = std::chrono::high_resolution_clock::now();
		}

		SPDLOG_DEBUG("container {} over {}ms no query reply", box_state.box_id, tm);

		if (overtime.overtm_query_tmp >= 3)   //三次超时
		{
			//box_state.box_st = box_state_ERROR;
			//send_single_box_state();
			SPDLOG_DEBUG("container {} set error because of query overtime", box_state.box_id);
			overtime.overtm_query_tmp = 0;

			return 1;		//判断为超时
		}
	}
	else
	{
		return 0;		//未超时
	}

	return 0;
}

int container_intf::is_ledcontrol_overtime(int64_t tm)
{
	if (tm > 1000)
	{
		overtime.overtm_times++;

		if (overtime.overtm_ledcontrol_tmp > 0)
		{
			if (overtime.overtime_interval() > 5000)  //如果两次超时的间隔时间长，认为是偶发事件
			{
				overtime.overtm_ledcontrol_tmp = 0;
			}
			else
			{
				overtime.overtm_ledcontrol_tmp++;
			}
		}
		else
		{
			overtime.overtm_ledcontrol_tmp++;
			overtime.overtime_start = std::chrono::high_resolution_clock::now();
		}

		SPDLOG_DEBUG("container {} over {}ms no ledcontrol reply", box_state.box_id, tm);

	//	control_led(last_led_state);
		if (overtime.overtm_ledcontrol_tmp >= 3)   //三次超时
		{
			//box_state.box_st = box_state_ERROR;
			//send_single_box_state();
			SPDLOG_DEBUG("container {} set error because of ledcontrol overtime", box_state.box_id);
			overtime.overtm_ledcontrol_tmp = 0;

			return 1;		//判断为超时
		}
	}
	else
	{
		return 0;		//未超时
	}

	return 0;
}

int container_intf::ledcontrol_reply_time_parse()
{
	int64_t ledcontrol_reply_time = overtime.ledcontrol_execution_time();

	if (is_ledcontrol_overtime(ledcontrol_reply_time))
	{
		if (rfid_access_err == NORMAL)
		{
			rfid_access_err = ERR;
			controller::get_instance()->report_rfid_access_fail_error(box_state.box_id);
			SPDLOG_ERROR("container {} ledcontrol overtime, report rfid access err", box_state.box_id);
			last_box_state.box_st = box_state_ERROR;
		}
	}
	else
	{
		if (rfid_access_err == ERR)
		{
			rfid_access_err = NORMAL;
			controller::get_instance()->report_rfid_access_fail_recover(box_state.box_id);
			SPDLOG_ERROR("container {} ledcontrol on time, report rfid access err recover", box_state.box_id);
		}
	}

	return 0;
}

int container_intf::query_reply_time_parse()
{
	int64_t query_reply_time = overtime.query_execution_time();

	if (is_query_overtime(query_reply_time))
	{
		if (rfid_access_err == NORMAL)
		{
			rfid_access_err = ERR;
			controller::get_instance()->report_rfid_access_fail_error(box_state.box_id);
			SPDLOG_ERROR("container {} query overtime, report rfid access err", box_state.box_id);
			last_box_state.box_st = box_state_ERROR;
		}
	}
	else
	{
		if (rfid_access_err == ERR)
		{
			rfid_access_err = NORMAL;
			controller::get_instance()->report_rfid_access_fail_recover(box_state.box_id);
			SPDLOG_ERROR("container {} query on time, report rfid access err recover", box_state.box_id);
		}
	}

	sum_reply_time += query_reply_time;

	if (query_reply_time < min_reply_time)
		min_reply_time = query_reply_time;
	
	if (query_reply_time > max_reply_time)
		max_reply_time = query_reply_time;

	if (query_reply_time < 30)
	{
		_0_30_reply_times++;
	}
	else if (query_reply_time >= 30 && query_reply_time < 40)
	{
		_30_40_reply_times++;
	}
	else if (query_reply_time >= 40 && query_reply_time < 50)
	{
		_40_50_reply_times++;
	}
	else if (query_reply_time >= 50 && query_reply_time < 60)
	{
		_50_60_reply_times++;
	}
	else if (query_reply_time >= 60)
	{
		_60_reply_times++;
	}

	return 0;
}

int container_intf::state_print()
{
	if (reply_tms_tmp >= 100)
	{
		reply_tms_tmp = 0;
		SPDLOG_INFO("-");
		SPDLOG_INFO("-----------No.{} container info-----------", box_state.box_id);
		SPDLOG_DEBUG("send led control {} times, receive reply {} times", ledcontroll_times, ledcontrol_reply_times);
		SPDLOG_DEBUG("query {} tms, receive {} tms, exist card {} tms, overtime {} tms, fail {} tms", query_times, query_reply_times, card_exist_times, overtime.overtm_times, query_fail_times);
	}

	return 0;
}

int container_intf::set_can_dev(can_interface &candev)
{
	can_dev = candev;
	return 0;
}

int container_intf::set_baudrate(int bdrate)
{
	can_frame can_send_msg;

	can_send_msg.can_id = can_id;
	can_send_msg.can_dlc = DLC;

	can_send_msg.data[0] = MASTER_REQUEST_CHANGE_SLAVE_BAUDRATE;
	can_send_msg.data[1] = bdrate;
	can_send_msg.data[2] = 0;
	can_send_msg.data[3] = 0;
	can_send_msg.data[4] = 0;
	can_send_msg.data[5] = 0;
	can_send_msg.data[6] = 0;
	can_send_msg.data[7] = 0;

	if (can_dev.can_send(&can_send_msg) > 0)
		return 1;
	else
	{
		SPDLOG_DEBUG("set baudrate to container {} failed", box_state.box_id);
		return 0;
	}
}

int container_intf::set_requery_tms(int times)
{
	can_frame can_send_msg;

	can_send_msg.can_id = can_id;
	can_send_msg.can_dlc = DLC;

	can_send_msg.data[0] = MASTER_REQUEST_CHANGE_SLAVE_REQUERY_TIMES;
	can_send_msg.data[1] = times;
	can_send_msg.data[2] = 0;
	can_send_msg.data[3] = 0;
	can_send_msg.data[4] = 0;
	can_send_msg.data[5] = 0;
	can_send_msg.data[6] = 0;
	can_send_msg.data[7] = 0;

	if (can_dev.can_send(&can_send_msg) > 0)
		return 1;
	else
	{
		SPDLOG_DEBUG("set requery times to container {} failed", box_state.box_id);
		return 0;
	}
}

int container_intf::fault_inquiry()
{
	can_frame can_send_msg;

	can_send_msg.can_id = can_id;
	can_send_msg.can_dlc = DLC;

	can_send_msg.data[0] = MASTER_REQUEST_INQUIRE_SLAVE_FAULT;
	can_send_msg.data[1] = 0;
	can_send_msg.data[2] = 0;
	can_send_msg.data[3] = 0;
	can_send_msg.data[4] = 0;
	can_send_msg.data[5] = 0;
	can_send_msg.data[6] = 0;
	can_send_msg.data[7] = 0;

	if (can_dev.can_send(&can_send_msg) > 0)
		return 1;
	else
	{
		SPDLOG_DEBUG("fault inquiry to container {} failed", box_state.box_id);
		return 0;
	}
}
