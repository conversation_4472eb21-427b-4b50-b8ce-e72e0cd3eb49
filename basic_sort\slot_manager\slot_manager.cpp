﻿
/**@file  	   device_manage.cpp
* @brief       本播墙调度系统的设备管理接口文件
* @details     NULL
* <AUTHOR>
* @date        2022-02-14
* @version     v1.0.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.1.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/06/25  <td>1.0.0    <td>lizhy     <td>初始版本	                   </tr>
* </table>
*
**********************************************************************************
*/

#include "slot_manager.hpp"


#include "share/nlohmann_json/json.hpp"


#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/sinks/vehicle_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>
#include <spdlog/logger.h>    
#include <spdlog/async.h>

//#include "./threadpool/blocking_queue.hpp"
#include "share/pb/idl/plane_slot.pb.h"


#include "share/global_def.h"

#include <zmq.h>
#include <cppzmq/zmq.hpp>

#include <sys/types.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <netdb.h>

#include <fcntl.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <errno.h>
#include <iostream>
#include <stdexcept>
#include <memory>
#include <mutex>          
#include <fstream>

using namespace std;



/**@brief  vehicle_list_map class构造函数
* @param[in]  NULL
* @return     NULL
*/
slot_manager::slot_manager(zmq::context_t &context)
:m_slot_state_sub(zmq::socket_t(context, ZMQ_SUB))
,m_seal_state_sub(zmq::socket_t(context, ZMQ_SUB))
{
	return ;
}


/**@brief  vehicle_list_map class析构函数
* @param[in]  NULL
* @return     NULL
*/
slot_manager::~slot_manager() 
{
	//m_vehicle_dev_list.clear();
	//m_vehicle_sock_list.clear();
}

void slot_manager::slot_manager_init(int add_id,string zmq_bind0,string zmq_bind1)
{

	if (add_id == 0)
	{
		
		m_slot_state_sub.connect(TOPIC_PLANE_SLOT_STATE);
		m_slot_state_sub.set(zmq::sockopt::subscribe, "");

		m_seal_state_sub.connect(TOPIC_CONTAINER_SEAL_STATE);
		m_seal_state_sub.set(zmq::sockopt::subscribe, "");
	}
	else
	{
		m_slot_state_sub.set(zmq::sockopt::tcp_keepalive, 1);
		m_slot_state_sub.set(zmq::sockopt::tcp_keepalive_idle, 30);
		m_slot_state_sub.set(zmq::sockopt::tcp_keepalive_cnt, 5);
		m_slot_state_sub.set(zmq::sockopt::tcp_keepalive_intvl, 6);

		m_slot_state_sub.connect(zmq_bind0);
		m_slot_state_sub.set(zmq::sockopt::subscribe, "");

		m_seal_state_sub.set(zmq::sockopt::tcp_keepalive, 1);
		m_seal_state_sub.set(zmq::sockopt::tcp_keepalive_idle, 30);
		m_seal_state_sub.set(zmq::sockopt::tcp_keepalive_cnt, 5);
		m_seal_state_sub.set(zmq::sockopt::tcp_keepalive_intvl, 6);

		m_seal_state_sub.connect(zmq_bind1);
		m_seal_state_sub.set(zmq::sockopt::subscribe, "");
	}
	m_add_id = add_id;

	create_container();
} 


void slot_manager::slot_manager_run()
{

	m_slot_state_thread = new std::thread(&slot_manager::slot_manager_update_slot_state, this);
	m_seal_state_thread = new std::thread(&slot_manager::slot_manager_update_seal_state, this);

} 


void slot_manager::slot_manager_update_slot_state()
{
	zmq::message_t msg;
	pb_istream_t stream_in;
	slot_state dev_state_temp;

	//create_container();

	while(1)
	{
		if(m_slot_state_sub.recv(msg, zmq::recv_flags::none))
		{
		
			stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());

			if (!pb_decode(&stream_in, slot_state_fields, &dev_state_temp))
			{

				SPDLOG_INFO("[ZMQ] slot_manager_get_slot_state msg pb decode fail ");
				continue;

			}
			else
			{
				m_slot_state[dev_state_temp.id] = dev_state_temp.st;
				SPDLOG_INFO("[ZMQ] slot_manager_get_slot_state msg get slot :{} info :{} ", dev_state_temp.id, dev_state_temp.st);
			}
	
		}
		
		
		//std::this_thread::sleep_for(std::chrono::milliseconds(1));

	}

	
} 


bool slot_manager::slot_manager_check_slot_state()
{	
	bool result = false;

	
	for(auto kv:m_slot_state)
	{
		//SPDLOG_INFO("slot_manager_check_slot_state :{} :{}", kv.first, kv.second);
		if( state_FULL==kv.second )
		{
			SPDLOG_INFO("slot_manager_check_slot_state :{} :{}", kv.first, kv.second);
			result = true;
			break;
		}
		
	}

	return result;
	
} 



state slot_manager::slot_manager_get_slot_state(int slot_id)
{	
	state result = state_NORMAL;

	if(m_slot_state.find(slot_id)==m_slot_state.end())
	{
		result = state_NORMAL;
	}
	else
	{
		result=m_slot_state[slot_id];
	}

	return result;
		
} 



int slot_manager::create_container()
{
	zmq::context_t context{1};

	uint8_t req_msg[32];
	pb_ostream_t stream_out;
	data_request request;
	data_map_containers_info container_data;
	int ret = 0;

	zmq::socket_t socket{context, zmq::socket_type::req};
    socket.connect(SERVICE_DATA_ACCESS);
	strncpy(request.key, DATA_KEY_CONTAINER_INFO, sizeof(request.key));
	request.type = data_request_cmd_READ;
	stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
	if (!pb_encode(&stream_out, data_request_fields, &request))
	{
		SPDLOG_DEBUG("pb encode error: {}", stream_out.errmsg);
		return -1;
	}
	else
		socket.send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

	zmq::message_t reply;
	pb_istream_t stream_in;
	socket.recv(reply, zmq::recv_flags::none);
	stream_in = pb_istream_from_buffer((const uint8_t *)reply.data(), reply.size());
	if (!pb_decode(&stream_in, data_map_containers_info_fields, &container_data))
	{
		SPDLOG_DEBUG("pb decode error: {}", stream_in.errmsg);
		return -1;
	}
    for (int i = 0; i < container_data.container_count; i++)
	{
		container_data.container[i].id = container_data.container[i].id + m_add_id;
		m_slot_state.insert(std::make_pair(container_data.container[i].id, state_NORMAL));

		SPDLOG_DEBUG("add container id {} {}", container_data.container[i].id, m_slot_state[container_data.container[i].id]);
    }

	 for (int i = 0; i < container_data.container_count; i++)
	{
		//container_data.container[i].id = container_data.container[i].id + m_add_id;
		m_seal_state.insert(std::make_pair(container_data.container[i].id, container_seal_state_IDLE));

		SPDLOG_DEBUG("add container id {} {}", container_data.container[i].id, m_seal_state[container_data.container[i].id]);
    }

	socket.disconnect(SERVICE_DATA_ACCESS);

    SPDLOG_INFO("create {} container done---------------------------", m_slot_state.size());

	return ret;
}



void slot_manager::slot_manager_update_seal_state()
{
	zmq::message_t msg;
	pb_istream_t stream_in;
	container_seal_state_single dev_state_temp;

	//create_container();

	while(1)
	{
		if(m_seal_state_sub.recv(msg, zmq::recv_flags::none))
		{
		
			stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());

			if (!pb_decode(&stream_in, container_seal_state_single_fields, &dev_state_temp))
			{

				SPDLOG_INFO("[ZMQ] slot_manager_update_seal_state msg pb decode fail ");
				continue;

			}
			else
			{
				m_seal_state[dev_state_temp.container_id] = dev_state_temp.seal_state;
				SPDLOG_INFO("[ZMQ] slot_manager_update_seal_state msg get seal :{} state :{} ", dev_state_temp.container_id, dev_state_temp.seal_state);
			}
	
		}
		
		
		//std::this_thread::sleep_for(std::chrono::milliseconds(1));

	}

	
} 



container_seal_state slot_manager::slot_manager_get_seal_state(int seal_id)
{	
	container_seal_state result = container_seal_state_UNKNOWN;

	if(m_seal_state.find(seal_id)==m_seal_state.end())
	{
		//result = container_seal_state_UNKNOWN;
		result =container_seal_state_IDLE;
	}
	else
	{
		result=m_seal_state[seal_id];
	}

	return result;
		
} 