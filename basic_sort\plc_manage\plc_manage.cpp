﻿
/**@file  	   plc_manage.cpp
* @brief       PLC设备管理软件，实现PLC通信指令帧的生成和PLC设备的运行中管理
* @details     NULL
* <AUTHOR>
* @date        2021-07-14
* @version     v1.1.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.3.1
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/07/10  <td>1.0.0    <td>lizhy     <td>初始版本                         </tr>
* <tr><td>2021/07/14  <td>1.1.0    <td>lizhy     <td>
* -# 重新设计switch任务通信指令，将原有的单switch 号更改为支持多switch id 下发
* -# 重新设计switch任务队列，将open任务和close任务设计为2个队列，分别处理
* <tr><td>2021/08/09  <td>1.2.0    <td>lizhy     <td>
* -# 增加SPDLOG日志设计
* <tr><td>2021/08/16  <td>1.2.1    <td>lizhy     <td>
* -# 针对网络发送字段Value为空的问题，优化判断机制，字符串分割后检测得到的新vector长度，
* -# 若vector长度不满足要求，则返回默认错误
* <tr><td>2021/11/26  <td>1.3.1    <td>lizhy     <td>
* -# 添加部分指令,适配PLC通信协议(1.5.2)版本
* </table>
*
**********************************************************************************
*/





#include "../plc_agent_debug.h"

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>


#include "../protocol/plc_proto_preprocess.hpp"
#include "../protocol/plc_protocol.hpp"

#include "plc_manage.hpp"


#include "share/pb/idl/plane_switch_state.pb.h"
#include "share/pb/idl/plane_switch_action.pb.h"
#include "share/pb/idl/plane_slot.pb.h"
#include "share/pb/idl/feeder_goods.pb.h"
#include "share/pb/idl/feeder_cmd.pb.h"
#include "share/pb/idl/exception.pb.h"

#include "share/pb/idl/plane_hmi.pb.h"

#include "share/pb/idl/sys_state.pb.h"

#include <unistd.h>
#include <iostream>
#include <stdint.h>
#include <string.h>
#include <arpa/inet.h>

using namespace std;


/**@brief     解析PLC当前通信帧类型
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中MsgType字段
* @return     PLC_MSG_TYPE  通信帧类型
*/
PLC_MSG_TYPE plc_manage_get_msg_type(const string &str_in )
{
	vector<string> msg_type_vec;
	PLC_MSG_TYPE result = PLC_MSG_UNKNOWN;
	int i;
	
	string_split(str_in, &msg_type_vec, PLC_COMM_PROTOCOL_VALUE_SEPARATOE );
#ifdef PROTOCOL_DEBUG
	for(i=0; i< msg_type_vec.size(); i++)
	{
		cout << msg_type_vec[i] << endl;
	}
#endif

	if( !strcmp(msg_type_vec[0].c_str(), PLC_PROTOCOL_SESSION_MSG_TYPE) )
	{		
		if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_ACK)  )
		{
			result = PLC_MSG_ACK;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_REG)  )
		{
			result = PLC_MSG_REG;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_HEART_BEAT)  )
		{
			result = PLC_MSG_HEART_BEAT;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_ERR_CODE_REPORT)  )
		{
			result = PLC_MSG_ERR_CODE;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_BUTTON_STATE_REPORT)  )
		{
			result = PLC_MSG_BUTTON_STATUS;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_SWITCH_STATUS)  )
		{
			result = PLC_MSG_SWITCH_STATUS;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_SLOT_STATUS)  )
		{
			result = PLC_MSG_SLOT_STATUS;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_GOOD_INFO)  )
		{
			result = PLC_MSG_GOOD_INFO;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_GOOD_INFO_END)  )
		{
			result = PLC_MSG_GOOD_INFO_END;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_REBIN_STATUS)  )
		{
			result = PLC_MSG_REBIN_STATUS;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_CTRL_LED)  )
		{
			result = PLC_MSG_CTRL_LED;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_CTRL_SWITCH)  )
		{
			result = PLC_MSG_CTRL_SWITCH;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_CTRL_FEEDER)  )
		{
			result = PLC_MSG_CTRL_FEEDER;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_CTRL_MAIN_LED)  )
		{
			result = PLC_MSG_CTRL_MAIN_LED;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_DEV_MODE)  )
		{
			result = PLC_MSG_DEV_MODE;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_CLEAN_GOODS)  )
		{
			result = PLC_MSG_CLEAN_GOODS;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_SCHEDULER_STATUS_PUB)  )
		{
			result = PLC_MSG_SCHEDU_STATUS;
		}
		else
		{
			result = PLC_MSG_UNKNOWN;
		}
	}
	else
	{
		result = PLC_MSG_UNKNOWN;
	}

	return result;

}




/**@brief     解析PLC当前通信设备ID
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中DeviceID字段
* @return     当前设备ID
*/
int plc_manage_get_msg_dev_id(const string &str_in )
{
	vector<string> msg_vec_temp;
	int dev_id_temp = 0x00;
	int i;
	
	string_split(str_in, &msg_vec_temp, PLC_COMM_PROTOCOL_VALUE_SEPARATOE);

#ifdef PROTOCOL_DEBUG
	for(i=0; i< msg_vec_temp.size(); i++)
	{
		cout << msg_vec_temp[i] << endl;
	}
#endif

	//判断分割后字符串数量是否满足要求
	if( !IS_PLC_DEV_PROTOCOL_VALUE_STRING_CNT_VALID(msg_vec_temp.size()) )
	{
		return 0x00;
	}
	

	if( !strcmp(msg_vec_temp[0].c_str(), PLC_PROTOCOL_SESSION_DEVICE_ID) )
	{
		//dev_id_temp = stoi( msg_vec_temp[1] );
		dev_id_temp = atoi( msg_vec_temp[1].c_str() );
#ifdef PROTOCOL_DEBUG
		cout <<"device id " << dev_id_temp << endl;
#endif
	}
	else
	{
		dev_id_temp = 0x00;
	}

	return dev_id_temp;

}



/**@brief     解析PLC当前通信帧序号
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中Sequence字段
* @return     当前通信帧序号
*/
int plc_manage_get_msg_sequence(const string &str_in )
{
	vector<string> msg_vec_temp;
	int dev_seq_temp = 0x00;
	int i;
	
	string_split(str_in, &msg_vec_temp, PLC_COMM_PROTOCOL_VALUE_SEPARATOE);

#ifdef PROTOCOL_DEBUG
	for(i=0; i< msg_vec_temp.size(); i++)
	{
		cout << msg_vec_temp[i] << endl;
	}
#endif

	//判断分割后字符串数量是否满足要求
	if( !IS_PLC_DEV_PROTOCOL_VALUE_STRING_CNT_VALID(msg_vec_temp.size()) )
	{
		return 0x00;
	}
	
	if( !strcmp(msg_vec_temp[0].c_str(), PLC_PROTOCOL_SESSION_SEQUENCE) )
	{		
		dev_seq_temp = atoi( msg_vec_temp[1].c_str() );
#ifdef PROTOCOL_DEBUG
		cout <<"sequence " << dev_seq_temp << endl;
#endif
	}
	else
	{
		dev_seq_temp = 0x00;
	}

	return dev_seq_temp;

}




/**@brief     解析PLC当前设备状态(心跳报文)
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中Status字段
* @return     当前设备状态
*/
_PLC_DEV_STATUS plc_manage_get_msg_dev_status(const string &str_in )
{
	vector<string> msg_vec_temp;
	_PLC_DEV_STATUS dev_status_temp = PLC_DEV_STATUS_FAULT;
	int i;
	
	string_split(str_in, &msg_vec_temp, PLC_COMM_PROTOCOL_VALUE_SEPARATOE);

#ifdef PROTOCOL_DEBUG
	for(i=0; i< msg_vec_temp.size(); i++)
	{
		cout << msg_vec_temp[i] << endl;
	}
#endif

	//判断分割后字符串数量是否满足要求
	if( !IS_PLC_DEV_PROTOCOL_VALUE_STRING_CNT_VALID(msg_vec_temp.size()) )
	{
		return PLC_DEV_STATUS_FAULT;
	}
	

	if( !strcmp(msg_vec_temp[0].c_str(), PLC_PROTOCOL_SESSION_ERR_CODE_STATUS) )
	{
		//dev_status_temp = (_PLC_DEV_STATUS)( stoi(msg_vec_temp[1]) );
		dev_status_temp = (_PLC_DEV_STATUS)( atoi(msg_vec_temp[1].c_str()) );
#ifdef PROTOCOL_DEBUG
		cout <<"sequence " << dev_status_temp << endl;
#endif
	}
	else
	{
		dev_status_temp = PLC_DEV_STATUS_FAULT;
	}

	return dev_status_temp;

}

/**@brief     解析PLC当前上报故障状态
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中Status字段
* @return     当前设备故障状体
*/
int plc_manage_get_dev_err_status(const string &str_in )
{
	vector<string> msg_vec_temp;
	int dev_err_status_temp = 0x00;
	int i;
	
	string_split(str_in, &msg_vec_temp, PLC_COMM_PROTOCOL_VALUE_SEPARATOE);

#ifdef PROTOCOL_DEBUG
	for(i=0; i< msg_vec_temp.size(); i++)
	{
		cout << msg_vec_temp[i] << endl;
	}
#endif

	//判断分割后字符串数量是否满足要求
	if( !IS_PLC_DEV_PROTOCOL_VALUE_STRING_CNT_VALID(msg_vec_temp.size()) )
	{
		return 0x00;
	}
	

	if( !strcmp(msg_vec_temp[0].c_str(), PLC_PROTOCOL_SESSION_ERR_CODE_STATUS) )
	{
		//dev_code_temp = stoi( msg_vec_temp[1] );
		dev_err_status_temp = atoi( msg_vec_temp[1].c_str() );
#ifdef PROTOCOL_DEBUG
		cout <<"dev_code_temp " << dev_code_temp << endl;
#endif
	}
	else
	{
		dev_err_status_temp = 0x00;
	}

	return dev_err_status_temp;

}


/**@brief     解析PLC当前上报故障码
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中FaultCode字段
* @return     当前设备故障码
*/
int plc_manage_get_dev_fault_code(const string &str_in )
{
	vector<string> msg_vec_temp;
	int dev_code_temp = 0x00;
	int i;
	
	string_split(str_in, &msg_vec_temp, PLC_COMM_PROTOCOL_VALUE_SEPARATOE);

#ifdef PROTOCOL_DEBUG
	for(i=0; i< msg_vec_temp.size(); i++)
	{
		cout << msg_vec_temp[i] << endl;
	}
#endif

	//判断分割后字符串数量是否满足要求
	if( !IS_PLC_DEV_PROTOCOL_VALUE_STRING_CNT_VALID(msg_vec_temp.size()) )
	{
		return 0x00;
	}
	

	if( !strcmp(msg_vec_temp[0].c_str(), PLC_PROTOCOL_SESSION_ERR_CODE_FAULTCODE) )
	{
		//dev_code_temp = stoi( msg_vec_temp[1] );
		dev_code_temp = atoi( msg_vec_temp[1].c_str() );
#ifdef PROTOCOL_DEBUG
		cout <<"dev_code_temp " << dev_code_temp << endl;
#endif
	}
	else
	{
		dev_code_temp = 0x00;
	}

	return dev_code_temp;

}

/**@brief     解析PLC当前上报button状态
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中Status字段
* @return     当前设备按键状态
*/
int plc_manage_get_deb_button_status(const string &str_in )
{
	vector<string> msg_vec_temp;
	int dev_button_status = 0x00;
	int i;
	
	string_split(str_in, &msg_vec_temp, PLC_COMM_PROTOCOL_VALUE_SEPARATOE);

#ifdef PROTOCOL_DEBUG
	for(i=0; i< msg_vec_temp.size(); i++)
	{
		cout << msg_vec_temp[i] << endl;
	}
#endif

	//判断分割后字符串数量是否满足要求
	if( !IS_PLC_DEV_PROTOCOL_VALUE_STRING_CNT_VALID(msg_vec_temp.size()) )
	{
		return 0xff;
	}
	

	if( !strcmp(msg_vec_temp[0].c_str(), PLC_PROTOCOL_SESSION_BUTTON_STATUS) )
	{
		//dev_code_temp = stoi( msg_vec_temp[1] );
		dev_button_status = atoi( msg_vec_temp[1].c_str() );
#ifdef PROTOCOL_DEBUG
		cout <<"dev_code_temp " << dev_code_temp << endl;
#endif
	}
	else
	{
		dev_button_status = 0xff;
	}

	return dev_button_status;

}




/**@brief     解析PLC当前上报switch的状态
* @param[in]  string *str_in     ---  输入的字符串，对应PLC通信协议中Rail相关字段
* @param[out] uint32_t *dev_no   ---  解析得到的switch ID 数组
* @param[out] PLC_SWITCH_STAE *dev_state    ---  解析得到的switch 状态
* @return     当前switch ID 数组内有效数据个数
*/
int plc_manage_get_switch_state(string *str_in , uint32_t *dev_no, PLC_SWITCH_STAE *dev_state)
{
	vector<string> msg_vec_temp;
	vector<string> state_vec_temp;
	vector<string> no_vec_temp;
	int dev_no_cnt = 0x00;
	int dev_state_temp = 0x00;
	int i;

	//首先解析变轨器编号
	string_split(str_in[0], &msg_vec_temp, PLC_COMM_PROTOCOL_VALUE_SEPARATOE);

#ifdef PROTOCOL_DEBUG
	for(i=0; i< msg_vec_temp.size(); i++)
	{
		cout << msg_vec_temp[i] << endl;
	}
#endif

	//判断分割后字符串数量是否满足要求
	if( !IS_PLC_DEV_PROTOCOL_VALUE_STRING_CNT_VALID(msg_vec_temp.size()) )
	{
		return 0x00;
	}
	
	if( !strcmp(msg_vec_temp[0].c_str(), PLC_PROTOCOL_SESSION_RAIL_NO) )
	{
		string_split(msg_vec_temp[1], &no_vec_temp, PLC_COMM_PROTOCOL_DEV_SEPARATOE);

		
#ifdef PROTOCOL_DEBUG
		for(i=0; i< no_vec_temp.size(); i++)
		{
			cout << no_vec_temp[i] << endl;
		}
#endif
		for(i=0; i< no_vec_temp.size(); i++)
		{
			//dev_no[i] = stoi( no_vec_temp[i] );
			dev_no[i] = atoi( no_vec_temp[i].c_str() );
		}

		dev_no_cnt = no_vec_temp.size();

#ifdef PROTOCOL_DEBUG
		cout <<"dev_no_cnt " << dev_no_cnt << endl;
#endif
	}
	else
	{
		dev_no_cnt = 0x00;
	}

	string_split(str_in[1], &state_vec_temp, PLC_COMM_PROTOCOL_VALUE_SEPARATOE);
	
	//判断分割后字符串数量是否满足要求
	if( !IS_PLC_DEV_PROTOCOL_VALUE_STRING_CNT_VALID(state_vec_temp.size()) )
	{
		dev_state_temp = PLC_SWITCH_UNKNOWN;
		*dev_state = (PLC_SWITCH_STAE)(dev_state_temp);
		return 0x00;
	}
	

	if( !strcmp(state_vec_temp[0].c_str(), PLC_PROTOCOL_SESSION_RAIL_STATUS) )
	{
		//dev_state_temp = stoi( state_vec_temp[1] );
		dev_state_temp = atoi( state_vec_temp[1].c_str() );
#ifdef PROTOCOL_DEBUG
		cout <<"dev_state_temp " << dev_state_temp << endl;
#endif
	}
	else
	{
		dev_state_temp = PLC_SWITCH_UNKNOWN;
	}

	*dev_state = (PLC_SWITCH_STAE)(dev_state_temp);

	
	return dev_no_cnt;

}




/**@brief     解析PLC当前上报的异常格口号
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中StoreNO字段
* @return     当前异常格口号
*/
int plc_manage_get_slot_number(const string &str_in )
{
	vector<string> msg_vec_temp;
	int dev_code_temp = 0x00;
	int i;
	
	string_split(str_in, &msg_vec_temp, PLC_COMM_PROTOCOL_VALUE_SEPARATOE);

#ifdef PROTOCOL_DEBUG
	for(i=0; i< msg_vec_temp.size(); i++)
	{
		cout << msg_vec_temp[i] << endl;
	}
#endif
	cout << msg_vec_temp.size() << endl;

	//判断分割后字符串数量是否满足要求
	if( !IS_PLC_DEV_PROTOCOL_VALUE_STRING_CNT_VALID(msg_vec_temp.size()) )
	{
		return 0x00;
	}
	

	if( !strcmp(msg_vec_temp[0].c_str(), PLC_PROTOCOL_SESSION_STORE_NO) )
	{
		//dev_code_temp = stoi( msg_vec_temp[1] );
		dev_code_temp = atoi( msg_vec_temp[1].c_str() );
#ifdef PROTOCOL_DEBUG
		cout <<"dev_code_temp " << dev_code_temp << endl;
#endif
	}
	else
	{
		dev_code_temp = 0x00;
	}

	return dev_code_temp;

}


/**@brief     解析PLC当前上报的异常格口状态
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中StoreStatus字段
* @return     当前异常格口状态
*/
PLC_SLOT_STAE plc_manage_get_slot_status(const string &str_in )
{
	vector<string> msg_vec_temp;
	int dev_code_temp = 0x00;
	int i;
	
	string_split(str_in, &msg_vec_temp, PLC_COMM_PROTOCOL_VALUE_SEPARATOE);

#ifdef PROTOCOL_DEBUG
	for(i=0; i< msg_vec_temp.size(); i++)
	{
		cout << msg_vec_temp[i] << endl;
	}
#endif

	//判断分割后字符串数量是否满足要求
	if( !IS_PLC_DEV_PROTOCOL_VALUE_STRING_CNT_VALID(msg_vec_temp.size()) )
	{
		return PLC_SLOT_UNKNOWN;
	}
	

	if( !strcmp(msg_vec_temp[0].c_str(), PLC_PROTOCOL_SESSION_STORE_STATUS) )
	{
		//dev_code_temp = stoi( msg_vec_temp[1] );
		dev_code_temp = atoi( msg_vec_temp[1].c_str() );
#ifdef PROTOCOL_DEBUG
		cout <<"dev_code_temp " << dev_code_temp << endl;
#endif
	}
	else
	{
		dev_code_temp = 0x00;
	}

	return (PLC_SLOT_STAE)dev_code_temp;

}


/**@brief     解析PLC当前上报的扫码枪ID
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中ScanID字段
* @return     扫码枪ID 
*/
int plc_manage_get_good_scan_id(const string &str_in )
{
	vector<string> msg_vec_temp;
	int dev_code_temp = 0x00;
	int i;
	
	string_split(str_in, &msg_vec_temp, PLC_COMM_PROTOCOL_VALUE_SEPARATOE);

#ifdef PROTOCOL_DEBUG
	for(i=0; i< msg_vec_temp.size(); i++)
	{
		cout << msg_vec_temp[i] << endl;
	}
#endif

	//判断分割后字符串数量是否满足要求
	if( !IS_PLC_DEV_PROTOCOL_VALUE_STRING_CNT_VALID(msg_vec_temp.size()) )
	{
		return 0x00;
	}
	

	if( !strcmp(msg_vec_temp[0].c_str(), PLC_PROTOCOL_SESSION_SCAN_ID) )
	{
		//dev_code_temp = stoi( msg_vec_temp[1] );
		dev_code_temp = atoi( msg_vec_temp[1].c_str() );
#ifdef PROTOCOL_DEBUG
		cout <<"dev_code_temp " << dev_code_temp << endl;
#endif
	}
	else
	{
		dev_code_temp = 0x00;
	}

	return dev_code_temp;

}



/**@brief     解析PLC当前上报的商品扫码信息
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中SKU字段
* @return     商品扫码信息
*/
string plc_manage_get_good_sku_info(const string &str_in )
{
	vector<string> msg_vec_temp;
	string sku_info ;
	int i;
	
	string_split(str_in, &msg_vec_temp, PLC_COMM_PROTOCOL_VALUE_SEPARATOE);

	cout << msg_vec_temp.size() << endl;

#ifdef PROTOCOL_DEBUG
	for(i=0; i< msg_vec_temp.size(); i++)
	{
		cout << msg_vec_temp[i] << endl;
	}
#endif

	OUTPUT_LOG;


	//判断分割后字符串数量是否满足要求
	if( !IS_PLC_DEV_PROTOCOL_VALUE_STRING_CNT_VALID(msg_vec_temp.size()) )
	{
		OUTPUT_LOG;
		sku_info = {"  "};
		return sku_info;
	}
	

	if( !strcmp(msg_vec_temp[0].c_str(), PLC_PROTOCOL_SESSION_SKU) )
	{
		sku_info = msg_vec_temp[1];
#ifdef PROTOCOL_DEBUG
		cout <<"sku_info : " << sku_info << endl;
#endif
	}
	else
	{
		sku_info = {};
	}

	return sku_info;

}




/**@brief     解析PLC当前上报的商品位置
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中Location字段
* @return     商品位置
*/
PLC_GOOD_LOCATION plc_manage_get_good_location(const string &str_in )
{
	vector<string> msg_vec_temp;
	int dev_code_temp = 0x00;
	int i;
	
	string_split(str_in, &msg_vec_temp, PLC_COMM_PROTOCOL_VALUE_SEPARATOE);

#ifdef PROTOCOL_DEBUG
	for(i=0; i< msg_vec_temp.size(); i++)
	{
		cout << msg_vec_temp[i] << endl;
	}
#endif

	//判断分割后字符串数量是否满足要求
	if( !IS_PLC_DEV_PROTOCOL_VALUE_STRING_CNT_VALID(msg_vec_temp.size()) )
	{
		return PLC_G_LOCATION_UNKNOWN;
	}
	
	if( !strcmp(msg_vec_temp[0].c_str(), PLC_PROTOCOL_SESSION_LOCATION) )
	{
		//dev_code_temp = stoi( msg_vec_temp[1] );
		dev_code_temp = atoi( msg_vec_temp[1].c_str() );
#ifdef PROTOCOL_DEBUG
		cout <<"dev_code_temp " << dev_code_temp << endl;
#endif
	}
	else
	{
		dev_code_temp = 0x00;
	}

	return (PLC_GOOD_LOCATION)dev_code_temp;

}


/**@brief     解析PLC当前上报的Rebin车位置
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中RebinLocation字段
* @return     Rebin车位置
*/
int plc_manage_get_rebin_location(const string &str_in )
{
	vector<string> msg_vec_temp;
	int dev_code_temp = 0x00;
	int i;
	
	string_split(str_in, &msg_vec_temp, PLC_COMM_PROTOCOL_VALUE_SEPARATOE);

#ifdef PROTOCOL_DEBUG
	for(i=0; i< msg_vec_temp.size(); i++)
	{
		cout << msg_vec_temp[i] << endl;
	}
#endif

	//判断分割后字符串数量是否满足要求
	if( !IS_PLC_DEV_PROTOCOL_VALUE_STRING_CNT_VALID(msg_vec_temp.size()) )
	{
		return 0x00;
	}
	

	if( !strcmp(msg_vec_temp[0].c_str(), PLC_PROTOCOL_SESSION_REBIN_LOCATION) )
	{
		//dev_code_temp = stoi( msg_vec_temp[1] );
		dev_code_temp = atoi( msg_vec_temp[1].c_str() );
#ifdef PROTOCOL_DEBUG
		cout <<"dev_code_temp " << dev_code_temp << endl;
#endif
	}
	else
	{
		dev_code_temp = 0x00;
	}

	return dev_code_temp;

}



/**@brief     解析PLC当前上报的Rebin车状态
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中Status字段
* @return     Rebin车状态
*/
PLC_REBIN_STAE plc_manage_get_rebin_state(const string &str_in )
{
	vector<string> msg_vec_temp;
	int dev_code_temp = 0x00;
	int i;
	
	string_split(str_in, &msg_vec_temp, PLC_COMM_PROTOCOL_VALUE_SEPARATOE);

#ifdef PROTOCOL_DEBUG
	for(i=0; i< msg_vec_temp.size(); i++)
	{
		cout << msg_vec_temp[i] << endl;
	}
#endif

	//判断分割后字符串数量是否满足要求
	if( !IS_PLC_DEV_PROTOCOL_VALUE_STRING_CNT_VALID(msg_vec_temp.size()) )
	{
		return PLC_REBIN_UNKNOWN;
	}

	if( !strcmp(msg_vec_temp[0].c_str(), PLC_PROTOCOL_SESSION_REBIN_STATUS) )
	{
		//dev_code_temp = stoi( msg_vec_temp[1] );
		dev_code_temp = atoi( msg_vec_temp[1].c_str() );
#ifdef PROTOCOL_DEBUG
		cout <<"dev_code_temp " << dev_code_temp << endl;
#endif
	}
	else
	{
		dev_code_temp = 0x00;
	}

	return (PLC_REBIN_STAE)dev_code_temp;

}



/**@brief     解析PLC当前上报的ACK信息对应的MSG类型
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中AckMsg字段
* @return     MSG类型
*/
PLC_MSG_TYPE plc_manage_get_ack_msg_type(const string &str_in )
{
	vector<string> msg_type_vec;
	PLC_MSG_TYPE result = PLC_MSG_UNKNOWN;
	int i;
	
	string_split(str_in, &msg_type_vec, PLC_COMM_PROTOCOL_VALUE_SEPARATOE );
#ifdef PROTOCOL_DEBUG
	for(i=0; i< msg_type_vec.size(); i++)
	{
		cout << msg_type_vec[i] << endl;
	}
#endif

	//判断分割后字符串数量是否满足要求
	if( !IS_PLC_DEV_PROTOCOL_VALUE_STRING_CNT_VALID(msg_type_vec.size()) )
	{
		result = PLC_MSG_UNKNOWN;
		return result;
	}


	if( !strcmp(msg_type_vec[0].c_str(), PLC_PROTOCOL_SESSION_ACK_MSG_TYPE) )
	{		
		if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_ACK)  )
		{
			result = PLC_MSG_ACK;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_REG)  )
		{
			result = PLC_MSG_REG;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_HEART_BEAT)  )
		{
			result = PLC_MSG_HEART_BEAT;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_ERR_CODE_REPORT)  )
		{
			result = PLC_MSG_ERR_CODE;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_BUTTON_STATE_REPORT)  )
		{
			result = PLC_MSG_BUTTON_STATUS;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_SWITCH_STATUS)  )
		{
			result = PLC_MSG_SWITCH_STATUS;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_SLOT_STATUS)  )
		{
			result = PLC_MSG_SLOT_STATUS;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_GOOD_INFO)  )
		{
			result = PLC_MSG_GOOD_INFO;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_GOOD_INFO_END)  )
		{
			result = PLC_MSG_GOOD_INFO_END;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_REBIN_STATUS)  )
		{
			result = PLC_MSG_REBIN_STATUS;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_CTRL_LED)  )
		{
			result = PLC_MSG_CTRL_LED;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_CTRL_SWITCH)  )
		{
			result = PLC_MSG_CTRL_SWITCH;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_CTRL_FEEDER)  )
		{
			result = PLC_MSG_CTRL_FEEDER;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_CTRL_MAIN_LED)  )
		{
			result = PLC_MSG_CTRL_MAIN_LED;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_DEV_MODE)  )
		{
			result = PLC_MSG_DEV_MODE;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_CLEAN_GOODS)  )
		{
			result = PLC_MSG_CLEAN_GOODS;
		}
		else if( !strcmp(msg_type_vec[1].c_str(), PLC_PROTOCOL_VALUE_SCHEDULER_STATUS_PUB)  )
		{
			result = PLC_MSG_SCHEDU_STATUS;
		}
		else
		{
			result = PLC_MSG_UNKNOWN;
		}
	}
	else
	{
		result = PLC_MSG_UNKNOWN;
	}

	return result;

}



/**@brief     下发控制rebin塔灯指令
* @param[out]  PLC_COMM_DOWNLOAD_MSG *msg   ---  组帧生成的控制字符串结构体
* @param[in]  int seque                     ---  消息序列号
* @param[in]  int dev_id                    ---  设备ID
* @param[in]  int rebin_loca                ---  Rebin车所在位置
* @param[in]  PLC_REBIN_LED led_ctrl        ---  塔灯闪烁控制
* @return     NULL
*/
void plc_manage_ctrl_rebin_led(PLC_COMM_DOWNLOAD_MSG *msg, int seque, int dev_id,  int rebin_loca, PLC_REBIN_LED led_ctrl)
{

	msg->msg_type = PLC_PROTOCOL_VALUE_CTRL_LED;
	msg->sequence = int_to_string_format(seque);
	msg->dev_id = int_to_string_format_fixed_length(dev_id, 3);
	msg->down_payload_1 = int_to_string_format_fixed_length(rebin_loca, 2);	
	msg->down_payload_2 = int_to_string_format(led_ctrl);
}




/**@brief     下发控制变轨switch开关
* @param[out]  PLC_COMM_DOWNLOAD_MSG *msg   ---  组帧生成的控制字符串结构体
* @param[in]  int seque                     ---  消息序列号
* @param[in]  int dev_id                    ---  设备ID
* @param[in]  int *switch_no                ---  switch ID 数组
* @param[in]  int dev_cnt                   ---  switch ID 数组有效个数
* @param[in]  PLC_SWITCH_ACT switch_ctrl    ---  switch控制类型
* @return     NULL
*/
void plc_manage_ctrl_switch(PLC_COMM_DOWNLOAD_MSG *msg, int seque, int dev_id,  int *switch_no, int dev_cnt, PLC_SWITCH_ACT switch_ctrl)
{
	int i;
	
	msg->msg_type = PLC_PROTOCOL_VALUE_CTRL_SWITCH;
	msg->sequence = int_to_string_format(seque);
	msg->dev_id = int_to_string_format_fixed_length(dev_id, 3);
	msg->down_payload_1 = {};

	msg->down_payload_1 = int_to_string_format(switch_no[0]);

	for(i=1; i<dev_cnt; i++)
	{
		msg->down_payload_1 += (PLC_COMM_PROTOCOL_DEV_SEPARATOE + int_to_string_format(switch_no[i]));
	}
	
	msg->down_payload_2 = int_to_string_format(switch_ctrl);
}




/**@brief     下发控制供包台皮带动作
* @param[out]  PLC_COMM_DOWNLOAD_MSG *msg    ---  组帧生成的控制字符串结构体
* @param[in]  int seque                      ---  消息序列号
* @param[in]  int dev_id                     ---  设备ID
* @param[in]  PLC_FEEDER_POSITION feeder_pos ---  供包台皮带位置
* @param[in]  PLC_FEEDER_DIRE feeder_dire    ---  供包台皮带动作
* @return     NULL
*/
void plc_manage_ctrl_feeder(PLC_COMM_DOWNLOAD_MSG *msg, int seque, int dev_id,  PLC_FEEDER_POSITION feeder_pos, PLC_FEEDER_DIRE feeder_dire )
{

	msg->msg_type = PLC_PROTOCOL_VALUE_CTRL_FEEDER;
	msg->sequence = int_to_string_format(seque);
	msg->dev_id = int_to_string_format_fixed_length(dev_id, 3);
	msg->down_payload_1 = int_to_string_format(feeder_pos);	
	msg->down_payload_2 = int_to_string_format(feeder_dire);
}

#if 0
/**@brief     向PLC下发主控故障信息
* @param[out]  PLC_COMM_DOWNLOAD_MSG *msg    ---  组帧生成的控制字符串结构体
* @param[in]  int seque                      ---  消息序列号
* @param[in]  int dev_id                     ---  设备ID
* @param[in]  int err_code                   ---  主控故障码
* @return     NULL
*/
void plc_manage_ctrl_send_err(PLC_COMM_DOWNLOAD_MSG *msg, int seque, int dev_id, int err_code )
{

	msg->msg_type = PLC_PROTOCOL_VALUE_SCHEDULER_ERR;
	msg->sequence = int_to_string_format(seque);
	msg->dev_id = int_to_string_format_fixed_length(dev_id, 3);
	msg->down_payload_1 = int_to_string_format_fixed_length(err_code, 3);	
	msg->down_payload_2 = {};
}
#endif


/**@brief     控制PLC系统状态塔灯
* @param[out]  PLC_COMM_DOWNLOAD_MSG *msg    ---  组帧生成的控制字符串结构体
* @param[in]  int seque                      ---  消息序列号
* @param[in]  int dev_id                     ---  设备ID
* @param[in]  int main_ctrl                  ---  控制信号数据结构体
* @return     NULL
*/
void plc_manage_ctrl_main_led(PLC_COMM_DOWNLOAD_MSG *msg, int seque, int dev_id, PLC_MAIN_LED_CTRL main_ctrl )
{

	msg->msg_type = PLC_PROTOCOL_VALUE_CTRL_MAIN_LED;
	msg->sequence = int_to_string_format(seque);
	msg->dev_id = int_to_string_format_fixed_length(dev_id, 3);
	msg->down_payload_1 = int_to_string_format_fixed_length(main_ctrl.mask, 4);	
	msg->down_payload_2 = int_to_string_format_fixed_length(main_ctrl.cmd, 2);
	msg->down_payload_3 = int_to_string_format_fixed_length(main_ctrl.freq, 2);
	//msg->down_payload_2 = {};
}




/**@brief     向PLC下发主控故障信息
* @param[out]  PLC_COMM_DOWNLOAD_MSG *msg    ---  组帧生成的控制字符串结构体
* @param[in]  int seque                      ---  消息序列号
* @param[in]  int dev_id                     ---  设备ID
* @param[in]  PLC_DEV_MODE mode              ---  PLC工作模式
* @param[in]  int switch_type                ---  变轨逻辑控制来源
* @return     NULL
*/
void plc_manage_ctrl_send_dev_mode(PLC_COMM_DOWNLOAD_MSG *msg, int seque, int dev_id, PLC_DEV_MODE mode , int switch_type)
{

	msg->msg_type = PLC_PROTOCOL_VALUE_DEV_MODE;
	msg->sequence = int_to_string_format(seque);
	msg->dev_id = int_to_string_format_fixed_length(dev_id, 3);
	msg->down_payload_1 = int_to_string_format(mode);	
	msg->down_payload_2 = int_to_string_format(switch_type);
}

/**@brief     清除供包机已有商品
* @param[out]  PLC_COMM_DOWNLOAD_MSG *msg    ---  组帧生成的控制字符串结构体
* @param[in]  int seque                      ---  消息序列号
* @param[in]  int dev_id                     ---  设备ID
* @return     NULL
*/
void plc_manage_ctrl_clean_curr_goods(PLC_COMM_DOWNLOAD_MSG *msg, int seque, int dev_id)
{

	msg->msg_type = PLC_PROTOCOL_VALUE_CLEAN_GOODS;
	msg->sequence = int_to_string_format(seque);
	msg->dev_id = int_to_string_format_fixed_length(dev_id, 3);
	msg->down_payload_1 = int_to_string_format(1);	
}

/**@brief     向PLC发布系统状态
* @param[out]  PLC_COMM_DOWNLOAD_MSG *msg    ---  组帧生成的控制字符串结构体
* @param[in]  int seque                      ---  消息序列号
* @param[in]  int dev_id                     ---  设备ID
* @param[in]  int sys_status                 ---  调度系统状态
* @return     NULL
*/
void plc_manage_ctrl_scheduler_status_pub(PLC_COMM_DOWNLOAD_MSG *msg, int seque, int dev_id, int sys_status)
{

	msg->msg_type = PLC_PROTOCOL_VALUE_SCHEDULER_STATUS_PUB;
	msg->sequence = int_to_string_format(seque);
	msg->dev_id = int_to_string_format_fixed_length(dev_id, 3);
	msg->down_payload_1 = int_to_string_format(sys_status);	
}


/**@brief     生成PLC消息对应的ACK消息
* @param[out]  PLC_COMM_DOWNLOAD_MSG *msg    ---  组帧生成的控制字符串结构体
* @param[in]  int seque                      ---  消息序列号
* @param[in]  int dev_id                     ---  设备ID
* @param[in]  PLC_MSG_TYPE msg_type          ---  PLC输入的消息类型
* @return     NULL
*/
void plc_manage_generate_ack(PLC_COMM_DOWNLOAD_MSG *msg, int seque, int dev_id, PLC_MSG_TYPE msg_type)
{

	msg->msg_type = PLC_PROTOCOL_VALUE_ACK;
	msg->sequence = int_to_string_format(seque);
	msg->dev_id = int_to_string_format_fixed_length(dev_id, 3);

	switch( msg_type )
	{
		case PLC_MSG_REG:
			msg->down_payload_1 = PLC_PROTOCOL_VALUE_REG;	
			break;
		
		case PLC_MSG_HEART_BEAT:
			msg->down_payload_1 = PLC_PROTOCOL_VALUE_HEART_BEAT;	
			break;
		
		case PLC_MSG_ERR_CODE:
			msg->down_payload_1 = PLC_PROTOCOL_VALUE_ERR_CODE_REPORT;	
			break;
		
		case PLC_MSG_BUTTON_STATUS:
			msg->down_payload_1 = PLC_PROTOCOL_VALUE_BUTTON_STATE_REPORT;	
			break;

		case PLC_MSG_SWITCH_STATUS:
			msg->down_payload_1 = PLC_PROTOCOL_VALUE_SWITCH_STATUS;	
			break;
		
		case PLC_MSG_SLOT_STATUS:
			msg->down_payload_1 = PLC_PROTOCOL_VALUE_SLOT_STATUS;	
			break;
		
		case PLC_MSG_GOOD_INFO:
			msg->down_payload_1 = PLC_PROTOCOL_VALUE_GOOD_INFO;	
			break;

		case PLC_MSG_GOOD_INFO_END:
			msg->down_payload_1 = PLC_PROTOCOL_VALUE_GOOD_INFO_END;	
			break;
		
		case PLC_MSG_REBIN_STATUS:
			msg->down_payload_1 = PLC_PROTOCOL_VALUE_REBIN_STATUS;	
			break;
			
	}
	msg->down_payload_2 = {};
	msg->down_payload_3 = {};
	msg->time_stamp = {};
	
}


/**@brief     解析PLC按键触发事件
* @param[out]  PLC_COMM_DOWNLOAD_MSG *msg    ---  组帧生成的控制字符串结构体
* @param[in]  int seque                      ---  消息序列号
* @param[in]  int dev_id                     ---  设备ID
* @param[in]  PLC_MSG_TYPE msg_type          ---  PLC输入的消息类型
* @return     NULL
*/
bool plc_manage_button_state_analyse(PLC_BUTTON_STATE state, key_event *event)
{

	switch( state )
	{
		case PLC_BUTTON_INVERT_RELEASE:
			event->key_id = key_id_KEY_FEEDER_ROLLBACK;
			event->evt_type = key_evt_type_KEY_UP;
			break;
		
		case PLC_BUTTON_START_PUSH:
			event->key_id = key_id_KEY_START;
			event->evt_type = key_evt_type_KEY_PRESSED;	
			break;
		
		case PLC_BUTTON_STOP_PUSH:
			event->key_id = key_id_KEY_STOP;
			event->evt_type = key_evt_type_KEY_PRESSED;	
			break;
		
		case PLC_BUTTON_PAUSE_PUSH:
			event->key_id = key_id_KEY_PAUSE;
			event->evt_type = key_evt_type_KEY_PRESSED;	
			break;

		case PLC_BUTTON_RESET_PUSH:
			event->key_id = key_id_KEY_RESET;
			event->evt_type = key_evt_type_KEY_PRESSED;		
			break;
		
		case PLC_BUTTON_EMERGT_PUSH:
			event->key_id = key_id_KEY_EMERG;
			event->evt_type = key_evt_type_KEY_DOWN;
			break;
		
		case PLC_BUTTON_EMERGT_RELEASE:
			event->key_id = key_id_KEY_EMERG;
			event->evt_type = key_evt_type_KEY_UP;
			break;

		case PLC_BUTTON_SAFE_GATE_OPEN:
			event->key_id = key_id_KEY_SAFETY_DOOR;
			event->evt_type = key_evt_type_KEY_DOWN;	
			break;
		
		case PLC_BUTTON_SAFE_GATE_CLOSE:
			event->key_id = key_id_KEY_SAFETY_DOOR;
			event->evt_type = key_evt_type_KEY_UP;
			break;

		case PLC_BUTTON_INVERT_PUSH:
			event->key_id = key_id_KEY_FEEDER_ROLLBACK;
			event->evt_type = key_evt_type_KEY_DOWN;	
			break;	

		default:
			break;
			
	}
	
}

/**@brief  plc_dev_manager class构造函数
* @param[in]  NULL
* @return     NULL
*/
plc_dev_manager::plc_dev_manager() 
:m_net_comm_fd(-1)
,m_plc_dev_id(-1)
,m_plc_dev_comm_sequenct(1)
,m_is_previous_comm_finished(true)
,m_plc_dev_default_mode(1)
,m_plc_dev_up_sequence(0)
,m_plc_dev_last_err_state(false)
{
	return ;
}


/**@brief  plc_dev_manager class 析构函数
* @param[in]  NULL
* @return     NULL
*/
plc_dev_manager::~plc_dev_manager() 
{
}


/**@brief	  plc_dev_manager class 初始化函数
* @param[in]  int dev_mode	 --- PLC设备默认的控制变轨策略
* @return	  函数执行结果
* - true	  执行完成
*/
bool plc_dev_manager::plc_dev_manager_init(int dev_mode) 
{
	plc_dev_manager_previous_msg_done();
	m_plc_dev_comm_sequenct = 1;
	//m_plc_dev_comm_sequenct = 65500;
	m_plc_dev_default_mode = dev_mode;
	return true;
	
}


/**@brief     plc_dev_manager 运行函数，创建线程并运行
* @param[in]  NULL
* @return     函数执行结果
* - true      创建完成
*/
bool plc_dev_manager::plc_dev_manager_run(void) 
{
	plc_dev_comm_downlin = new std::thread(&plc_dev_manager::plc_dev_manager_ins_downlink_thread, this);
	return true;
	
}



/**@brief     plc_dev_manager 内部网络通信文件描述符的初始赋值函数，当前fd<0，说明网络建立通信，则通信指令无法下发
* @param[in]  int fd           ---  传入的网络通信描述符
* @return     函数执行结果
* - true      创建完成
*/
bool plc_dev_manager::plc_dev_manager_net_fd_init(int fd) 
{
	m_net_comm_fd = fd;

#ifdef PROTOCOL_DEBUG
	OUTPUT_LOG;
	cout <<"log:>> net fd  " << m_net_comm_fd << fd << endl;
#endif

	return true;
	
}

/**@brief     plc_dev_manager 内部网络通信文件描述符的初始赋值函数，当前fd<0，说明网络建立通信，则通信指令无法下发
* @param[in]  int fd           ---  传入的网络通信描述符
* @return     函数执行结果
* - true      创建完成
*/
void plc_dev_manager::plc_dev_manager_sequenct_monitor(void) 
{
	if(0==m_plc_dev_comm_sequenct)
	{
		m_plc_dev_comm_sequenct++;
	}
	
}




/**@brief     检测当前网络通信是否有效的判据
* @param[in]  NULL
* @return     函数执行结果
* - true      网络已建立，具备发送条件
* - false     网络通信尚未建立，不具备发送条件
*/
bool plc_dev_manager::plc_dev_manager_dev_id_valid(void)
{
	if( m_plc_dev_id<0 )
	{
		return false;
	}
	else
	{
		return true;
	}
}




/**@brief     通信完成后的标志清除
* @param[in]  NULL
* @return     NULL
*/
void plc_dev_manager::plc_dev_manager_previous_msg_done(void)
{
	m_is_previous_comm_finished = true;
	m_previous_comm_msg.msg_data = {};
	m_previous_comm_msg.msg_info.msg_dev_id = 0x00;
	m_previous_comm_msg.msg_info.msg_sequence = 0x00;
	m_previous_comm_msg.msg_info.msg_type = PLC_MSG_UNKNOWN;

	
#ifdef PLC_DEVICE_LOG
	SPDLOG_DEBUG("[DEVICE] plc_dev_manager_previous_msg_done");
#endif

}


/**@brief     PLC网络数据包的处理函数，网络接收数据并预处理后，即传入此处进行进一步行为级管理
* @param[in]  vector<string> &msg_vec             ---  预处理完成的字符串vector
* @return     NULL
*/
void plc_dev_manager::plc_dev_manager_net_msg_manage(vector<string> &msg_vec             ) 
{
	PLC_MSG_TYPE msg_type_temp;
	int sequence_temp;	
	int dev_id_temp;
	PLC_COMM_DOWNLOAD_MSG msg_down_temp;
	PLC_MSG_TYPE msg_ack_type_temp;
	uint32_t msg_id_temp;
	
	plc_dev_state_upload msg_upload_temp;
	switch_state_single switch_state_temp;
	int switch_cnt;
	uint32_t switch_no[PLC_DEV_SWITCH_MAX_NUM];
	PLC_SWITCH_STAE switch_state;
	int i;
	slot_state slot_state_temp;
	PLC_SLOT_STAE slot_state_data;
	goods_info goods_info_temp;
	string goods_info_data;

	_PLC_DEV_STATUS plc_dev_state;
	int plc_err_no;
	int plc_button_status;
	event_exception plc_dev_excep;
	key_event plc_button_event;
	
	struct timespec curr_tick;            		///< 网络通信的时间tick记录
	long time_diff;

	// 解析当前指令类型
	msg_type_temp = plc_manage_get_msg_type(msg_vec[0] );

#ifdef PLC_DEVICE_LOG
	SPDLOG_DEBUG("[DEVICE] plc_manage_get_msg_type :{}", msg_type_temp);
#endif

#ifdef PLC_MANAGE_DEBUG
	OUTPUT_LOG;
	cout <<"log:>> msg_type_temp  " << msg_type_temp << endl;
#endif

	//如果消息类型无法解析，直接返回不进行任何处理
	if( PLC_MSG_UNKNOWN==msg_type_temp )
	{

#ifdef PLC_DEVICE_LOG
		SPDLOG_DEBUG("[DEVICE] plc msg type unkown return");
#endif

		return;
	}

	//如果是ACK消息，查找当前消息，进行匹配处理
	if( PLC_MSG_ACK == msg_type_temp )
	{
		msg_id_temp = plc_manage_get_msg_dev_id(msg_vec[2]);
		sequence_temp = plc_manage_get_msg_sequence(msg_vec[1]);
		msg_ack_type_temp = plc_manage_get_ack_msg_type(msg_vec[3]);
		
#ifdef PLC_DEVICE_LOG
		SPDLOG_INFO("[DEVICE] plc msg type ACK, msg info :{} :{} :{}", msg_id_temp, sequence_temp, msg_ack_type_temp);
#endif

		if(   ( msg_id_temp==m_previous_comm_msg.msg_info.msg_dev_id )
			&&( sequence_temp==m_previous_comm_msg.msg_info.msg_sequence )
			&&( msg_ack_type_temp==m_previous_comm_msg.msg_info.msg_type ) )
		{
			plc_dev_manager_previous_msg_done();
			clock_gettime(CLOCK_MONOTONIC, &curr_tick);
			time_diff = 1000000*(curr_tick.tv_sec-m_last_msg_download_tick.tv_sec)+(curr_tick.tv_nsec-m_last_msg_download_tick.tv_nsec)/1000/1000;
			SPDLOG_INFO("[DEVICE] PLC Dev Msg Ack Time Delay :{} :{} :{}", msg_ack_type_temp, sequence_temp, time_diff);
		}
	
	}
	//如果是其他类型消息，生成ACK，并注入发送队列
	else
	{
		
		dev_id_temp = plc_manage_get_msg_dev_id(msg_vec[2]);
		sequence_temp = plc_manage_get_msg_sequence(msg_vec[1]);

#ifdef PLC_MANAGE_DEBUG
		cout <<"dev id " << dev_id_temp <<" sequence_temp	=  " << sequence_temp	<< endl;
#endif

#ifdef PLC_DEVICE_LOG
		SPDLOG_INFO("[DEVICE] plc msg type :{}, msg info :{} :{}", msg_type_temp, dev_id_temp, sequence_temp);
#endif

		//如果是注册消息，更新当前class的设备ID
		if( PLC_MSG_REG==msg_type_temp )
		{
			m_plc_dev_id = dev_id_temp;
			m_plc_dev_comm_sequenct = 1;
			///m_plc_dev_comm_sequenct = 65500;
			
#ifdef PLC_MANAGE_DEBUG
			cout <<"m_plc_dev_id	=  " << m_plc_dev_id	<< endl;
#endif
		}

		// 根据当前消息类型，生成对应的ACK并准备下发
		plc_manage_generate_ack(&msg_down_temp,sequence_temp,m_plc_dev_id,   msg_type_temp);
		plc_protocol_codec_ack(&msg_down_temp, msg_type_temp);

		// 记录准备下发的消息
		msg_full_info msg_temp;
		
		msg_temp.msg_data={};
		msg_temp.msg_data += plc_protocol_data_generate(&msg_down_temp);
		msg_temp.msg_info.msg_dev_id = m_plc_dev_id;
		msg_temp.msg_info.msg_sequence = sequence_temp;
		msg_temp.msg_info.msg_type = PLC_MSG_ACK;
		m_net_downlink_queue.push(msg_temp);

#ifdef PLC_MANAGE_DEBUG
		cout << msg_temp.msg_data << endl;
#endif		

#ifdef PLC_DEVICE_LOG
		SPDLOG_INFO("[DEVICE] plc msg :{}", msg_temp.msg_data);
#endif


		if( PLC_MSG_REG==msg_type_temp )
		{
			plc_dev_manager_set_plc_auto();
		}

		SPDLOG_INFO("[DEVICE] plc msg :{} :{}", sequence_temp, m_plc_dev_up_sequence);
		if(sequence_temp == m_plc_dev_up_sequence)
		{
			SPDLOG_INFO("[DEVICE] the same sequence quit");
			return;
		}
		else
		{
			m_plc_dev_up_sequence = sequence_temp;
		}
		SPDLOG_INFO("[DEVICE] plc msg continue");


		// 生成内部消息，控制上报
		msg_upload_temp.type = msg_type_temp;
		
		if( PLC_MSG_SWITCH_STATUS==msg_type_temp )
		{
			switch_cnt = plc_manage_get_switch_state(&msg_vec[3] , switch_no, &switch_state );

			if( PLC_DEV_SWITCH_MAX_NUM<switch_cnt )
			{
				switch_cnt = PLC_DEV_SWITCH_MAX_NUM;
			}
#ifdef PLC_MANAGE_DEBUG
			OUTPUT_LOG;
			cout <<"log:>> switch_cnt	" << switch_cnt << endl;
#endif			
			if( 0x00!=switch_cnt )
			{
				for(i=0; i<switch_cnt; i++)
				{
					switch_state_temp.switch_id = switch_no[i];
					switch_state_temp.position = (switch_position_def)(switch_state-1);
					switch_state_temp.state = switch_state_def_STATE_IDLE;
					switch_state_temp.err_code = 0x00;
					msg_upload_temp.len = sizeof(switch_state_temp);
					memcpy(msg_upload_temp.data, &switch_state_temp, sizeof(switch_state_temp));
					plc_dev_manager_dev_state_queue_push(msg_upload_temp);
#ifdef PLC_MANAGE_DEBUG
					OUTPUT_LOG;
					cout <<"log:>> switch_state_temp.switch_id  " << switch_state_temp.switch_id << endl;
					cout <<"log:>> switch_state_temp.position  " << switch_state_temp.position << endl;
					cout <<"log:>> switch_state_temp.state  " << switch_state_temp.state << endl;
					cout <<"log:>> switch_state_temp.err_code  " << switch_state_temp.err_code << endl;
#endif

#ifdef PLC_DEVICE_LOG
					SPDLOG_INFO("[DEVICE] plc switch upload id:{} / pos:{} / state:{} / err:{}", switch_state_temp.switch_id, switch_state_temp.position, switch_state_temp.state, switch_state_temp.err_code);
#endif

				}
			}
		}
		else if( PLC_MSG_SLOT_STATUS==msg_type_temp )
		{
			slot_state_temp.id = plc_manage_get_slot_number(msg_vec[3]);
			slot_state_data = plc_manage_get_slot_status(msg_vec[4]);
			switch( slot_state_data )
			{
				case PLC_SLOT_FULL:
					slot_state_temp.st = state_FULL;
					break;
				
				case PLC_SLOT_ABSENT:
					slot_state_temp.st = state_ABSENT;
					break;
				
				case PLC_SLOT_NORMAL:
					slot_state_temp.st = state_NORMAL;
					break;

				default:
					slot_state_temp.st = state_NORMAL;
					break;
					
			}
			
			msg_upload_temp.len = sizeof(slot_state_temp);
			memcpy(msg_upload_temp.data, &slot_state_temp, sizeof(slot_state_temp));
			plc_dev_manager_dev_state_queue_push(msg_upload_temp);
			
#ifdef PLC_MANAGE_DEBUG
			OUTPUT_LOG;
			cout <<"log:>> slot_state_temp.id	" << slot_state_temp.id << endl;
			cout <<"log:>> slot_state_temp.st  " << slot_state_temp.st << endl;
#endif

#ifdef PLC_DEVICE_LOG
			SPDLOG_INFO("[DEVICE] plc slot upload id:{} / state:{}", slot_state_temp.id, slot_state_temp.st);
#endif
			
		}
		else if( PLC_MSG_GOOD_INFO==msg_type_temp )
		{
			goods_info_temp.scanner_id = plc_manage_get_good_scan_id( msg_vec[3] );
			goods_info_temp.position = (goods_position)plc_manage_get_good_location( msg_vec[5] );
			goods_info_data = plc_manage_get_good_sku_info(msg_vec[4] );
			strcpy(goods_info_temp.codes, goods_info_data.c_str());
			goods_info_temp.codes[goods_info_data.length()] = 0x00;

			msg_upload_temp.len = sizeof(goods_info_temp);
			memcpy(msg_upload_temp.data, &goods_info_temp, sizeof(goods_info_temp));
			plc_dev_manager_dev_state_queue_push(msg_upload_temp);
#ifdef PLC_MANAGE_DEBUG
			OUTPUT_LOG;
			cout <<"log:>> goods_info_temp.scanner_id	" << goods_info_temp.scanner_id << endl;
			cout <<"log:>> goods_info_temp.position " << goods_info_temp.position << endl;
			cout <<"log:>> goods_info_temp.codes " << goods_info_temp.codes << endl;
#endif

#ifdef PLC_DEVICE_LOG
			SPDLOG_INFO("[DEVICE] plc goods info upload id:{} / state:{} / info:{}", goods_info_temp.scanner_id, goods_info_temp.position, goods_info_temp.codes);
#endif
			

		}
		else if( PLC_MSG_GOOD_INFO_END==msg_type_temp )
		{
			goods_info_temp.scanner_id = scanner_NONE;
			goods_info_temp.position = goods_position_LAST_BELT;//(goods_position)plc_manage_get_good_location( msg_vec[5] );
			goods_info_data = plc_manage_get_good_sku_info(msg_vec[3] );
			strcpy(goods_info_temp.codes, goods_info_data.c_str());
			goods_info_temp.codes[goods_info_data.length()] = 0x00;
			
			msg_upload_temp.len = sizeof(goods_info_temp);
			memcpy(msg_upload_temp.data, &goods_info_temp, sizeof(goods_info_temp));
			plc_dev_manager_dev_state_queue_push(msg_upload_temp);
#ifdef PLC_MANAGE_DEBUG
			OUTPUT_LOG;
			cout <<"log:>> goods_info_temp.scanner_id	" << goods_info_temp.scanner_id << endl;
			cout <<"log:>> goods_info_temp.position " << goods_info_temp.position << endl;
			cout <<"log:>> goods_info_temp.codes " << goods_info_temp.codes << endl;
#endif		

#ifdef PLC_DEVICE_LOG
			SPDLOG_INFO("[DEVICE] plc goods info(sku) upload id:{} / state:{} / info:{}", goods_info_temp.scanner_id, goods_info_temp.position, goods_info_temp.codes);
#endif

		}
		else if( PLC_MSG_ERR_CODE==msg_type_temp )
		{

			plc_dev_state = plc_manage_get_msg_dev_status(msg_vec[3] );
			plc_err_no = plc_manage_get_dev_fault_code(msg_vec[4]);

			plc_dev_excep.which_evt_except = 2;

			if( plc_err_no<100 )
			{
				plc_dev_excep.evt_except.except.src = exception_src_PLANE;
			}
			else if( (plc_err_no>100)&&(plc_err_no<120) )
			{
				plc_dev_excep.evt_except.except.src = exception_src_FEEDER;
			}
			else
			{
				plc_dev_excep.evt_except.except.src = exception_src_PLANE;
			}
			plc_dev_excep.evt_except.except.code = plc_err_no;
			plc_dev_excep.evt_except.except.sub_code = plc_err_no;
			plc_dev_excep.evt_except.except.dev = m_plc_dev_id ;
			plc_dev_excep.evt_except.except.level = exception_level_WARNNING ;
			//plc_dev_excep.description = 0x00;
			
			msg_upload_temp.len = sizeof(plc_dev_excep);
			memcpy(msg_upload_temp.data, &plc_dev_excep, sizeof(plc_dev_excep));
			plc_dev_manager_dev_state_queue_push(msg_upload_temp);
#ifdef PLC_MANAGE_DEBUG
			OUTPUT_LOG;
			cout <<"log:>> plc_dev_excep.src	" << plc_dev_excep.src << endl;
			cout <<"log:>> plc_dev_excep.code " << plc_dev_excep.code << endl;
			cout <<"log:>> plc_dev_excep.dev  " << plc_dev_excep.dev  << endl;
			cout <<"log:>> sizeof(plc_dev_excep).dev  " << sizeof(plc_dev_excep) << endl;
#endif		

#ifdef PLC_DEVICE_LOG
			SPDLOG_INFO("[DEVICE] plc dev status upload plc_dev_state:{} / plc_err_no:{} ", plc_dev_state, plc_err_no );
#endif


		}
		else if( PLC_MSG_BUTTON_STATUS==msg_type_temp )
		{
#if 1
			plc_button_status = plc_manage_get_deb_button_status(msg_vec[3] );

			plc_manage_button_state_analyse((PLC_BUTTON_STATE)(plc_button_status),  &plc_button_event);
			//msg_upload_temp.type = PLC_MSG_BUTTON_STATUS;
			msg_upload_temp.len = sizeof(plc_button_event);
			memcpy(msg_upload_temp.data, &plc_button_event, sizeof(plc_button_event));
			plc_dev_manager_dev_state_queue_push(msg_upload_temp);
#ifdef PLC_MANAGE_DEBUG
			OUTPUT_LOG;
			cout <<"log:>> plc_dev_excep.src	" << plc_dev_excep.src << endl;
			cout <<"log:>> plc_dev_excep.code " << plc_dev_excep.code << endl;
			cout <<"log:>> plc_dev_excep.dev  " << plc_dev_excep.dev  << endl;
			cout <<"log:>> sizeof(plc_dev_excep).dev  " << sizeof(plc_dev_excep) << endl;
#endif		

#ifdef PLC_DEVICE_LOG
			SPDLOG_INFO("[DEVICE] plc dev status upload button status:{}  ", plc_button_status);
#endif
#endif 

		}
		else if( PLC_MSG_REBIN_STATUS==msg_type_temp )
		{
			//TBD
		}

		
	}

	plc_dev_manager_update_comm_tick();

}



/**@brief     PLC指令消息下发管理线程运行函数
* @param[in]  NULL
* @return     NULL
*/
void plc_dev_manager::plc_dev_manager_ins_downlink_thread(void) 
{
	msg_full_info msg_down_temp;
	int downlink_retry_cnt = 0;

	while(1)
	{
	
		plc_dev_manager_sequenct_monitor();
		
		//判断当前是否具备消息下发条件
		if( m_net_comm_fd<0 )
		{
#ifdef PLC_MANAGE_DEBUG
			//OUTPUT_ERR;
			//cout <<"err:>> net fd invalid " << m_net_comm_fd << endl;
#endif
			//int pid = getpid();
			//OUTPUT_LOG;
			//std::cout << "pid>>: pid= "<<pid <<", lwp = "<< syscall(SYS_gettid) << "--"<< __FILE__ <<"--"<< __FUNCTION__<<"--" << __LINE__ << std::endl;
			
			std::this_thread::sleep_for(std::chrono::milliseconds(20));
			continue;
		}

		if( m_plc_dev_id<0 )
		{
#ifdef PLC_MANAGE_DEBUG
			//OUTPUT_ERR;
			//cout <<"err:>> m_plc_dev_id invalid " << m_plc_dev_id << endl;
#endif
			//int pid = getpid();
			//OUTPUT_LOG;
			//std::cout << "pid>>: pid= "<<pid <<", lwp = "<< syscall(SYS_gettid) << "--"<< __FILE__ <<"--"<< __FUNCTION__<<"--" << __LINE__ << std::endl;
			
			std::this_thread::sleep_for(std::chrono::milliseconds(20));
			continue;
		}
		
#ifdef PLC_MANAGE_DEBUG
		if( false==m_is_previous_comm_finished )
		{
			cout <<"log:>> m_is_previous_comm_finished	" << m_is_previous_comm_finished << endl;	
			cout <<"log:>> downlink_retry_cnt  " << downlink_retry_cnt << endl;
		}
#endif

		//检测之前的通信动作是否完成，若完成，开启新的通信动作，若未完成，继续尝试重发
 		if( true==m_is_previous_comm_finished )
		{
			
			if( !m_net_downlink_queue.empty() )
			{
				m_net_downlink_queue.pop(msg_down_temp);
#ifdef PLC_MANAGE_DEBUG
				OUTPUT_LOG;
				cout <<"log:>> m_net_downlink_queue.size  " << m_net_downlink_queue.size() << endl;
				cout <<"log:>> msg.size  " << msg_down_temp.msg_data << "  " << msg_down_temp.msg_data.length() << endl;
#endif
				
				//write(m_net_comm_fd, msg_down_temp.msg_data.c_str(), msg_down_temp.msg_data.length());	
				send(m_net_comm_fd, msg_down_temp.msg_data.c_str(), msg_down_temp.msg_data.length(), MSG_NOSIGNAL);	
				
				clock_gettime(CLOCK_MONOTONIC, &m_last_msg_download_tick);

				if( PLC_MSG_ACK != msg_down_temp.msg_info.msg_type )
				{
					m_previous_comm_msg = msg_down_temp;
					m_is_previous_comm_finished = false;					
				}
#ifdef PLC_NET_EVENT_LOG
				SPDLOG_INFO("[NET] [OUT] message :{}, len :{}, :{}", m_net_comm_fd, msg_down_temp.msg_data.length(), msg_down_temp.msg_data.c_str());
#endif

				downlink_retry_cnt = 0x00;

			}
			else
			{	
				//usleep(200000);
			}

		}
		else
		{
			//重新发送上次的消息
			//write(m_net_comm_fd, m_previous_comm_msg.msg_data.c_str(), m_previous_comm_msg.msg_data.length());			
			usleep(200000);

			send(m_net_comm_fd, m_previous_comm_msg.msg_data.c_str(), m_previous_comm_msg.msg_data.length(), MSG_NOSIGNAL); 

#ifdef PLC_NET_EVENT_LOG
			SPDLOG_INFO("[NET] [OUT_Retry:{}] message :{}, len :{}, :{}", downlink_retry_cnt, m_net_comm_fd, msg_down_temp.msg_data.length(), msg_down_temp.msg_data.c_str());
#endif
			downlink_retry_cnt++;

			if( downlink_retry_cnt>4 )
			{
				plc_dev_manager_previous_msg_done();
				downlink_retry_cnt = 0x00;
			}
			
			
			
		}


		//int pid = getpid();
		//std::cout << "pid>>: pid= "<<pid <<", lwp = "<< syscall(SYS_gettid) << "--"<< __FILE__ <<"--"<< __FUNCTION__<<"--" << __LINE__ << std::endl;

		//std::this_thread::sleep_for(std::chrono::milliseconds(5));
		std::this_thread::sleep_for(std::chrono::microseconds(50));
	}


}




/**@brief     获取当前PLC通信的时间tick
* @param[in]  NULL
* @return     NULL
*/
void plc_dev_manager::plc_dev_manager_update_comm_tick(void)
{
	//自动获取当前系统时间，并进行更新
	clock_gettime(CLOCK_MONOTONIC, &m_last_msg_upload_tick);
	
#ifdef PLC_HEART_BEAT_LOG
	SPDLOG_INFO("[HEART_BEAT] plc_dev_manager_update_comm_tick ");
#endif
}


/**@brief     获取当前PLC最近一次有效通信的时间信息
* @param[in]  NULL
* @return     最近一次通信的时间信息
*/
struct timespec plc_dev_manager::plc_dev_manager_get_last_comm_tick(void)
{
	//自动获取当前系统时间，并进行更新

#ifdef PLC_HEART_BEAT_LOG
	SPDLOG_INFO("[HEART_BEAT] plc_dev_manager_get_last_comm_tick :{}.{} ", m_last_msg_upload_tick.tv_sec, m_last_msg_upload_tick.tv_nsec);
#endif

	return m_last_msg_upload_tick;
	
}


/**@brief     blocking queue empty函数的二次封装，避免直接访问成员变量。用来查询待上报的设备状态任务队列的空满状态
* @param[in]  NULL
* @return     队列空满状态
* - true      blocking queue为空
* - false     blocking queue非空
*/
bool plc_dev_manager::plc_dev_manager_dev_state_queue_empty(void)
{
	return m_plc_dev_state_queue.empty();
}


/**@brief     blocking queue pop函数的二次封装，避免直接访问成员变量
* @param[in]  plc_dev_state_upload state_data --- 待操作的数据指针
* @return     NULL
*/
void plc_dev_manager::plc_dev_manager_dev_state_queue_push(plc_dev_state_upload state_data)
{
	m_plc_dev_state_queue.push(state_data);
}


/**@brief     blocking queue pop函数的二次封装，避免直接访问成员变量
* @param[in]  NULL
* @return     队首元素
*/
plc_dev_state_upload plc_dev_manager::plc_dev_manager_dev_state_queue_pop(void)
{
	plc_dev_state_upload data_temp;
	
	m_plc_dev_state_queue.pop(data_temp);

	return data_temp;
}




/**@brief     组帧生成对PLC的变轨器的实际控制指令，并准备下发
* @param[in]  int *action          ---  待操作的switch ID
* @param[in]  int cnt              ---  待操作的switch ID 数量
* @param[in]  PLC_SWITCH_ACT act   ---  待操作的switch ID 对应的操作 OPEN/CLOSE 
* @return     NULL 
*/
void plc_dev_manager::plc_dev_manager_get_switch_action(int *action, int cnt, PLC_SWITCH_ACT act)
{
	PLC_COMM_DOWNLOAD_MSG msg_down_temp;
	PLC_SWITCH_ACT switch_ctrl_temp;
	msg_full_info msg_temp;
	switch_state_single switch_state_temp;
	plc_dev_state_upload msg_upload_temp;
	int i;
	
	plc_dev_manager_sequenct_monitor();

	switch_ctrl_temp = act;
	
	plc_manage_ctrl_switch( &msg_down_temp, m_plc_dev_comm_sequenct, m_plc_dev_id,	action, cnt, switch_ctrl_temp);
	plc_protocol_codec_downlink( &msg_down_temp 		 , PLC_MSG_CTRL_SWITCH);
	
	msg_temp.msg_data={};
	
	msg_temp.msg_data += plc_protocol_data_generate(&msg_down_temp);
	msg_temp.msg_info.msg_dev_id = m_plc_dev_id;
	msg_temp.msg_info.msg_sequence = m_plc_dev_comm_sequenct;
	msg_temp.msg_info.msg_type = PLC_MSG_CTRL_SWITCH;

	m_net_downlink_queue.push(msg_temp);
	
#ifdef PLC_DEVICE_LOG
	SPDLOG_INFO("[DEVICE] switch m_net_downlink_queue.push id:{} / msg:{} ",m_plc_dev_id, msg_temp.msg_data );
#endif
	
	
	msg_upload_temp.type = PLC_MSG_SWITCH_STATUS;

	for(i=0; i<cnt; i++)
	{
		switch_state_temp.switch_id = action[i];
		switch_state_temp.position = switch_position_def_SWITCH_STATE_UNKNOWN;
		switch_state_temp.state = switch_state_def_STATE_RUNNING;
		switch_state_temp.err_code = 0x00;
		
		msg_upload_temp.len = sizeof(switch_state_temp);	
		memcpy(msg_upload_temp.data, &switch_state_temp, sizeof(switch_state_temp));
		plc_dev_manager_dev_state_queue_push(msg_upload_temp);

		if( switch_state_temp.switch_id<0 )
		{
			switch_state_temp.switch_id = action[i];

			if( PLC_SWITCH_ACT_OPEN==act )
			{
				switch_state_temp.position = switch_position_def_SWITCH_STATE_OPEN;
			}
			else if( PLC_SWITCH_ACT_CLOSE==act )
			{
				switch_state_temp.position = switch_position_def_SWITCH_STATE_CLOSE;
			}
			switch_state_temp.state = switch_state_def_STATE_IDLE;
			switch_state_temp.err_code = 0x00;
			
			msg_upload_temp.len = sizeof(switch_state_temp);	
			memcpy(msg_upload_temp.data, &switch_state_temp, sizeof(switch_state_temp));
			plc_dev_manager_dev_state_queue_push(msg_upload_temp);
		}
		
		
	}


	m_plc_dev_comm_sequenct++;

}


/**@brief     组帧生成对PLC供包台的实际控制指令，并准备下发
* @param[in]  feeder_cmd *action   ---  待执行的供包台操作
* @return     NULL 
*/
void plc_dev_manager::plc_dev_manager_get_feeder_action(feeder_cmd *action)
{
	PLC_COMM_DOWNLOAD_MSG msg_down_temp;

	PLC_FEEDER_POSITION feeder_pos_temp;
	PLC_FEEDER_DIRE feeder_dire_temp;

	msg_full_info msg_temp;

	plc_dev_manager_sequenct_monitor();
	
	if(action->feeder_id >= 2)
	{
		SPDLOG_INFO("[DEVICE] action.feeder_id err {}",action->feeder_id);
		action->feeder_id = 1;
	}
	switch( action->part )
	{
		case part_num_FIRST:
			feeder_pos_temp = PLC_FEEDER_FIRST_BELT;
			break;
		
		case part_num_SECOND:
			feeder_pos_temp = PLC_FEEDER_SECOND_BELT;
			break;
		
		case part_num_THIRD:
			feeder_pos_temp = PLC_FEEDER_THIRD_BELT;
			break;
		
		case part_num_FORTH:
			feeder_pos_temp = PLC_FEEDER_FOURTH_BELT;
			break;
		
		case part_num_LAST:
			feeder_pos_temp = PLC_FEEDER_LAST_BELT;
			break;
		
		case part_num_ALL:
			feeder_pos_temp = PLC_FEEDER_ALL_BELT;
			break;
		
		default:
			feeder_pos_temp = PLC_FEEDER_UNKNOWN;
			break;		
	}
	
	switch( action->act )
	{
		case action_ROLL_FORWARD:
			feeder_dire_temp = PLC_FEEDER_DIRE_FORWARD;
			break;
		
		case action_ROLL_BACKWARD:
			feeder_dire_temp = PLC_FEEDER_DIRE_BACKWARD;
			break;
		
		case action_STOP:
			feeder_dire_temp = PLC_FEEDER_DIRE_STOP;
			break;
		
		case action_CLEAR:
			feeder_dire_temp = PLC_FEEDER_CMD_CLEAR;
			break;
		
		default:
			feeder_dire_temp = PLC_FEEDER_DIRE_UNKNOWN;
			break;		
	}
	// 供包机控制指令 解析
	if(feeder_pos_temp == PLC_FEEDER_ALL_BELT)
	{
		stfeeder_ctrl[action->feeder_id].type = 1;
        // 9-5 组合供包指令
		if((stfeeder_ctrl[action->feeder_id].cmd != (uint8_t)action->act) && ((uint8_t)action->act == 5))
		SPDLOG_INFO("feeder id {},cmd action->part={},action->act={}",action->feeder_id,action->part,action->act);
	    
		stfeeder_ctrl[action->feeder_id].cmd  = (uint8_t)action->act;
		// stfeeder_ctrl[action->feeder_id].vehicle_id  = action.vehicle_id;
	}
	else
	{
		stfeeder_ctrl[action->feeder_id].type = 2;
		stfeeder_ctrl[action->feeder_id].part = feeder_pos_temp;
		stfeeder_ctrl[action->feeder_id].cmd  = (uint8_t)action->act;
		stfeeder_ctrl[action->feeder_id].belt_speed = action->belt_speed; 
		SPDLOG_INFO("feeder id {}, cmd action->part={},action->act={}",action->feeder_id,action->part,action->act);
	}
	#if 0
	if( action_CLEAR != action->act )
	{
		//plc_manage_ctrl_feeder(&msg_down_temp, m_plc_dev_comm_sequenct, m_plc_dev_id,	feeder_pos_temp, feeder_dire_temp );
		//plc_protocol_codec_downlink( &msg_down_temp , PLC_MSG_CTRL_FEEDER);ljl
	}
	else
	{
		//plc_manage_ctrl_clean_curr_goods(&msg_down_temp, m_plc_dev_comm_sequenct, m_plc_dev_id);
		//plc_protocol_codec_downlink(&msg_down_temp , PLC_MSG_CLEAN_GOODS);ljl
	}

	
	msg_temp.msg_data={};
	
	msg_temp.msg_data += plc_protocol_data_generate(&msg_down_temp);
	msg_temp.msg_info.msg_dev_id = m_plc_dev_id;
	msg_temp.msg_info.msg_sequence = m_plc_dev_comm_sequenct;
	msg_temp.msg_info.msg_type = PLC_MSG_CTRL_FEEDER;
	
	cout << msg_temp.msg_data << endl;
	#endif

#ifdef PLC_DEVICE_LOG
	//SPDLOG_INFO("[DEVICE] feeder m_net_downlink_queue.push id:{} / msg:{} ",m_plc_dev_id, msg_temp.msg_data );
#endif
	// m_net_downlink_queue.push(msg_temp); ljl

	m_plc_dev_comm_sequenct++;

}



/**@brief     控制PLC进入默认工作模式，需要在收到REG指令后自动下发
* @param[in]  NULL
* @return     NULL 
*/
void plc_dev_manager::plc_dev_manager_set_plc_auto(void)
{
	PLC_COMM_DOWNLOAD_MSG msg_down_temp;

	msg_full_info msg_temp;

	plc_manage_ctrl_send_dev_mode( &msg_down_temp, m_plc_dev_comm_sequenct, m_plc_dev_id,	PLC_DEV_MODE_AUTO, m_plc_dev_default_mode);
	plc_protocol_codec_downlink( &msg_down_temp 		 , PLC_MSG_DEV_MODE);
	
	msg_temp.msg_data={};
	
	msg_temp.msg_data += plc_protocol_data_generate(&msg_down_temp);
	msg_temp.msg_info.msg_dev_id = m_plc_dev_id;
	msg_temp.msg_info.msg_sequence = m_plc_dev_comm_sequenct;
	msg_temp.msg_info.msg_type = PLC_MSG_DEV_MODE;
		
	m_net_downlink_queue.push(msg_temp);

	m_plc_dev_comm_sequenct++;

}




/**@brief     组帧生成对PLC供包台的实际控制指令，并准备下发
* @param[in]  feeder_cmd *action   ---  待执行的供包台操作
* @return     NULL 
*/
void plc_dev_manager::plc_dev_manager_send_system_state(sys_mode_state dev_state)
{
	PLC_COMM_DOWNLOAD_MSG msg_down_temp;

	msg_full_info msg_temp;

	plc_dev_manager_sequenct_monitor();
	
	plc_manage_ctrl_scheduler_status_pub(&msg_down_temp, m_plc_dev_comm_sequenct, m_plc_dev_id,	dev_state.state+1);
	plc_protocol_codec_downlink( &msg_down_temp, PLC_MSG_SCHEDU_STATUS);
	
	msg_temp.msg_data={};
	
	msg_temp.msg_data += plc_protocol_data_generate(&msg_down_temp);
	msg_temp.msg_info.msg_dev_id = m_plc_dev_id;
	msg_temp.msg_info.msg_sequence = m_plc_dev_comm_sequenct;
	msg_temp.msg_info.msg_type = PLC_MSG_SCHEDU_STATUS;
	
	cout << msg_temp.msg_data << endl;

#ifdef PLC_DEVICE_LOG
	//SPDLOG_INFO("[DEVICE] feeder m_net_downlink_queue.push id:{} / msg:{} ",m_plc_dev_id, msg_temp.msg_data );
#endif
	m_net_downlink_queue.push(msg_temp);

	m_plc_dev_comm_sequenct++;

}



/**@brief     组帧生成对PLC供包台的实际控制指令，并准备下发
* @param[in]  feeder_cmd *action   ---  待执行的供包台操作
* @return     NULL 
*/
void plc_dev_manager::plc_dev_manager_clear_good_info(void)
{
	PLC_COMM_DOWNLOAD_MSG msg_down_temp;

	msg_full_info msg_temp;

	plc_dev_manager_sequenct_monitor();
	
	plc_manage_ctrl_clean_curr_goods(&msg_down_temp, m_plc_dev_comm_sequenct, m_plc_dev_id);
	plc_protocol_codec_downlink( &msg_down_temp, PLC_MSG_CLEAN_GOODS);
	
	msg_temp.msg_data={};
	
	msg_temp.msg_data += plc_protocol_data_generate(&msg_down_temp);
	msg_temp.msg_info.msg_dev_id = m_plc_dev_id;
	msg_temp.msg_info.msg_sequence = m_plc_dev_comm_sequenct;
	msg_temp.msg_info.msg_type = PLC_MSG_CLEAN_GOODS;
	
	cout << msg_temp.msg_data << endl;

#ifdef PLC_DEVICE_LOG
	SPDLOG_INFO("[DEVICE] feeder m_net_downlink_queue.push id:{} / msg:{} ",m_plc_dev_id, msg_temp.msg_data );
#endif
	m_net_downlink_queue.push(msg_temp);

	m_plc_dev_comm_sequenct++;

}




/**@brief     组帧生成对PLC供包台的实际控制指令，并准备下发
* @param[in]  feeder_cmd *action   ---  待执行的供包台操作
* @return     NULL 
*/
void plc_dev_manager::plc_dev_manager_send_main_led_cmd(led_cmd cmd)
{
	PLC_COMM_DOWNLOAD_MSG msg_down_temp;
	msg_full_info msg_temp;
	PLC_MAIN_LED_CTRL ctrl_temp;
	
	//ctrl_temp.mask = 1<<(cmd.color);
	ctrl_temp.mask = cmd.color ;
	//ctrl_temp.cmd = cmd.cmd;
	ctrl_temp.freq = cmd.param;
	if( led_cmd_type_ON==cmd.cmd )
	{
		ctrl_temp.cmd = 1;
	}
	else if( led_cmd_type_OFF==cmd.cmd )
	{
		ctrl_temp.cmd = 0;
	}
	else
	{
		ctrl_temp.cmd = cmd.cmd;
	}
  
	plc_dev_manager_sequenct_monitor();
		
	plc_manage_ctrl_main_led(&msg_down_temp, m_plc_dev_comm_sequenct, m_plc_dev_id,ctrl_temp);
	plc_protocol_codec_downlink( &msg_down_temp, PLC_MSG_CTRL_MAIN_LED);
	
	msg_temp.msg_data={};
	
	msg_temp.msg_data += plc_protocol_data_generate(&msg_down_temp);
	msg_temp.msg_info.msg_dev_id = m_plc_dev_id;
	msg_temp.msg_info.msg_sequence = m_plc_dev_comm_sequenct;
	msg_temp.msg_info.msg_type = PLC_MSG_CTRL_MAIN_LED;
	
	cout << msg_temp.msg_data << endl;

#ifdef PLC_DEVICE_LOG
	SPDLOG_INFO("[DEVICE] feeder m_net_downlink_queue.push id:{} / msg:{} ",m_plc_dev_id, msg_temp.msg_data );
#endif
	m_net_downlink_queue.push(msg_temp);

	m_plc_dev_comm_sequenct++;

}
