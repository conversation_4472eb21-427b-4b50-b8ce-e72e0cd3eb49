﻿
/**@file  	   plc_angent.hpp
* @brief       PLC设备管理软件的业务顶层代码
* @details     NULL
* <AUTHOR>
* @date        2021-07-15
* @version     v1.1.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.1.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/07/10  <td>1.0.0    <td>lizhy     <td>初始版本                         </tr>
* <tr><td>2021/07/15  <td>1.1.0    <td>lizhy     <td>
* -# 重新设计switch的open任务和close任务
* -# switch设备号为负数的设备任务更改还需要进一步设计
* <tr><td>2021/08/09  <td>1.2.0    <td>lizhy     <td>
* -# 添加SPDLOG 日志设计
* -# switch action 缓存队列thread周期修改为10ms
* </table>
*
**********************************************************************************
*/



#ifndef __PLC_AGENT_CONFIG_HPP__
#define __PLC_AGENT_CONFIG_HPP__

#include "share/nlohmann_json/json.hpp"


#include <spdlog/spdlog.h>
#include <spdlog/common.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#include <string>
#include <netinet/ip.h>

#include <iostream>

#include <vector>
#include <map>
#include <sys/epoll.h>
#include <functional>
#include <memory>




using namespace std;

#define PLC_AGENT_LOG_CONFIG_ON					("on")
#define PLC_AGENT_LOG_CONFIG_OFF				("off")


#define PLC_AGENT_LOG_LEVEL_TRACE				("trace")
#define PLC_AGENT_LOG_LEVEL_DEBUG				("debug")
#define PLC_AGENT_LOG_LEVEL_INFO				("info")
#define PLC_AGENT_LOG_LEVEL_WARN				("warn")
#define PLC_AGENT_LOG_LEVEL_ERR					("err")
#define PLC_AGENT_LOG_LEVEL_OFF					("off")


/**@struct server_info
* @brief 定义配置PLC Agent TCP Server的配置项，包含IP地址及端口设置
*/
typedef struct _server_info
{
	string ip_addr;
	int port;
	int udp_port;

	string client_udp_ip;
	int client_udp_port;

	string client_tcp_ip0;
	int client_tcp_port0;

	string client_tcp_ip1;
	int client_tcp_port1;

	string scan_client_tcp_ip0;
	int scan_client_tcp_port0;

	string scan_client_tcp_ip1;
	int scan_client_tcp_port1;

	string feeder_client_tcp_ip0;
	int feeder_client_tcp_port0;

	string feeder_client_tcp_ip1;
	int feeder_client_tcp_port1;

	string zmq_tcp_slot_addr = "tcp://************:8000";
	string zmq_tcp_seal_addr = "tcp://************:8001";

}server_info;


typedef struct _log_cfg
{
	spdlog::level::level_enum log_level;
	bool net_log;
	bool protocol_log;
	bool zmq_log;
	bool dev_log;
	bool heartbeat_log;
}log_cfg;


typedef struct _plc_work_mode
{
	int mode = 0;

	bool over_length;
	int length_max;
}plc_work_mode;

typedef struct _package_over_info
{
	bool over_length;
	int length_max;
}package_over_info;

typedef struct _para_t
{
    /* belt servo para */
    unsigned int belt_turn_speed; //速度
    unsigned int belt_turn_time;  //皮带转动时间，单位：0.01s
    unsigned int belt_turn_delay_time; //皮带转动延时时间，单位：0.01s
    unsigned int belt_acc;
    unsigned int work_mode; //工作模式，0：正常模式；others：测试模式

	uint16_t run_dir_config[2];

}para_t;

typedef struct _feeder_info
{
	string scan_dev1_addr = "/dev/ttysWK0";
	string scan_dev2_addr = "/dev/ttysWK1";

	uint16_t feeder_count = 1;
    uint16_t belt_count = 3;
	uint16_t first_feeder_dir = -1;
	uint16_t second_feeder_dir = -1;
	uint16_t double_feeder_scan = 1;
	uint16_t feeder_belt_virtual = 0;
	uint16_t cancel_container_change = 0;
	uint16_t cancel_container_id = 10101;
	uint16_t code_count = 10000;
	uint16_t belt1_spd = 9000;
	uint16_t belt2_spd = 9000;
	uint16_t belt3_spd = 9000;
	uint16_t belt4_spd = 9000;
  
	uint16_t belt_acc = 0x012c;
	uint16_t belt_dcc = 0x012c;

	float belt1_ratio = 2;
	float belt2_ratio = 2;
	float belt3_ratio = 2;
	float belt4_ratio = 2;

	uint16_t belt1_diameter = 37;
	uint16_t belt2_diameter = 37;
	uint16_t belt3_diameter = 37;
	uint16_t belt4_diameter = 37;
	
	uint16_t belt1_len = 1567;
	uint16_t belt2_len = 845;
	uint16_t belt3_len = 845;
	uint16_t belt4_len = 845;

	int16_t belt1_dis = -50;
	int16_t belt2_dis = -50;
	int16_t belt3_dis = -50;
	int16_t belt4_dis = -50;

	int16_t belt1_dis_stop = -120;
	int16_t belt2_dis_stop = -120;
	int16_t belt3_dis_stop = -120;
	int16_t belt4_dis_stop = -120;
    uint16_t isuse_fuse    = 1;
	uint16_t volume_enable = 0;
	uint16_t goods_len_max = 800;
	uint16_t len_max = 800;
	uint16_t wide_max = 500;
	uint16_t high_max = 400;
	uint16_t weight_enable = 0;
	uint16_t weight_max = 5000;

	uint16_t volume_key = 2;
	uint16_t auto_scan_static = 0;
	uint16_t sku_code_len = 256;
	uint16_t noread_stop = 0;
	uint16_t weight_min  = 0;

	uint16_t auto_feeder = 0;

	para_t para;
	server_info ip_info;

}feeder_info;

extern bool plc_agent_get_config(  server_info *s_info, log_cfg *log_config, plc_work_mode *default_mode,feeder_info *p_feeder_info);

#endif
