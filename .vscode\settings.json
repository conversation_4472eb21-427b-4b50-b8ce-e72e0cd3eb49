{"files.associations": {"iomanip": "cpp", "condition_variable": "cpp", "array": "cpp", "atomic": "cpp", "*.tcc": "cpp", "bitset": "cpp", "cctype": "cpp", "chrono": "cpp", "clocale": "cpp", "cmath": "cpp", "codecvt": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "forward_list": "cpp", "list": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "filesystem": "cpp", "functional": "cpp", "optional": "cpp", "ratio": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "fstream": "cpp", "future": "cpp", "initializer_list": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "memory": "cpp", "mutex": "cpp", "new": "cpp", "ostream": "cpp", "numeric": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "thread": "cpp", "cfenv": "cpp", "cinttypes": "cpp", "regex": "cpp", "utility": "cpp", "typeinfo": "cpp", "valarray": "cpp"}, "cmake.sourceDirectory": "D:/work/rebin_schedule_Ho<PERSON>_new/vehicle_agent"}