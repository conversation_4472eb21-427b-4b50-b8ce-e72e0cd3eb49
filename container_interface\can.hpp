#ifndef __CONTAINERS_INTERFACE_CAN_H__
#define __CONTAINERS_INTERFACE_CAN_H__
#include <stdint.h>

#include <stdint.h>
#ifdef __linux__
#include <sys/epoll.h>
#include <net/if.h>
#include <linux/can.h>
#include <linux/can/error.h>
#include <linux/can/raw.h>
#include <fcntl.h>
#include <arpa/inet.h>
#include <sys/types.h>
#include <sys/ioctl.h>
#include <unistd.h>
#include <stdio.h>
#include <string.h>
#include <sys/socket.h>

#include <sys/select.h>
#include <sys/time.h>
#endif
#include <assert.h>
#include <ctime>
#include <deque>

#include <spdlog/spdlog.h>

#include <errno.h>
#include "net/tcp_socket.hpp"
#include <signal.h>

#define CMD_LEN  1204
#define NONBLOCK 1
#define BLOCK    0

#define MAX_RETRY_TIMES 10

class can_interface
{
public:

    int init_can(int iDevNum, int iMode, int iBitRate, int iFd);//, void *pFilter, int iFilterSize);
    int set_can_filter(void *pfilter,int size);
    int can_send(struct can_frame *can_frame_msg);
    int can_recv(struct can_frame *can_frame_msg, uint32_t waiting_usec);
    int net_to_can_init(std::string server_ip, int server_port);
    int can_to_net_send(can_frame *can_frame_msg);
    int close_net();
    int get_fd()
    {
        return fd;
    }

private:

    tcp_socket socket;
    int fd;
    int can_to_net(can_frame *can_frame_msg, char *net_msg);

    static void pipesig_handler(int sig)       //pipsig处理
    {
        SPDLOG_ERROR("net_to_can receive signal {}", sig);
    }
};

#endif
