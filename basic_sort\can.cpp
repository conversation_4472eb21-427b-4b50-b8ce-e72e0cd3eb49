/********************************************************************************
* Copyright (C) 2025, JD.COM, Inc.
* All rights reserved.
* FileName: can.c
* Author: <PERSON><PERSON><PERSON><PERSON>   Version: V1.0   Data:2017-06-23
* Description:
*            : set can para cmd:
*            : ip link set can0 up type can bitrate 500000
*            : ip link set can0 down
*            : ip link set can0 up
********************************************************************************/
//#include "global.h"
#include "can.hpp"
//#include "debug.h"
#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#define CAN_RCV_BUFF_SIZE	1024*1024	//Bytes
#define CAN_SND_BUFF_SIZE	1024*1024	//Bytes
agv_parm_t g_stAgvParm;	// agv parameter(s)
// CAN0总线上主要有：左轮、右轮、托盘、升降伺服电机
// CAN0总线的波特率:1Mbps
struct can_filter  g_stCan1Filter[CAN1_FILTER_SIZE] =
{
	{ .can_id = 0x583, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x585, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x587, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x589, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x183, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x185, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x187, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x189, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x283, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x285, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x287, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x289, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x383, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x385, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x387, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x389, .can_mask = CAN_SFF_MASK },
#if 0
	{ .can_id = 0x38A, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x30A, .can_mask = CAN_SFF_MASK },
#endif
	{ .can_id = 0x483, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x485, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x487, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x489, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x083, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x085, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x087, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x089, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x703, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x705, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x707, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x709, .can_mask = CAN_SFF_MASK },
};

// CAN0总线上主要有：PGV模块、电池模块
// CAN0总线的波特率:500Kbps
struct can_filter  g_stCan0Filter[CAN0_FILTER_SIZE] =
{
	{ .can_id = PGV_CANID_TxPDO1, .can_mask = CAN_SFF_MASK },
	{ .can_id = PGV_CANID_TxPDO2, .can_mask = CAN_SFF_MASK },
	{ .can_id = PGV_CANID_TxPDO3, .can_mask = CAN_SFF_MASK },
	{ .can_id = PGV_CANID_TxPDO4, .can_mask = CAN_SFF_MASK },
	{ .can_id = PGV_CANID_TxTEST, .can_mask = CAN_SFF_MASK },
	{ .can_id = PGV_CANID_TxTEST_ACK, .can_mask = CAN_SFF_MASK },

	{ .can_id = BATT_CANID_REQ0X16, .can_mask = CAN_SFF_MASK },
	{ .can_id = BATT_CANID_REQ0X20, .can_mask = CAN_SFF_MASK },
	{ .can_id = BATT_CANID_ACK0X10, .can_mask = CAN_SFF_MASK },
	{ .can_id = BATT_CANID_ACK0X15, .can_mask = CAN_SFF_MASK },

	{ .can_id = 0x38A, .can_mask = CAN_SFF_MASK },
	{ .can_id = 0x30A, .can_mask = CAN_SFF_MASK },
};

/*******************************************************************************
* Function Name		 : init_can_socket
* Description	     : init the socket of can, and set can filter
* Input 		     : iDevNum: the index of can device
* Input 		     : iMode: BLOCK or NONBLOCK
* Input 		     : iBitRate: the bitrate of can, such as:5000bps, 10000bps
* Input 		     : pFilter: can filter buffer
* Output		     : NONE
* Return		     : 0: success; -1:failure
*******************************************************************************/
#define CMD_LEN  1204
int init_can_socket(int iDevNum, int iMode, int iBitRate, void *pFilter, int iFilterSize)
{
	int iRet = 0;
	int iFlags = 0;
	int iSockFd = 0;

	int iRcvSize = 0;
	int iSndSize = 0;
	
	struct sockaddr_can stSockAddr = {0};
	struct ifreq stIfReq = {0};
	char ifName[16] = {0};
	int iLoopBack      = 0; /* 0 = disabled, 1 = enabled (default) */
	int iRecvOwnMsgs = 0; /* 0 = disabled (default), 1 = enabled */
	char pCMD[CMD_LEN] = { 0 };

	if (pFilter == NULL) {
		//LOG_ERR("can_filter is NULL");
		return -1;
	}
	
	if ((iDevNum < 0) || (iDevNum > 1)) {
		//LOG_ERR("Wrong devnum: %d", iDevNum);
		return -1;
	}	
	sprintf(ifName, "%s%d", "can", iDevNum);

	// 重新关闭和开启CAN接口, 并设置CAN接口的比特率
	memset(pCMD, 0, CMD_LEN);
	//snprintf(pCMD, CMD_LEN, "sudo ip link set can%d down", iDevNum);
	snprintf(pCMD, CMD_LEN, "echo jd@ugv | sudo -S ip link set can%d down", iDevNum);
	
	SPDLOG_INFO("execute cmd :{}", pCMD);
	//system(pCMD);

	memset(pCMD, 0, CMD_LEN);
	snprintf(pCMD, CMD_LEN, "echo jd@ugv | sudo -S ip link set can%d up type can bitrate %d", iDevNum, iBitRate);
	//snprintf(pCMD, CMD_LEN, "echo jd@ugv | sudo -S ip link set can%s up type can bitrate %d",iDevNum, iBitRate);
	SPDLOG_INFO("execute cmd :{}", pCMD);
	//system(pCMD);
   
	// 创建can socket
	iSockFd = socket(PF_CAN, SOCK_RAW, CAN_RAW);
	if(iSockFd < 0) {
		SPDLOG_INFO("socket error:{},:{}", errno, strerror(errno));
		return -1;
	}

	strcpy(stIfReq.ifr_name, ifName);
	ioctl(iSockFd, SIOCGIFINDEX, &stIfReq);

	stSockAddr.can_family  = PF_CAN;
	stSockAddr.can_ifindex = stIfReq.ifr_ifindex; 

	iRet = bind(iSockFd, (struct sockaddr *)&stSockAddr, sizeof(stSockAddr));
	if(iRet < 0) {
		SPDLOG_INFO("bind error:{},:{}", errno, strerror(errno));
		goto error_0;
	}

	// set socket option with O_NONBLOCK or not
	iFlags = fcntl(iSockFd, F_GETFL, 0);
	if (iFlags == -1) {
		SPDLOG_INFO("can:{}fcntl F_GETFL fail:{}", iDevNum, strerror(errno));
		iRet = -1;
		goto error_0;
	}	
	if (NONBLOCK == iMode) {
		iFlags |= O_NONBLOCK;
	} else {
		iFlags &= ~O_NONBLOCK;
	}	
	if (fcntl(iSockFd, F_SETFL, iFlags) == -1)
	{
		SPDLOG_INFO("can:{},set block fail:{},:{}", iDevNum, errno, strerror(errno));
		goto error_0;
	}

	// set socket option with CAN_RAW_LOOPBACK\CAN_RAW_RECV_OWN_MSGS\CAN_RAW_FILTER
	setsockopt(iSockFd, SOL_CAN_RAW, CAN_RAW_LOOPBACK, &iLoopBack, sizeof(iLoopBack));
	setsockopt(iSockFd, SOL_CAN_RAW, CAN_RAW_RECV_OWN_MSGS, &iRecvOwnMsgs, sizeof(iRecvOwnMsgs));
	iRet = setsockopt(iSockFd, SOL_CAN_RAW, CAN_RAW_FILTER, pFilter, iFilterSize);
	if (iRet < 0)
	{
		//LOG_WRN("set CAN_RCV_BUFF_SIZE failed:[%d-%s]\n", errno, strerror(errno));
	}

	// Linux CAN socket不支持接收和发送缓存区大小的设置
#if 0
	// set socket option with SO_RCVBUF
	iRcvSize = CAN_RCV_BUFF_SIZE;
	iRet = setsockopt(iSockFd, SOL_CAN_RAW, SO_RCVBUF, (char*)&iRcvSize, sizeof(iRcvSize));
	if (iRet < 0 )
	{
		LOG_WRN("set CAN_RCV_BUFF_SIZE failed:[%d-%s]\n", errno, strerror(errno));
	}
	// set socket option with SO_SNDBUF
	iSndSize = CAN_SND_BUFF_SIZE;
	iRet = setsockopt(iSockFd, SOL_CAN_RAW, SO_SNDBUF, (char*)&iSndSize, sizeof(iSndSize));
	if (iRet < 0)
	{
		LOG_WRN("set CAN_RCV_BUFF_SIZE failed:[%d-%s]\n", errno, strerror(errno));
	}

	// get socket option with SO_RCVBUF
	iRcvSize = CAN_RCV_BUFF_SIZE;
	iRet = getsockopt(iSockFd, SOL_CAN_RAW, SO_RCVBUF, (char*)&iRcvSize, &iRcvSize);
	if (iRet == 0)
	{
		LOG_INF("get CAN_RCV_BUFF_SIZE = %d bytes\n", iRcvSize);
	}
	// get socket option with SO_SNDBUF
	iSndSize = CAN_SND_BUFF_SIZE;
	iRet = getsockopt(iSockFd, SOL_CAN_RAW, SO_SNDBUF, (char*)&iSndSize, &iSndSize);
	if (iRet == 0)
	{
		LOG_WRN("set CAN_RCV_BUFF_SIZE = %d bytes\n", iSndSize);
	}
#endif

	return iSockFd;
	
error_0:
	if (iSockFd > 0) {
		close(iSockFd);
		iSockFd = 0;
	}

	return iRet;
}

/*******************************************************************************
* Function Name		 : close_can_socket
* Description	     : close the socket of can
* Input 		     : iSockFd: the fd of can socket
* Output		     : NONE
* Return		     : 0: success; -1:failure
*******************************************************************************/
int close_can_socket(int iSockFd)
{
	if (iSockFd > 0) {
		close(iSockFd);
		iSockFd = 0;
	}

	return 0;
}

/*******************************************************************************
* Function Name		 : init_can
* Description	     : int can filter.
* input			     : iDevNum:can bus number, 0:can0, 1:can1
* input			     : pfilter: filter buffer;
* input			     : iSize: filter size.
* Output		     : NONE
* Return		     : 0:success. <0:failure
*******************************************************************************/
int init_can(int iDevNum, int iMode)
{
	struct ifreq if_dev;
	struct sockaddr_can stAddr;
	int iSockFd = 0;
	int iFlags = 0;

	if ((iSockFd = socket(PF_CAN, SOCK_RAW, CAN_RAW)) < 0)
	{
		//LOG_ERR("can socket error:[%d-%s]\n", errno, strerror(errno));
		return -1;
	}

	if (iDevNum == CAN0)
	{
		strncpy(if_dev.ifr_name, "can0", IFNAMSIZ); // set name of can
		g_stAgvParm.iCanSocketFd0 = iSockFd;
	}
	else if (iDevNum == CAN1)
	{
		strncpy(if_dev.ifr_name, "can1", IFNAMSIZ); // set name of can
		g_stAgvParm.iCanSocketFd1 = iSockFd;
	}
	else
	{
		//LOG_ERR("wrong can bus num\n");
		return -1;
	}	
	ioctl(iSockFd, SIOCGIFINDEX, &if_dev);
	stAddr.can_family = PF_CAN;
	stAddr.can_ifindex = if_dev.ifr_ifindex;

	if (bind(iSockFd, (struct sockaddr *)&stAddr, sizeof(stAddr)) < 0) // bind the socket to device
	{
		//LOG_ERR("can%d bind error:[%d-%s]!\n", iDevNum, errno, strerror(errno));
		return -1;
	}

	if ((iFlags = fcntl(iSockFd, F_GETFL, 0)) == -1)
	{
		//LOG_ERR("can%d fcntl F_GETFL fail:[%d-%s]\n", iDevNum, errno, strerror(errno));
		exit(1);
	}
	if (NONBLOCK == iMode)
	{
		iFlags |= O_NONBLOCK;
		if (fcntl(iSockFd, F_SETFL, iFlags) == -1)
		{
			//LOG_ERR("can%d set nonblock fail:[%d-%s]\n", iDevNum, errno, strerror(errno));
			return -1;
		}
	}
	else
	{
		iFlags &= ~O_NONBLOCK;
		if (fcntl(iSockFd, F_SETFL, iFlags) == -1)
		{
			//LOG_ERR("can%d set block fail:[%d-%s]\n", iDevNum, errno, strerror(errno));
			return -1;
		}
	}
	// TODO
	//setsockopt(iSockFd, SOL_CAN_RAW, CAN_RAW_FILTER, &filter, sizeof(filter)); 
	
	return 0;

}

/*******************************************************************************
* Function Name		 : set_can_filter
* Description	     : set  can filter.
* input			     : iDevNum:can bus number, 0:can0, 1:can1
* input			     : pfilter: filter buffer;
* input			     : iSize: filter size.
* Output		     : NONE
* Return		     : 0:success. <0:failure
*******************************************************************************/
int set_can_filter(int iDevNum, void *pfilter,int iSize )
{
	int iSockFd = 0;
	int iRet = 0;

	if (pfilter == NULL)
	{
		//LOG_ERR("can%d filter NULL\n", iDevNum);
		return -1;
	}

	if (iDevNum == CAN0)
	{
		iSockFd = g_stAgvParm.iCanSocketFd0;
	}
	else if (iDevNum == CAN1)
	{
		iSockFd = g_stAgvParm.iCanSocketFd1;
	}
	else
	{
		//LOG_ERR("wrong can bus num\n");
		return -1;
	}

	iRet = setsockopt(iSockFd, SOL_CAN_RAW, CAN_RAW_FILTER, pfilter, iSize);
	return iRet;
}

/*******************************************************************************
* Function Name		 : send_can
* Description	     : send can.frame to bus.
* input			     : iDevNum:can bus number, 0:can0, 1:can1
* input			     : pCanFrame:can frame buffer
* Output		     : NONE
* Return		     : return the send byte number on success. <0:failure
*******************************************************************************/
int send_can(int iDevNum, struct can_frame *pCanFrame)
{
	int iSockFd = 0;
	int iNum = 0;

	if (pCanFrame == NULL)
	{
		//LOG_ERR("can%d send buffer is null\n", iDevNum);
		return -1;
	}

	if (iDevNum == CAN0)
	{		
		//iSockFd = g_stAgvParm.iCanSocketFd0;
	}
	else if (iDevNum == CAN1)
	{		
		//iSockFd = g_stAgvParm.iCanSocketFd1;
	}
	else
	{
		//LOG_ERR("wrong can bus num\n");
		return -1;
	}

	iNum = write(iSockFd, pCanFrame, sizeof(struct can_frame));
	if (iNum < 0)
	{
		//LOG_MCU("can%d send error:[%d-%s]\n", iDevNum, errno, strerror(errno));
		return -1;
	}

	return iNum;
}

/*******************************************************************************
* Function Name		 : recv_can
* Description	     : receive can.frame from bus.
* input			     : iDevNum:can bus number, 0:can0, 1:can1
* input			     : pCanFrame:can frame buffer
* Output		     : NONE
* Return		     : return the receive byte number on success. <0:failure
*******************************************************************************/
int recv_can(int iDevNum, struct can_frame *pCanFrame)
{
	int iSockFd = 0;
	int iRet = 0;

	if (pCanFrame == NULL)
	{
		//LOG_ERR("can%d recv buffer is null\n", iDevNum);
		return -1;
	}
	if (iDevNum == CAN0)
	{
		//iSockFd = g_stAgvParm.iCanSocketFd0;
	}
	else if (iDevNum == CAN1)
	{
		//iSockFd = g_stAgvParm.iCanSocketFd1;
	}
	else
	{
		//LOG_ERR("wrong can bus num\n");
		return -1;
	}

	iRet = read(iSockFd, pCanFrame, sizeof(struct can_frame));

	return iRet;

}

/*******************************************************************************
* Function Name		 : close_can
* Description	     : close can.
* input			     : iDevNum:can bus number, 0:can0, 1:can1
* Output		     : NONE
* Return		     : 0:success. <0:failure
*******************************************************************************/
int close_can(int iDevNum)
{
	int iRet = 0;
	
	if (iDevNum == CAN0)
	{
		iRet = close(g_stAgvParm.iCanSocketFd0);
	}
	else if (iDevNum == CAN1)
	{
		iRet = close(g_stAgvParm.iCanSocketFd1);
	}
	else
	{
		//LOG_ERR("wrong can bus num\n");
		return -1;
	}

	return iRet;
}
