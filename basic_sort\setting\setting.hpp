#ifndef __THING_AGENT_SETTING_H__
#define __THING_AGENT_SETTING_H__

#include <spdlog/spdlog.h>
#include "share/nlohmann_json/json.hpp"

class setting
{
    using json = nlohmann::json;

public:

    static setting *get_instance(void)
	{
		static setting instance;
		return &instance;
	}

    struct setting_data
    {
        int device_id;
        int belt_segment_number;
        int auto_scanner_id;
        int manual_scanner_id;

        std::string sdk_config_host_name;
        int32_t sdk_config_host_port;
        std::string sdk_config_device_id;
        std::string sdk_config_ca_path;
        std::string sdk_config_cert_path;
        std::string sdk_config_key_path;

        std::string thing_model_id;
        std::string thing_model_version;

        uint32_t time_query_vehicle_info;
        uint32_t time_report_vehicle_state;
        uint32_t time_report_sys_state;
    };

    const setting_data & get_setting(void) const
    {
        return settings;
    }

    int load_setting(const char *file_name);

private:

    setting_data settings;
    int load_agent_setting(const char *file_name);
};

#endif