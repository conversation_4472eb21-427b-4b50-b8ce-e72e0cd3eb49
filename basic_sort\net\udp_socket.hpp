﻿
/**@file  udp_socket.hpp
* @brief       基于udp的socket操作软件二次封装对应头文件
* @details     NULL
* <AUTHOR>
* @date        2021-07-01
* @version     v1.2.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.2.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/06/11  <td>1.0.1    <td>lizhy     <td>初始版本，支持udp服务器建立              </tr>
* <tr><td>2021/06/22  <td>1.1.0    <td>lizhy     <td>
* -# 对socket API进行二次封装，使用class的成员函数实现socket API功能
* <tr><td>2021/07/01  <td>1.2.0    <td>lizhy     <td>
* -# 添加quick ack功能设计 
* </table>
*
**********************************************************************************
*/




#ifndef __NET_udp_SOCKET_HPP__
#define __NET_udp_SOCKET_HPP__


#include <string>
#include <netinet/ip.h>

#include <iostream>


/**
* @brief 实现udp Socket通信的二次封装，将常用的socket操作封装于udp_socket 类中
*/

class udp_socket
{
	

#define udp_SOCKET_MIN_PORT	 (1000)
#define udp_SOCKET_MAX_PORT	 (10000)

#define is_udp_SOCKET_PORT_VALID(port)				( (port>=udp_SOCKET_MIN_PORT)&&(port<=udp_SOCKET_MAX_PORT) )

public:

	/**@brief  udp_socket class构造函数
	* @param[in]  NULL
	* @return	  NULL
	*/
    explicit udp_socket();

	/**@brief  udp_socket class析构函数，调用时关闭udp服务器的文件描述符
	* @param[in]  NULL
	* @return	  NULL
	*/
    ~udp_socket();

	
	/**@brief	  udp 通信中 Server 构造
	* @param[in]  maxWaiter --- 支持的最大客户端数量，默认16
	* @return	  函数执行结果
	* - false	  server创建失败
	* - true	  server创建成功
	*/
	bool udp_socket_init(int maxWaiter);

	/**@brief	  udp 通信中 Server 构造
	* @param[in]  NULL
	* @return	  函数执行结果
	* - false	  server创建失败
	* - true	  server创建成功
	*/
	bool udp_socket_init(void);
	

	/**@brief	  udp 通信中 Server 地址及端口绑定
	* @param[in]  NULL
	* @return	  函数执行结果
	* - false	  server绑定失败
	* - true	  server绑定成功
	*/
	bool udp_socket_bind(void);
	

	/**@brief	  向udp Server分配IP地址及端口号
	* @param[in]  const std::string &ip --- 服务器IP地址
	* @param[in]  int port --- 服务器端口号
	* @return	  函数执行结果
	* - false	  server设置失败
	* - true	  server设置成功
	*/
	bool udp_socket_server_cfg(const std::string &ip, int _port) ;
	/**@brief	  向udp client分配IP地址及端口号
	* @param[in]  const std::string &ip --- 服务器IP地址
	* @param[in]  int port --- 服务器端口号
	* @return	  函数执行结果
	* - false	  client设置失败
	* - true	  client设置成功
	*/
	bool udp_socket_client_cfg(const std::string &ip, int port,struct sockaddr_in &config_addr) ;

	/**@brief	  设置udp 服务器 IP 地址reuse特性，软件异常停止后可以第一时间恢复该地址的使用
	* @param[in]  bool option --- reuse特性开启操作
	* @ref			true  开启地址 reuse
	* @ref			false 禁止地址 reuse
	* @return	  函数执行结果
	* - false	  server设置失败
	* - true	  server设置成功
	*/
	bool udp_socket_set_reuseaddr(bool option)  ;

	
	/**@brief	  设置udp 服务器 IP 端口reuse特性，软件异常停止后可以第一时间恢复该端口的使用
	* @param[in]  bool option --- reuse特性开启操作
	* @ref			true  开启端口 reuse
	* @ref			false 禁止端口 reuse
	* @return	  函数执行结果
	* - false	  server设置失败
	* - true	  server设置成功
	*/
	bool udp_socket_set_reuseport(bool option)  ;

	



	/**@brief	  获取udp 服务器端口号
	* @param[in]  NULl
	* @return	  udp 服务器端口号
	*/	
	unsigned int udp_socket_get_port() const ;	
	
	/**@brief	  close 套接字
	* @param[in]  int fd
	* @return	  函数执行结果
	* - false	  失败
	* - true	  成功
	*/	
	bool udp_socket_close(int fd );

	
	/**@brief	  设置描述符为非阻塞形式
	* @param[in]  int server_fd  ---  待操作描述符
	* @return	   函数执行结果
	* - false	  失败
	* - true	  成功
	*/
	bool udp_socket_set_nonblocking(int sock_fd);


	/**@brief	  获取当前服务器 描述符
	* @param[in]  NULL 
	* @return	  server 描述符
	*/
	inline int udp_socket_get_fd() const 
	{
		return m_udp_server_sockfd; 
	}

	
	/**@brief	  获取当前客户端 描述符
	* @param[in]  NULL 
	* @return	  server 描述符
	*/
	inline int udp_socket_get_clnt_fd() const 
	{
		return m_client_sockfd; 
	}

private:


	int m_udp_server_sockfd;	 ///< Server套接字描述符
	int m_client_sockfd;     ///< Client套接字描述符

    struct sockaddr_in m_serv_addr;  ///< Server IP 地址及端口结构体
	struct sockaddr_in m_client_addr_config;

	int m_default_processor;  ///< 
		
};


#endif
