#pragma once

#ifndef __CONTAINERS_INTERFACE_CONTAINERS_H__
#define __CONTAINERS_INTERFACE_CONTAINERS_H__

#include "can.hpp"
#include <thread>
#include <mutex>
#include <list>
#include <ctime>

#include <zmq.h>
#include <cppzmq/zmq.hpp>

#ifdef __linux__
#include <linux/can.h>
#endif

#include "share/pb/idl/container_cmd.pb.h"
#include "share/global_def.h"
#include "setting/setting.hpp"

#define DLC      8 //数据长度
#define BAUDRATE_500     500000
#define BAUDRATE_100     100000

//命令协议  主->从
#define MASTER_REQUEST_READ_SLAVE_ID                (0x30)
#define MASTER_REQUEST_CONTROL_SLAVE_LED            (0x50)
#define MASTER_REQUEST_CONTROL_SLAVE_CONTAIN        (0X31)
#define MASTER_REQUEST_CONTROL_CONTAIN_IDLE         (0X00)
#define MASTER_REQUEST_CONTROL_CONTAIN_CON          (0X03)
#define MASTER_REQUEST_CONTROL_CONTAIN_SEAL         (0X04)

#define MASTER_REQUEST_CHANGE_SLAVE_BAUDRATE        (0x70)
#define MASTER_REQUEST_CHANGE_SLAVE_REQUERY_TIMES   (0x90)
#define MASTER_REQUEST_INQUIRE_SLAVE_FAULT          (0xee)

//从->主
#define SLAVE_REPLY_LED_COLOR                       (0X60)
#define SLAVE_REPLY_QUERY                           (0x40)
#define CONTAINER_FULL                              (0xf0)
#define RASTER_TRIGGER                              (0xf1)
#define CONTAINER_EMPTY                             (0xe0)
#define SLAVE_SEAL_STATE                            (0xf2)
#define SLAVE_SEAL_CMD_REPLY                        (0x41)

//天津信封封箱状态解析
#define CONTAINER_SEAL_IDEL                         (0x00)
#define CONTAINER_SEAL_FULL                         (0x10)
#define CONTAINER_SEAL_DONE                         (0x20)
#define CONTAINER_SEAL_CONTAIN                      (0x30)
#define CONTAINER_SEAL_T_SEAL                       (0x40)   //物控封箱

#define CONTAINER_SEAL_IDEL_RECV                    (0x00)
#define CONTAINER_SEAL_FULL_RECV                    (0x01)
#define CONTAINER_SEAL_DONE_RECV                    (0x02)
#define CONTAINER_SEAL_CONTAIN_RECV                 (0x03)
#define CONTAINER_SEAL_T_SEAL_RECV                  (0x04)   //物控封箱

#define SLAVE_REPLY_ERROR                           (0xee)
#define SLAVE_REPLY_BAUDRATE_SET                    (0x80)
#define SLAVE_REPLY_REQUERY_TIMES_SET               (0xa0)

//#define ELIMINATE_DITHERING_TIMER                   100        //无框消抖时间，无框时，连续两次寻卡确认
#define RASTER_TRIGGER_TIMER                        1500        //满箱消抖时间
#define RASTER_CHECK_VALID_TIMER                    500         //光栅有效性检测时间

#define COLOR_LIGHT_OFF                             0
#define COLOR_YELLOW                                1
#define COLOR_GREEN                                 2
#define COLOR_RED                                   3
#define COLOR_BULE                                  4
#define COLOR_PURPLE                                5

class container_intf
{
public:

    container_intf(canid_t canid, int i, can_interface candev)
    {
        can_id = canid;
        box_state.box_id = i;
        box_state.box_st = box_state_UNBIND;
        strcpy(box_state.RFID, "0");
        can_dev = candev;
        saturation_state = EMPTY;
        seal_state = container_seal_state_UNKNOWN;
        rfid_prefix = setting::get_instance()->get_setting().rfid_prefix;
        rfid_format = setting::get_instance()->get_setting().rfid_format;
        rfid_offset = setting::get_instance()->get_setting().rfid_value_offset;
    }

    container_intf(canid_t canid, int i)
    {
        can_id = canid;
        box_state.box_id = i;
        box_state.box_st = box_state_UNBIND;
        strcpy(box_state.RFID, "0");
        saturation_state = EMPTY;
        seal_state = container_seal_state_UNKNOWN;
        rfid_prefix = setting::get_instance()->get_setting().rfid_prefix;
        rfid_format = setting::get_instance()->get_setting().rfid_format;
        rfid_offset = setting::get_instance()->get_setting().rfid_value_offset;
    }

    const box_info_single &get_container_state()
	{
		return box_state;
	}

    const container_seal_state &get_seal_state()
    {
        return seal_state;
    }

    bool on_raster_trigger_timer()
    {
        if (!raster_trigger_timer.caculate_timing || saturation_state != RASTER_TRIGGERING)
            return false;
        auto time = raster_trigger_timer.execute_time();
        bool on_time = (time > RASTER_TRIGGER_TIMER) && (time < 200000);
        if (on_time)
        {
            raster_trigger_timer.caculate_timing = false;
            saturation_state = FULL;
        }
        return on_time;       //防止多线程冲突
    }

    canid_t &get_canid()
    {
        return can_id;
    }
    can_interface &get_dev()
    {
        return can_dev;
    }

    struct over_time
    {
        std::chrono::high_resolution_clock::time_point ledcontrol_start_time;
        std::chrono::high_resolution_clock::time_point query_start_time;

        std::chrono::high_resolution_clock::time_point overtime_start;   //用于两次超时之间的间隔  小的话超时次数清零

        uint32_t wait_query_reply_num = 0;
        uint32_t wait_led_reply_num = 0;
        uint32_t overtm_times = 0;
        uint32_t overtm_ledcontrol_tmp = 0;
        uint32_t overtm_query_tmp = 0;
        
        int overtime_interval()
        {
            std::chrono::high_resolution_clock::time_point overtime_end = std::chrono::high_resolution_clock::now();
            std::chrono::milliseconds interval = std::chrono::duration_cast<std::chrono::milliseconds>(overtime_end - overtime_start);

            return interval.count();
        }

        int query_execution_time()
        {
            std::chrono::high_resolution_clock::time_point end_time = std::chrono::high_resolution_clock::now();
		    std::chrono::milliseconds interval = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - query_start_time);

    		return interval.count();
        }

        int ledcontrol_execution_time()
        {
            std::chrono::high_resolution_clock::time_point end_time = std::chrono::high_resolution_clock::now();
		    std::chrono::milliseconds interval = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - ledcontrol_start_time);

    		return interval.count();
        }
    };

    int listen_recv_msg(__u8 data[CAN_MAX_DLEN]);
    int control_led(led_info &st);
    int control_contain(container_seal_state state);
    int control_query();
    over_time overtime;
    int set_can_dev(can_interface &candev);
    int set_baudrate(int baudrate);
    int set_requery_tms(int times);
    int fault_inquiry();

    uint64_t fail_query_tmp_times = 0;

    bool init_finish = false;

    int is_query_overtime(int64_t tm);
    int is_ledcontrol_overtime(int64_t tm);

    void set_init_finish()
    {
        init_finish = true;
    };

    //测试用
    int test_begin();
    int container_test();
    int led_test();
    int query_test();
    int state_print();
    int query_reply_time_parse();
    int ledcontrol_reply_time_parse();

    uint64_t query_times = 0;
    uint64_t query_reply_times = 0;
    uint64_t query_fail_times = 0;
    uint64_t card_exist_times = 0;
    uint64_t ledcontroll_times = 0;
    uint64_t ledcontrol_reply_times = 0;

    uint64_t reply_tms_tmp = 0;

    int max_reply_time = 0;
    int min_reply_time = 50;
    uint64_t sum_reply_time = 0;
    uint64_t avg_reply_time()
    {
        return uint64_t(sum_reply_time/query_reply_times);
    }
    uint64_t _0_30_reply_times = 0;
    uint64_t _30_40_reply_times = 0;
    uint64_t _40_50_reply_times = 0;
    uint64_t _50_60_reply_times = 0;
    uint64_t _60_reply_times = 0;

private:

    enum container_saturation_state
    {
        FULL,
        EMPTY,
        RASTER_TRIGGERING
    };

    struct timer
    {
        std::chrono::high_resolution_clock::time_point start_time;   //定时查询用
        std::chrono::high_resolution_clock::time_point end_time;

        bool caculate_timing = false;       //表示是否在计时中

        void start()
        {
            caculate_timing = true;
            start_time = std::chrono::high_resolution_clock::now();
        }

        int execute_time()
        {
            end_time = std::chrono::high_resolution_clock::now();
            std::chrono::milliseconds interval = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

            return interval.count();
        }
    };

    timer unbind_timer;
    timer raster_trigger_timer;
    timer raster_valid_timer;
    container_saturation_state saturation_state;
    container_seal_state seal_state;
    uint8_t  seal_data = 0xff;

    canid_t can_id;
    can_interface can_dev;
    box_info_single box_state;

    led_info last_led_state;  //记录最后一次收到的灯控状态，用于超时状态下的灯控重发
    box_info_single last_box_state;

    enum state
    {
        NORMAL = 0,
        ERR = 1
    };

    state can_state = NORMAL;
    state rfid_recognized_err = NORMAL;
    state rfid_access_err = NORMAL;

    std::string rfid_prefix;
    std::string rfid_format;
    int rfid_offset;

    int check_query_is_success(__u8 data[CAN_MAX_DLEN]);
    int update_box_state(__u8 data[CAN_MAX_DLEN]);
    int update_seal_state(uint8_t &data);
    int process_seal_state(uint8_t &data);

    int set_seal_state(const container_seal_state &st)
    {
        if (st != seal_state)
        {
            seal_state = st;
            return 1;
        }
        return 0;
    }
    int set_seal_data(uint8_t &data)
    {
        if (data != seal_data)
        {
            seal_data = data;
            return 1;
        }
        return 0;
    }
    int send_single_box_state();
    int key_down();
};

#endif