﻿
/**@file  	   plc_protocol.hpp
* @brief       PLC通信协议编解码处理
* @details     NULL
* <AUTHOR>
* @date        2021-11-26
* @version     v1.2.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.2.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/07/10  <td>1.0.0    <td>lizhy     <td>初始版本                         </tr>
* <tr><td>2021/11/25  <td>1.2.0    <td>lizhy     <td>
* -# 新增部分指令,适配PLC通信协议(1.5.2)版本
* </table>
*
**********************************************************************************
*/



#ifndef __PROTOCOL_PLC_PROTOCOL_HPP__
#define __PROTOCOL_PLC_PROTOCOL_HPP__

#include <vector>
#include  <iostream>
#include  <string>

using namespace std;


#define PLC_COMM_ROTOTCOL_SWITCH_OPT_MAX_SIZE					( 10 )
#define PLC_COMM_PROTOCOL_TCP_MAX_LEN							( 254 )


//PLC通信协议中的首尾符号及内部字段分隔符
#define PLC_COMM_PROTOCOL_START									( '<' )
#define PLC_COMM_PROTOCOL_STOP									( '>' )
#define PLC_COMM_PROTOCOL_SESSION_SEPARATOE						( ";" )
#define PLC_COMM_PROTOCOL_VALUE_SEPARATOE						( ":" )
#define PLC_COMM_PROTOCOL_DEV_SEPARATOE							( "," )


// PLC通信协议不同信息字段
#define PLC_PROTOCOL_SESSION_MSG_TYPE							( "MsgType" )
#define PLC_PROTOCOL_SESSION_SEQUENCE							( "Sequence" )
#define PLC_PROTOCOL_SESSION_DEVICE_ID							( "DeviceID" )
#define PLC_PROTOCOL_SESSION_TIME_STAMP							( "Timestamp" )
#define PLC_PROTOCOL_SESSION_ACK_MSG_TYPE						( "AckMsgType" )
//#define PLC_PROTOCOL_SESSION_DEV_STATUS							( "Status" )
//#define PLC_PROTOCOL_SESSION_DEV_FAULTCODE						( "FaultCode" )
#define PLC_PROTOCOL_SESSION_ERR_CODE_STATUS					( "Status" )
#define PLC_PROTOCOL_SESSION_ERR_CODE_FAULTCODE					( "FaultCode" )
#define PLC_PROTOCOL_SESSION_BUTTON_STATUS						( "Status" )

#define PLC_PROTOCOL_SESSION_RAIL_NO							( "RailNO" )
#define PLC_PROTOCOL_SESSION_RAIL_STATUS						( "RailStatus" )
#define PLC_PROTOCOL_SESSION_STORE_NO							( "StoreNO" )
#define PLC_PROTOCOL_SESSION_STORE_STATUS						( "StoreStatus" )
#define PLC_PROTOCOL_SESSION_SCAN_ID							( "ScanID" )
#define PLC_PROTOCOL_SESSION_SKU								( "SKU" )
#define PLC_PROTOCOL_SESSION_LOCATION							( "Location" )
#define PLC_PROTOCOL_SESSION_REBIN_LOCATION						( "RebinLocation" )
#define PLC_PROTOCOL_SESSION_REBIN_STATUS						( "Status" )
#define PLC_PROTOCOL_SESSION_LED_COLOUR							( "LEDColour" )
#define PLC_PROTOCOL_SESSION_RAIL_ACTION						( "RailAction" )
#define PLC_PROTOCOL_SESSION_CONVEYOR_NO						( "ConveyorNO" )
#define PLC_PROTOCOL_SESSION_DIRE								( "Direction" )
//#define PLC_PROTOCOL_SESSION_ERR_CODE							( "ERRcode" )
#define PLC_PROTOCOL_SESSION_MAIN_LED_MASK						( "Mask" )
#define PLC_PROTOCOL_SESSION_MAIN_LED_CMD						( "Cmd" )
#define PLC_PROTOCOL_SESSION_MAIN_LED_FREQ						( "Freq" )
#define PLC_PROTOCOL_SESSION_MODE								( "Mode" )
#define PLC_PROTOCOL_SESSION_MODE_SWITCH_TYPE					( "SwitchType" )
#define PLC_PROTOCOL_SESSION_CLEAN_GOODS						( "CleanGoods" )
#define PLC_PROTOCOL_SESSION_SCHEDULER_STATUS					( "SystemStatus" )





//PLC 消息类型字段
#define PLC_PROTOCOL_VALUE_ACK									( "ACK" )
#define PLC_PROTOCOL_VALUE_REG									( "REG" )
#define PLC_PROTOCOL_VALUE_HEART_BEAT							( "HBT" )
//#define PLC_PROTOCOL_VALUE_DEV_STATUS							( "STU" )
#define PLC_PROTOCOL_VALUE_ERR_CODE_REPORT						( "FCE" )
#define PLC_PROTOCOL_VALUE_BUTTON_STATE_REPORT					( "BTU" )
#define PLC_PROTOCOL_VALUE_SWITCH_STATUS						( "RLC" )
#define PLC_PROTOCOL_VALUE_SLOT_STATUS							( "STS" )
#define PLC_PROTOCOL_VALUE_GOOD_INFO							( "SCV" )
#define PLC_PROTOCOL_VALUE_GOOD_INFO_END						( "ESV" )
#define PLC_PROTOCOL_VALUE_REBIN_STATUS							( "RBS" )
#define PLC_PROTOCOL_VALUE_CTRL_LED								( "RTS" )
#define PLC_PROTOCOL_VALUE_CTRL_SWITCH							( "CRA" )
#define PLC_PROTOCOL_VALUE_CTRL_FEEDER							( "CLC" )
//#define PLC_PROTOCOL_VALUE_SCHEDULER_ERR						( "SFT" )
#define PLC_PROTOCOL_VALUE_CTRL_MAIN_LED						( "TLS" )
#define PLC_PROTOCOL_VALUE_DEV_MODE								( "MOD" )
#define PLC_PROTOCOL_VALUE_CLEAN_GOODS							( "CGS" )
#define PLC_PROTOCOL_VALUE_SCHEDULER_STATUS_PUB					( "SCD" )



/**@enum PLC_MSG_TYPE
* @brief PLC设备通信协议指令类型枚举变量，用于plc_agent内部运算处理使用
*/
typedef enum
{
	PLC_MSG_REG             = 0,
	PLC_MSG_HEART_BEAT      = 1,
	//PLC_MSG_DEV_STATUS      = 2,
	PLC_MSG_ERR_CODE        = 2,
	PLC_MSG_BUTTON_STATUS   = 3,
	PLC_MSG_SWITCH_STATUS   = 4,
	PLC_MSG_SLOT_STATUS     = 5,
	PLC_MSG_GOOD_INFO       = 6,
	PLC_MSG_GOOD_INFO_END   = 7,
	PLC_MSG_REBIN_STATUS    = 8,
	PLC_MSG_CTRL_LED        = 9,
	PLC_MSG_CTRL_SWITCH     = 10,
	PLC_MSG_CTRL_FEEDER     = 11,
	//PLC_MSG_SCHEDULER_ER    = 12,
	PLC_MSG_CTRL_MAIN_LED   = 12,
	PLC_MSG_DEV_MODE        = 13, 
	PLC_MSG_CLEAN_GOODS     = 14,
	PLC_MSG_SCHEDU_STATUS   = 15,
	PLC_MSG_ACK             = 29,
	PLC_MSG_UNKNOWN         = 30
}PLC_MSG_TYPE;


/**@enum _PLC_DEV_STATUS
* @brief PLC运行状态类型枚举变量
*/
typedef enum
{	
	PLC_DEV_STATUS_RUNNING				= 1,
	PLC_DEV_STATUS_STOP					= 2,
	PLC_DEV_STATUS_SUSPEND				= 3,
	PLC_DEV_STATUS_EMERGENCY			= 4,
	PLC_DEV_STATUS_SAGE_GATE_OPEN		= 5,
	PLC_DEV_STATUS_FAULT				= 6
}_PLC_DEV_STATUS;


/**@struct PLC_COMM_ACK
* @brief PLC ACK消息格式
*/
typedef struct _PLC_COMM_ACK
{
	string msg_start;
	string msg_type;
	string sequence;
	string dev_id;
	string ack_msg_type;
	string msg_end;
}PLC_COMM_ACK;


/**@struct PLC_COMM_UPLOAD_MSG
* @brief PLC 设备上行消息格式
*/
typedef struct _PLC_COMM_UPLOAD_MSG
{
	string msg_type;
	string sequence;
	string dev_id;
	string up_payload_1;
	string up_payload_2;
	string up_payload_3;
}PLC_COMM_UPLOAD_MSG;


/**@struct PLC_COMM_DOWNLOAD_MSG
* @brief PLC 设备下发消息格式
*/
typedef struct _PLC_COMM_DOWNLOAD_MSG
{
	//string msg_start;
	string msg_type;
	string sequence;
	string dev_id;
	string down_payload_1;
	string down_payload_2;
	string down_payload_3;
	string time_stamp;
	//string msg_end;
}PLC_COMM_DOWNLOAD_MSG;


/**@struct PLC_MAIN_LED_CTRL
* @brief PLC 系统状态塔灯控制结构体
*/
typedef struct _PLC_MAIN_LED_CTRL
{
	uint32_t mask;
	uint16_t cmd;
	uint16_t freq;
}PLC_MAIN_LED_CTRL;



/**@brief     PLC下发消息编码函数
* @param[out]  PLC_COMM_DOWNLOAD_MSG *msg      --- 编码输出的字符串分解结构体
* @param[in]  PLC_MSG_TYPE msg_type  --- 消息类型
* @return     NULL
*/
extern void plc_protocol_codec_downlink(PLC_COMM_DOWNLOAD_MSG *msg          , PLC_MSG_TYPE msg_type);


/**@brief     PLC商法消息的解码函数，用于提取有效字符串，然后按照通信协议进行字符串分割
* @param[in]  char *msg  --- 网络接收数据，字符串类型
* @param[in]  uint16_t msg_len  --- 网络接收字符串长度
* @param[in]  vector<string> *str_vec  --- 初次字符串分割后的vector，每一个成员均为PLC通信协议中的一个字段
* @return	  字符串解码结果	
* - true	  原始数据有效，解码vector可用
* - false	  原始数据无效，解码vector不可用
*/
extern bool plc_protocol_decodec(char *msg, uint16_t msg_len, vector<string> *str_vec);


/**@brief     PLC通信协议中，时间戳生成函数
* @param[in]  NULL
* @return     生成的时间戳字符串
*/
extern string plc_protocol_generate_time_stamp(void);


/**@brief     PLC下发ACK编码函数
* @param[out]  PLC_COMM_DOWNLOAD_MSG *msg      --- 编码输出的字符串分解结构体
* @param[in]  PLC_MSG_TYPE msg_type  --- 消息类型
* @return     NULL
*/
extern void plc_protocol_codec_ack(PLC_COMM_DOWNLOAD_MSG *msg          , PLC_MSG_TYPE msg_type);

/**@brief     PLC下发ACK编码函数
* @param[in]  PLC_COMM_DOWNLOAD_MSG *msg_input   --- 内部字段整合完成，待拼接的MSG结构体
* @return     编码生成的字符串，用于最终网络下发
*/
extern string plc_protocol_data_generate(PLC_COMM_DOWNLOAD_MSG *msg_input);
#endif
