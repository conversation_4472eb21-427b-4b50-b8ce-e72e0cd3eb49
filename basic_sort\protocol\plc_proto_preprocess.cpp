﻿

/**@file  	   plc_proto_preprocess.cpp
* @brief       PLC通信字符串预处理，实现字符串的分割、转换等操作
* @details     NULL
* <AUTHOR>
* @date        2021-07-10
* @version     v1.0.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.2.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/07/10  <td>1.0.0    <td>lizhy     <td>初始版本                         </tr>
* </table>
*
**********************************************************************************
*/



#include "../plc_agent_debug.h"

#include "plc_proto_preprocess.hpp"

#include <iostream>
#include <string>
#include <vector>
#include <stack> 


using namespace std;




/**@brief     字符串分割函数，按照给定的分割符号对输入字符串进行分割操作
* @param[in]  const string &str      --- 输入待分割原始字符串
* @param[out] vector<string> *vec    --- 分割完成后输出的 vector
* @param[in]  const string &spl_char --- 指定的字符串分割符
* @return     NULL
*/
void string_split(const string &str, vector<string> *vec, const string &spl_char)
{
	string::size_type pos = str.find(spl_char);
	string::size_type pos1 = 0;
	while(string::npos !=  pos)
	{
		vec->push_back(str.substr(pos1, pos -pos1));
		pos1 = pos + spl_char.size();
		pos = str.find(spl_char, pos1);
	}

	if(pos1 != str.length())
	{
		vec->push_back(str.substr(pos1));
	}

}


/**@brief     对左右括号的二进制重新赋值操作
* @param[in]  char c      --- 输入的各类型括号
* @return     括号的二进制重新赋值
*/
uint8_t char_select(char c)
{
    unsigned char return_value = 0x00;

    switch(c)
    {        
        case 0x02:
            return_value = 0x02;
            break;

        case '<':
            return_value = 0x03;
            break;
                
        case 0x03:
            return_value = 0x05;
            break;
        
        case '>':
            return_value = 0x06;
            break;
        
        default:
            return_value = 0x00;
            break;
    }

    return return_value;
}




/**@brief     特征符号查找与提取函数，用于对原始字符串进行特征匹配查找与提取
* @param[in]  uint8_t *data_input      --- 输入待处理原始字符串
* @param[in]  uint16_t input_len       --- 原始字符串长度
* @param[out] uint16_t *head_pos       --- 匹配查找完成的特征头字符在原始字符串中的位置r
* @param[in]  uint16_t *data_len           --- 有效字符串长度
* @return	  字符串预处理结果	
* - true	  原始数据有效，特征头及长度信息可用
* - false	  原始数据无效，特征头及长度信息不可用
*/
bool plc_net_data_process(uint8_t *data_input, uint16_t input_len, uint16_t *head_pos, uint16_t *data_len)
{
	stack<char> stack_temp;
	stack<uint16_t> pos;
	
	uint8_t select_result;
	bool  compare_result;
	char compare_value_temp;
	uint16_t head_pos_temp;
	uint16_t len_temp;

	int i;

	

    if(0x00>=input_len)
    {
        return false;
    }

	
	compare_result = false;

    for(i=0; i<input_len; i++)
    {
        select_result = char_select(data_input[i]);
        if( true == compare_result )
        {
			break;
        }
        
        if(select_result!=0x00)
        {
            
            if(select_result<=0x03)
            {
				stack_temp.push(select_result);
				pos.push(i);
            }
            else
            {
                if(stack_temp.empty())
                {
                    compare_result = false;
                    break;
                }

                compare_value_temp = stack_temp.top();
				
                switch(select_result)
                {
                    case 0x04:
                        if(0x01==compare_value_temp)
                        {
                            stack_temp.pop();
							head_pos_temp = pos.top();
							len_temp = i-head_pos_temp;
                            compare_result = true;
                        }
                        else
                        {
                            compare_result = false;
                        }
                        break;

                    case 0x05:
                        if(0x02==compare_value_temp)
                        {
                            stack_temp.pop();
							head_pos_temp = pos.top();
							len_temp = i-head_pos_temp;
                            compare_result = true;
                        }
                        else
                        {
                            compare_result = false;
                        }
                        break;

                    case 0x06:
                        if(0x03==compare_value_temp)
                        {
                            stack_temp.pop();
							head_pos_temp = pos.top();
							len_temp = i-head_pos_temp;
                            compare_result = true;
                        }
                        else
                        {
                            compare_result = false;
                        }
                        break;

                    default:
						compare_result = false;
                        break;
                }

                 if(false==compare_result)
                {
                    break;
                }

            }

        }
        else
        {
            // do nothing
            continue;
        }

    }

	*head_pos = head_pos_temp;
	*data_len = len_temp-1;


	return compare_result;

	
}




/**@brief     整型转字符串(有长度要求)
* @param[in]  int num   --- 待转换的整型数据
* @param[in]  int len   --- 要求转换的字符串长度
* @return     转换后的字符串
*/
const string int_to_string_format_fixed_length(int num, int len)
{
	string temp;
	
	temp = to_string(num);
		
	while( temp.length()<len )
	{
		temp = "0" + temp;
	}

	return temp;
}



/**@brief     整型转字符串(无长度要求)
* @param[in]  int num   --- 待转换的整型数据
* @return     转换后的字符串
*/
string int_to_string_format(int num)
{
	return  to_string(num);
}



