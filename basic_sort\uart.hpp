/********************************************************************************
* Copyright (C) 2025, JD.COM, Inc.
* All rights reserved.
* FileName    : uart.h
* Author      : wangmenghu   Version: V1.0   Data:2018-08-21
* Description : realize the uart function lib.
********************************************************************************/
#ifndef __UART_H__
#define __UART_H__

#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <termios.h>
#include <errno.h>
#include <stdlib.h>
// �����豸�ĺ������ݽӿ�
typedef struct
{
	char cDateBit;
	char cParity;
	char cStopBit;
	int iBaud;
	char *pUartName;
} uart_attr_t;


#define UART_NAME_RS232     "/dev/ttymxc3"	// ���� RS232��Ӧ���豸�ڵ�
#define UART_NAME_RS485_1   "/dev/ttymxc1"	// ���� RS485��Ӧ���豸�ڵ�
#define UART_NAME_RS485_2   "/dev/ttymxc2"	// ���� RS485��Ӧ���豸�ڵ�

// BAUDRATE
#define     BAUD_1200       1200  
#define     BAUD_2400       2400  
#define     BAUD_4800       4800  
#define     BAUD_9600       9600  
#define     BAUD_19200      19200 
#define     BAUD_38400      38400
#define     BAUD_57600      57600 
#define     BAUD_115200     115200  
#define     BAUD_460800     460800  
// PARITY 
#define     PARITY_ODD    'O' //ODD
#define     PARITY_EVEN   'E' //EVEN
#define     PARITY_NONE   'N' //NONE
// STOP BIT  
#define     STOP_BIT_1     1  
#define     STOP_BIT_2     2  
// DATA BIT  
#define     DATA_BIT_7     7
#define     DATA_BIT_8     8

// ���ڲ����ӿ�
extern int init_uart(uart_attr_t *pUartAttar);
extern size_t recv_uart(int iFd, char *pRcvBuf, ssize_t iLen);
extern int send_uart(int iFd, char *pBuf, int iLen);
extern int close_uart(int iFd);
#endif //__UART_H__