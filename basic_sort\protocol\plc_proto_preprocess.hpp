﻿

/**@file  	   plc_proto_preprocess.hpp
* @brief       PLC通信字符串预处理，实现字符串的分割、转换等操作
* @details     NULL
* <AUTHOR>
* @date        2021-07-10
* @version     v1.0.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.2.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/07/10  <td>1.0.0    <td>lizhy     <td>初始版本                         </tr>
* </table>
*
**********************************************************************************
*/


#ifndef __PROTOCOL_STRING_SPLIT_HPP__
#define __PROTOCOL_STRING_SPLIT_HPP__

#include <iostream>
#include <string>
#include <vector>

using namespace std;


/**@enum PROTOCOL_DATA_STATE
* @brief PLC传输数据解析有效性
*/

typedef enum
{
	PROTOCOL_DATA_VALID = 0,
	PROTOCOL_DATA_INVALID = 1
}PROTOCOL_DATA_STATE;



/**@brief     字符串分割函数，按照给定的分割符号对输入字符串进行分割操作
* @param[in]  const string &str      --- 输入待分割原始字符串
* @param[out] vector<string> *vec    --- 分割完成后输出的 vector
* @param[in]  const string &spl_char --- 指定的字符串分割符
* @return     NULL
*/
extern void string_split(const string &str, vector<string> *vec, const string &spl_char);


/**@brief     特征符号查找与提取函数，用于对原始字符串进行特征匹配查找与提取
* @param[in]  uint8_t *data_input      --- 输入待处理原始字符串
* @param[in]  uint16_t input_len       --- 原始字符串长度
* @param[out] uint16_t *head_pos       --- 匹配查找完成的特征头字符在原始字符串中的位置r
* @param[in]  uint16_t *data_len           --- 有效字符串长度
* @return	  字符串预处理结果	
* - true	  原始数据有效，特征头及长度信息可用
* - false	  原始数据无效，特征头及长度信息不可用
*/
extern bool plc_net_data_process(uint8_t *data_input, uint16_t input_len, uint16_t *head_pos, uint16_t *data_len);



/**@brief     整型转字符串(有长度要求)
* @param[in]  int num   --- 待转换的整型数据
* @param[in]  int len   --- 要求转换的字符串长度
* @return     转换后的字符串
*/
extern const string int_to_string_format_fixed_length(int num, int len);



/**@brief     整型转字符串(无长度要求)
* @param[in]  int num   --- 待转换的整型数据
* @return     转换后的字符串
*/
extern string int_to_string_format(int num);


#endif
