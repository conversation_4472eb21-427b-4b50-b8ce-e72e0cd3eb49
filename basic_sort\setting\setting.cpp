#include <fstream>      // std::ifstream

#include "setting.hpp"

int setting::load_setting(const char *file_name)
{
	int ret;

	SPDLOG_INFO("load setting from:{}", file_name);

	try
	{
		ret = load_thing_agent_setting(file_name);
	}
	catch (nlohmann::detail::exception &e)
	{
		SPDLOG_ERROR("json throw an error:{}", e.what());	
		ret = 0;
	}

	return ret;
}

int setting::load_agent_setting(const char *file_name)
{
    json root;
	
	std::ifstream setting_file(file_name, std::ifstream::in);
	if (!setting_file.is_open())
	{
		SPDLOG_ERROR("Error opening file");
		return 0;
	}
	setting_file >> root;

	settings.device_id = root["feeder"]["id"];
	settings.belt_segment_number = root["feeder"]["belt_segment_num"];
	settings.auto_scanner_id = root["scanner"]["auto_scanner_id"];
	settings.manual_scanner_id = root["scanner"]["manual_scanner_id"];

	settings.sdk_config_host_name = root["sdk_config"]["host_name"];

	settings.sdk_config_host_port = root["sdk_config"]["host_port"];
	settings.sdk_config_device_id = root["sdk_config"]["device_id"];
	settings.sdk_config_ca_path = root["sdk_config"]["ca_path"];
	settings.sdk_config_cert_path = root["sdk_config"]["cert_path"];
	settings.sdk_config_key_path = root["sdk_config"]["key_path"];

	settings.thing_model_id = root["thing_model"]["id"];
	settings.thing_model_version = root["thing_model"]["version"];

	settings.time_query_vehicle_info = root["time"]["query_vehicle_info"];
	settings.time_report_sys_state = root["time"]["report_sys_state"];
	settings.time_report_vehicle_state = root["time"]["report_vehicle_state"];

    return 1;
}

