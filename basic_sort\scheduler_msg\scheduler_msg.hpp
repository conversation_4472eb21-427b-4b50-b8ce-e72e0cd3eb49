﻿
/**@file       scheduler_msg.hpp
* @brief       基于ZMQ的通信二次封装，用来实现plc_agent软件同secheduler软件的进程间通信
* @details     NULL
* <AUTHOR>
* @date        2021-11-29
* @version     v1.3.1
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.3.1
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/07/09  <td>1.0.0    <td>lizhy     <td>初始版本，支持ZMP节点的建立及数据收发              </tr>
* <tr><td>2021/07/15  <td>1.1.0    <td>lizhy     <td>
* -# 将switch和feeder的任务队列分开处理，
* -# 增加switch的任务缓存设置
* <tr><td>2021/08/15  <td>1.2.0    <td>lizhy     <td>
* -# 增加SPDLOG日志信息
* <tr><td>2021/08/18  <td>1.2.1    <td>lizhy     <td>
* -# 增加SPDLOG日志开关
* <tr><td>2021/11/29  <td>1.3.1    <td>lizhy     <td>
* -# 修改STU状态字为FCE状态字，匹配通信协议(1.5.2)版本
* -# 添加按键状态的ZMQ消息发送机制，对应消息发送节点还需要增加设计
* </table>
*
**********************************************************************************
*/




#ifndef __SCHEDULER_MSG_SCHEDULER_MSG_HPP__
#define __SCHEDULER_MSG_SCHEDULER_MSG_HPP__

#include "share/global_def.h"

#include "./blocking_queue/blocking_queue.hpp"


#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"


#include "share/pb/idl/plane_switch_state.pb.h"
#include "share/pb/idl/plane_switch_action.pb.h"
#include "share/pb/idl/plane_slot.pb.h"
#include "share/pb/idl/feeder_goods.pb.h"
#include "share/pb/idl/feeder_state.pb.h"
#include "share/pb/idl/exception.pb.h"
#include "share/pb/idl/data_map.pb.h"

#include "share/pb/idl/plane_hmi.pb.h"
#include "share/pb/idl/sys_state.pb.h"
#include "share/pb/idl/abnormal_slot_state.pb.h"
#include "share/pb/idl/task.pb.h"

#include <vector>
#include <unordered_map>
#include <map>
#include <thread>
#include <mutex>
#include <queue>
#include <condition_variable>
#include <zmq.h>
#include <cppzmq/zmq.hpp>
//#include <zmq.hpp>

#include <iomanip>
#include <string>
#include <sstream>
#include <time.h>
#include <assert.h>
#include <stdarg.h>
#include <signal.h>
#include <unistd.h>




/**@enum __MSG_TYPE
* @brief 定义的需要PUB的消息类型，用来解析后续的数据是何种消息
*/
typedef enum
{
	PLC_SWITCH_STATE_PUB = 0,
	PLC_SLOT_STATE_PUB = 1,
	PLC_EXCEP_PUB = 2, 
	PLC_GOODS_INS_PUB = 3,
	PLC_SWITCH_STATE_TOTAL_PUB = 4,
	PLC_SWITCH_ACTION = 5,
	PLC_FEEDER_ACTION = 6,
	PLC_BUTTON_STATE_PUB = 7,
	PLC_LED_CMD = 8,
	PLC_SYS_MODE = 9,
	PLC_CLEAR_GOOD =10,
	PLC_FEEDER_STATE_PUB =11,
	PLC_ABNORMAL_SLOT_TOTAL_PUB = 12,
	PLC_SORT_TASK_ID = 13,
	PLC_SORT_SORT_INFO_PUB = 14,
	PLC_SORT_CANCEL_PUB = 15,
}__MSG_TYPE;


/**@struct msg_queue
* @brief 定义的用来构成blocking queue的数据结构，使用枚举定义类型，用来指定后续data的解析
*/
typedef struct _msg_queue
{
	/// 任务可执行对象
	__MSG_TYPE type;
	int dev_id;
	/// 任务id
	uint8_t msg_data[1050];
}msg_queue;


#define  IS_SWITCH_DEV_ID_IN_MSG_VALID(n)						( n>0 )


/**
* @brief 基于blocking queue/ZMQ/thread等进行封装，将plc_agent同scheduler的进程间通信进行统一设计
*/
class scheduler_manager
{

public:

	/**@brief	  scheduler_manager class构造函数，在构造列表里构造ZMQ socket
	* @param[in]  zmq::context_t &context ZMQ创建的上下文
	* @return	  NULL
	*/
	explicit scheduler_manager(zmq::context_t &context);

	/**@brief	  scheduler_manager class析构函数
	* @param[in]  NULL
	* @return	  NULL
	*/
	~scheduler_manager();

	/**@brief     scheduler_manager 初始化函数，对使用到的ZMQ socket进行初始化
	* @param[in]  NULL
	* @return     函数执行结果
	* - true      server创建成功
	*/
	bool scheduler_manager_init(void);

	/**@brief	  scheduler_manager 日志系统初始化
	* @param[in]  bool &opt 	---  日志系统开关设置
	* @return	  NULl
	*/
	void scheduler_manager_log_init(bool &opt);

	/**@brief     scheduler_manager 运行函数，创建线程并运行
	* @param[in]  NULL
	* @return     函数执行结果
	* - true      server创建成功	
	*/
	bool scheduler_manager_run(void); 


	/**@brief	  单个switch状态变化消息的发布函数，基于ZMQ 的PUB-SUB模式
	* @param[in]  switch_state_single *dev_state --- 待操作的switch状态数据结构体指针
	* @return	  操作结构
	* - true	  发布成功
	* - false	  发布失败
	*/	
	bool scheduler_manager_plc_switch_state_pub_single(switch_state_single *dev_state); 


	/**@brief     所有switch状态变化消息的发布函数，基于ZMQ 的PUB-SUB模式
	* @param[in]  switch_state_multiple *dev_state --- 待操作的switch状态数据结构体指针
	* @return     操作结构
	* - true      发布成功
	* - false     发布失败
	*/
	bool scheduler_manager_plc_switch_state_pub_total(switch_state_multiple *dev_state);


	/**@brief     供包机状态变化消息的发布函数，基于ZMQ 的PUB-SUB模式
	* @param[in]  slot_state *dev_state --- 待操作的格口状态数据结构体指针
	* @return     操作结构
	* - true      发布成功
	* - false     发布失败
	*/
	bool scheduler_manager_plc_feeder_state_pub(feeder_dev_state_total *dev_state);
    //bool scheduler_manager_plc_feeder_state_pub(scanner_state *dev_state);
	/**@brief     按键状态变化消息的发布函数，基于ZMQ 的PUB-SUB模式
	* @param[in]  int *dev_state --- 按键状态数据指针
	* @return     操作结构
	* - true      发布成功
	* - false     发布失败	
	*/
	bool scheduler_manager_plc_button_state_pub(int *dev_state);

	/**@brief     商品扫码信息的发布函数，基于ZMQ 的PUB-SUB模式
	* @param[in]  sorting_task_msg *info --- 商品信息结构体
	* @return     操作结构
	* - true      发布成功
	* - false     发布失败
	*/
	bool scheduler_manager_plc_goods_info_pub(sorting_task_msg *info);

    bool scheduler_manager_plc_sort_info_pub(sorting_task_msg *info);

	bool scheduler_manager_plc_sort_info_pub(sorting_task_state_msg *info) ;

	/**@brief     PLC设备异常状态的发布函数，基于ZMQ 的PUB-SUB模式
	* @param[in]  exception_info *exce --- 异常消息结构体
	* @return     操作结构
	* - true      发布成功
	* - false     发布失败
	*/
	bool scheduler_manager_plc_exception_pub(event_exception *exce);


	/**@brief	  同调度软件通信的线程函数，响应调度端发布的任务并存储至对应的blocking queue中，将网络端接收到的设备状态解析后的状态信息pop并发布出去
	* @param[in]  NULL
	* @return	  操作结构
	* - true	  成功
	* - false	  失败
	*/
	bool scheduler_manager_scheduler_thread(void);
	bool scheduler_manager_scheduler_thread2(void);
	bool scheduler_manager_scheduler_thread3(void);
	bool scheduler_manager_scheduler_vehicle(void);
    bool scheduler_manager_scheduler_vehicle2(void);
	

	/**@brief     从database获取当前的设备map信息，用来解析并获取所有switch的信息
	* @param[out]  data_map *map --- 获取的map信息数据
	* @return     操作结构
	* - true      获取成功
	* - false     获取失败
	*/
	bool scheduler_manager_get_plc_switch_info(          data_map *map);

	/**@brief     blocking queue push函数的二次封装，避免直接访问成员变量
	* @param[in]  msg_queue &data --- 待操作的数据
	* @return     NULL
	*/
	void scheduler_manager_scheduler_msg_queue_push(msg_queue &data);


	/**@brief     blocking queue empty函数的二次封装，避免直接访问成员变量。用来查询队列的空满状态
	* @param[in]  NULL
	* @return     队列空满状态
	* - true      blocking queue为空
	* - false     blocking queue非空
	*/
	bool scheduler_manager_scheduler_msg_queue_empty(void);


	/**@brief     blocking queue pop函数的二次封装，避免直接访问成员变量
	* @param[out]  msg_queue *data --- 待操作的数据指针
	* @return     NULL
	*/
	void scheduler_manager_scheduler_msg_pop(msg_queue *data);


	/**@brief	  blocking queue empty函数的二次封装，避免直接访问成员变量。用来查询队列的空满状态
	* @param[in]  NULL
	* @return	  队列空满状态
	* - true	  blocking queue为空
	* - false	  blocking queue非空
	*/
	bool scheduler_manager_feeder_task_msg_queue_empty(void);	


	/**@brief	  blocking queue pop函数的二次封装，避免直接访问成员变量
	* @param[out]  msg_queue *data --- 待操作的数据指针
	* @return	  NULL
	*/
	void scheduler_manager_feeder_task_msg_pop(msg_queue *data);


	/**@brief	  blocking queue empty函数的二次封装，避免直接访问成员变量。用来查询switch open 任务队列的空满状态
	* @param[in]  NULL
	* @return	  队列空满状态
	* - true	  blocking queue为空
	* - false	  blocking queue非空
	*/
	bool scheduler_manager_switch_open_task_msg_queue_empty(void);


	/**@brief	  blocking queue pop函数的二次封装，避免直接访问成员变量
	* @param[out] NULL
	* @return	  待open的switch id
	*/
	int scheduler_manager_switch_open_task_msg_pop(void);


	/**@brief     blocking queue empty函数的二次封装，避免直接访问成员变量。用来查询switch open队列当前深度
	* @param[in]  NULL
	* @return     队列深度
	*/
	int scheduler_manager_get_switch_open_task_msg_size(void);


	/**@brief     blocking queue empty函数的二次封装，避免直接访问成员变量。用来查询switch close 任务队列的空满状态
	* @param[in]  NULL
	* @return     队列空满状态	
	* - true      blocking queue为空
	* - false     blocking queue非空
	*/
	bool scheduler_manager_switch_close_task_msg_queue_empty(void);


	/**@brief	  blocking queue pop函数的二次封装，避免直接访问成员变量
	* @param[out] NULL
	* @return	  待open的switch id
	*/
	int scheduler_manager_switch_close_task_msg_pop(void);


	/**@brief	  blocking queue empty函数的二次封装，避免直接访问成员变量。用来查询switch close队列当前深度
	* @param[in]  NULL
	* @return	  队列深度
	*/
	int scheduler_manager_get_switch_close_task_msg_size(void);
	

	int scheduler_manager_switch_open_task_msg_push(int dev);

	bool scheduler_manager_get_led_cmd(void);
	bool scheduler_manager_plc_button_state_pub(key_event *dev_state);

	bool scheduler_manager_get_system_state(void);

	bool scheduler_manager_plc_abnormal_slot_total_pub(abnormal_slot_list *dev_state);
	
    bool scheduler_manager_get_sort_task_flag(uint8_t id)
	{
		return m_sort_task_flag[id];
	}

	bool scheduler_manager_set_sort_task_flag(uint8_t id)
	{
		 m_sort_task_flag[id] = 0;
		 return 0;
	}

	bool scheduler_manager_get_sort_cancel_flag(uint8_t id)
	{
		return m_sort_task_flag[id];
	}

	bool scheduler_manager_set_sort_cancel_flag(uint8_t id)
	{
		 m_sort_task_flag[id] = 0;
		 return 0;
	}

	bool scheduler_manager_set_req_task_queue(uint8_t id,char *req_task)
	{
		strcpy(m_req_task_queue[id] , req_task);
		 return 0;
	}
	uint32_t scheduler_crc_task_info(uint32_t pos);

	bool supply_cmd_flag[2] ={0,0} ;
	bool sort_cmd_flag ;
	std::atomic<bool> m_sort_task_flag[2];
	std::atomic<bool> m_sort_cancel_flag[2];
	// 供包机接收到的请求分播信息
	sorting_task_msg m_sort_task_recv[2];
	// 供包机第四段皮带分播信息
	sorting_task_msg m_sort_action_recv[2];

	std::mutex m_sort_action_lock;

	sorting_task_msg sorting_action_record[2];

	e_wkstate m_state;

    std::mutex m_vehicle_lock;
	//std::unordered_map<uint32_t, uint32_t > m_vehicle_map;
	std::map<uint32_t, uint32_t > m_vehicle_map;
	std::shared_ptr<spdlog::logger> sub_logger;

	//std::unor <int32_t>  m_ctn_forbid0;
	//std::vector <int32_t>  m_ctn_forbid1;

	//std::unordered_map<int, state> m_ctn_forbid0;
	//std::unordered_map<int, container_seal_state> m_ctn_forbid1;

private:

	zmq::socket_t m_plc_switch_state_publisher;  ///< switch状态发布 PUB 类型
	zmq::socket_t m_plc_feeder_state_publisher;    ///< 格口状态发布 PUB 类型
	zmq::socket_t m_plc_goods_info_publisher;    ///< 商品扫码信息发布 PUB 类型
	zmq::socket_t m_plc_exception_publisher;     ///< 设备异常状态发布 PUB 类型
	zmq::socket_t m_plc_abslot_state_publisher;  ///< 异常格口状态发布 PUB 类型

	zmq::socket_t m_sort_task_cancel_publisher;

	zmq::socket_t m_plc_sort_info_publisher;    ///< 商品扫码信息发布 PUB 类型

	zmq::socket_t m_data_requester;              ///< switch设备信息获取 REQ 类型

	zmq::socket_t m_switch_replayer;             ///< switch任务获取     REP类型 
	zmq::socket_t m_feeder_replayer;             ///< feeder任务获取     REP类型 
    // add by ljl 20220714
	zmq::socket_t m_sort_task_replayer;          ///< sort 分播任务获取     REP类型 

	zmq::socket_t m_sort_action_replayer;         ///< sort 分播任务获取     REP类型 

	zmq::socket_t m_plc_button_state_publisher;     ///< 按键状态发布 PUB 类型
	zmq::socket_t m_plc_led_cmd_subscriber;      ///< LED及蜂鸣器命令获取 SUB 类型

	zmq::socket_t m_plc_sys_state_subscriber;      ///< 系统状态获取 SUB 类型

	zmq::socket_t m_slot_state_sub;  
	zmq::socket_t m_seal_state_sub; 

	//zmq::socket_t m_plc_sys_state_subscriber;      ///< 系统状态获取 SUB 类型
	zmq::socket_t m_vehicle_task_subscriber;
	zmq::socket_t m_vehicle_task_state_subscriber;
    //zmq::detail::socket_base::recv
	std::thread *scheduler_msg;					 ///< thread指针 
	std::thread *scheduler_msg2;				 ///< thread指针 
	std::thread *scheduler_msg3;				 ///< thread指针 
	std::thread *scheduler_vehicle_msg;			 ///< thread指针
	std::thread *scheduler_vehicle_msg2;			 ///< thread指针


	blocking_queue<msg_queue> m_scheduler_msg_queue;   ///< secheduler消息传递队列 

	blocking_queue<int> m_switch_open_task_msg;        ///< switch open 任务队列 
	blocking_queue<int> m_switch_close_task_msg;       ///< switch close 任务队列
	
	blocking_queue<msg_queue> m_feeder_task_msg;       ///< 供包台任务队列 
    blocking_queue<msg_queue> m_sort_task_msg; 
	bool m_zmq_log_switch;
    char m_req_task_queue[2][36];
};

#endif
