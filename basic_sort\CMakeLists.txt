cmake_minimum_required(VERSION 3.5)

SET( CROSS_COMPILE ON )

SET(CMAKE_SYSTEM_NAME Linux)

if(CROSS_COMPILE)
	SET(CMAKE_C_COMPILER "/usr/bin/arm-linux-gnueabihf-gcc")
	SET(CMAKE_CXX_COMPILER "/usr/bin/arm-linux-gnueabihf-g++")
	link_directories("../share/libs/arm/lib")
	include_directories("../share/libs/arm/include")
else()
	SET(CMAKE_C_COMPILER "/usr/bin/gcc")
	SET(CMAKE_CXX_COMPILER "/usr/bin/g++")
	link_directories("../share/libs/x86/lib")
	include_directories("../share/libs/x86/include")
	SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -m64")
endif()


project(basic_sort LANGUAGES CXX C)

set(CMAKE_CXX_STANDARD 11)

set(CMAKE_CXX_STANDARD_REQUIRED ON)

SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -pthread ")


add_definitions(-D UNIT_TEST)
#add_definitions(-Wall -g)

include_directories("../")

include_directories("../share/pb/nanopb" ".")

include_directories("../share/libs/include")


link_libraries(spdlog zmq)

add_subdirectory("../share/pb/nanopb" nanopb_binary_dir)
add_subdirectory("../share/pb/idl" idl_binary_dir)

add_subdirectory("../share/lwshell" lwshell_binary_dir)

#添加阻塞队列库
add_subdirectory( blocking_queue )
aux_source_directory( blocking_queue QUEUE )
#添加网络库
add_subdirectory(net)
aux_source_directory(net NET)
#添加协议库
add_subdirectory(protocol)
aux_source_directory(protocol PROTOCOL)
#添加管理模块
add_subdirectory(plc_manage)
aux_source_directory(plc_manage  PLC_MANAGE)
#ZMQ消息模块
add_subdirectory(scheduler_msg)
aux_source_directory(scheduler_msg  SCHEDULER_MSG)

add_subdirectory(feeder)
aux_source_directory(feeder  FEEDER_MSG)

add_subdirectory(diagnose)
aux_source_directory(diagnose SHELL_MSG)

add_subdirectory(slot_manager)
aux_source_directory(slot_manager SLOT_MSG)

#默认当前文件夹下所有文件均参与编译
aux_source_directory(. DIR_SRCS)

#生成所需文件
#add_executable(vehicle_agent ${DIR_SRCS} ${THREADPOOL} ${NET} ${PROTOCOL} ${VEHICLE_MANAGE} ${SCHEDULER_MSG} )
#add_executable( plc_agent ${DIR_SRCS} ${NET} ${QUEUE} ${PROTOCOL}  ${PLC_MANAGE} ${SCHEDULER_MSG}  )

#添加外部库依赖
#target_link_libraries(plc_agent nanopb idl lib_net lib_queue lib_protocol lib_plc_manage lib_msg )

#add_executable(vehicle_agent ${DIR_SRCS} ${THREADPOOL} ${NET} ${PROTOCOL} ${VEHICLE_MANAGE} ${SCHEDULER_MSG} )
add_executable( basic_sort ${DIR_SRCS} ${NET} ${QUEUE} ${PROTOCOL}  ${PLC_MANAGE} ${SCHEDULER_MSG} ${FEEDER_MSG} ${SHELL_MSG} ${SLOT_MSG})

#添加外部库依赖
target_link_libraries(basic_sort nanopb idl lib_net lib_queue lib_protocol lib_plc_manage lib_msg lib_servo diagnose lwshell lib_slot_manager)