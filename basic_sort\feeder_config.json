{"server": {"ip_addr": "0.0.0.0", "port": 3000, "udp_port": 3001}, "log": {"log_level": "info", "net_log": "on", "protocol_log": "on", "zmq_log": "on", "dev_log": "on", "heartbeat_log": "on"}, "mode": {"ctrl_mode": 1}, "feeder": {"// feeder_dev_addr_info": "uart dev addr", "scan_dev1_addr": "/dev/ttysWK0", "scan_dev2_addr": "/dev/ttysWK1", "// the num of feeder": "//feeder num", "feeder_count": 1, "// the num of belt ": "===//belt num", "belt_count": 3, "// feeder belt run dir ": " ", "first_feeder_dir": 1, "second_feeder_dir": 1, "// double_feeder_scan_info": "//1 first scan;2 second scan", "double_feeder_scan": 1, "//feeder has virtual belt": " ", "feeder_belt_virtual": 0, "// feeder change container id,when back": " ", "cancel_container_change": 1, "cancel_container_id": 20101, "code_count": 10000, "// feeder belt speed,RPM": " ", "belt1_spd": 9000, "belt2_spd": 15000, "belt3_spd": 9000, "belt4_spd": 9000, "// belt_acc_info": "//belt acc, xxx ms acc 3000RPM", "belt_acc": 300, "belt_dcc": 300, "// ratio info": "//ratio value", "belt1_ratio": 2.0, "belt2_ratio": 4.4, "belt3_ratio": 2.0, "belt4_ratio": 2.0, "// belt diameter info :mm": ":mm", "belt1_diameter": 35, "belt2_diameter": 35, "belt3_diameter": 35, "belt4_diameter": 35, "// belt length :mm": ":mm", "belt1_len": 1567, "belt2_len": 580, "belt3_len": 580, "belt4_len": 580, "// goods position of stoping  on half of belt,under xxx :mm": ":mm", "belt1_dis": -50, "belt2_dis": -50, "belt3_dis": -80, "belt4_dis": -50, "// goods must stop befor  belt length - xxx , :mm": ":mm", "belt1_dis_stop": -120, "belt2_dis_stop": -120, "belt3_dis_stop": -120, "belt4_dis_stop": -120, "isuse_fuse": 1, "// 20230613 add": "weight ,volume", "weight_enable": 0, "weight_max": 5000, "// volume info ": "weight ,volume", "volume_enable": 0, "wide_max": 500, "high_max": 400, "len_max": 500, "// goods lenght max ": "weight ,volume", "goods_len_max": 500, "// volume metering ,key trigger": "volume", "volume_key": 0, "auto_scan_static": 0, "sku_code_len": 256, "// 'noread' sku and stop,": "", "noread_stop": 0, "// 20230922 add weight min": "", "weight_min": 30}}