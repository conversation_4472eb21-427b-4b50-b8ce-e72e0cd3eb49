﻿
/**@file       scheduler_msg.cpp
* @brief       基于ZMQ的通信二次封装，用来实现plc_agent软件同secheduler软件的进程间通信
* @details     NULL
* <AUTHOR>
* @date        2021-11-29
* @version     v1.3.1
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.3.1
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/07/09  <td>1.0.0    <td>lizhy     <td>初始版本，支持ZMP节点的建立及数据收发              </tr>
* <tr><td>2021/07/15  <td>1.1.0    <td>lizhy     <td>
* -# 将switch和feeder的任务队列分开处理，
* -# 增加switch的任务缓存设置
* <tr><td>2021/08/15  <td>1.2.0    <td>lizhy     <td>
* -# 增加SPDLOG日志信息
* <tr><td>2021/08/18  <td>1.2.1    <td>lizhy     <td>
* -# 增加SPDLOG日志开关
* <tr><td>2021/11/29  <td>1.3.1    <td>lizhy     <td>
* -# 修改STU状态字为FCE状态字，匹配通信协议(1.5.2)版本
* -# 添加按键状态的ZMQ消息发送机制，对应消息发送节点还需要增加设计
* </table>
*
**********************************************************************************
*/

#include "../plc_agent_debug.h"
#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>



#include "share/global_def.h"

#include "./blocking_queue/blocking_queue.hpp"


#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"

#include "../plc_manage/plc_manage.hpp"

#include "share/pb/idl/plane_switch_state.pb.h"
#include "share/pb/idl/plane_switch_action.pb.h"
#include "share/pb/idl/plane_slot.pb.h"
#include "share/pb/idl/feeder_goods.pb.h"
#include "share/pb/idl/feeder_cmd.pb.h"
#include "share/pb/idl/feeder_state.pb.h"
#include "share/pb/idl/exception.pb.h"
#include "share/pb/idl/data_request.pb.h"
#include "share/pb/idl/ack.pb.h"

#include "share/pb/idl/data_map.pb.h"

#include "share/pb/idl/plane_hmi.pb.h"
#include "share/pb/idl/task.pb.h"

#include "share/pb/idl/sys_state.pb.h"
#include "share/pb/idl/abnormal_slot_state.pb.h"

#include "share/pb/idl/vehicle_task.pb.h"
#include "share/pb/idl/vehicle_state.pb.h"


#include "scheduler_msg.hpp"

#include <fcntl.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <errno.h>
#include <iostream>
#include <stdexcept>
#include <cassert>
#include <cstring>


#include <vector>
#include <thread>
#include <mutex>
#include <queue>
#include <condition_variable>
#include <zmq.h>
//#include <cppzmq/zmq.hpp>
//#include <cppzmq/zmq_addon.hpp>
//#include <zmq.hpp>

#include <iomanip>
#include <string>
#include <sstream>
#include <time.h>
#include <assert.h>
#include <stdarg.h>
#include <signal.h>
#include <unistd.h>



/**@brief     scheduler_manager class构造函数，在构造列表里构造ZMQ socket
* @param[in]  zmq::context_t &context ZMQ创建的上下文
* @return     NULL
*/
scheduler_manager::scheduler_manager(zmq::context_t &context)
:m_plc_switch_state_publisher(zmq::socket_t(context, ZMQ_PUB))
,m_plc_feeder_state_publisher(zmq::socket_t(context, ZMQ_PUB))
,m_plc_goods_info_publisher(zmq::socket_t(context, ZMQ_PUB))
,m_sort_task_cancel_publisher(zmq::socket_t(context, ZMQ_PUB))
,m_plc_exception_publisher(zmq::socket_t(context, ZMQ_PUB))
,m_plc_sort_info_publisher(zmq::socket_t(context, ZMQ_PUB))
,m_data_requester(zmq::socket_t(context, ZMQ_REQ))
,m_switch_replayer(zmq::socket_t(context, ZMQ_REP))
,m_feeder_replayer(zmq::socket_t(context, ZMQ_REP))
,m_sort_task_replayer(zmq::socket_t(context, ZMQ_REP))
,m_sort_action_replayer(zmq::socket_t(context, ZMQ_REP))
,m_plc_button_state_publisher(zmq::socket_t(context, ZMQ_PUB))
,m_plc_led_cmd_subscriber(zmq::socket_t(context, ZMQ_SUB))
,m_plc_sys_state_subscriber(zmq::socket_t(context, ZMQ_SUB))
,m_plc_abslot_state_publisher(zmq::socket_t(context, ZMQ_PUB))
,m_vehicle_task_subscriber(zmq::socket_t(context, ZMQ_SUB))
,m_vehicle_task_state_subscriber(zmq::socket_t(context, ZMQ_SUB))
,m_zmq_log_switch(true)
{
	m_scheduler_msg_queue.clear();
	
	return ;
}



/**@brief     scheduler_manager class析构函数
* @param[in]  NULL
* @return     NULL
*/
scheduler_manager::~scheduler_manager() 
{

}


/**@brief     scheduler_manager 初始化函数，对使用到的ZMQ socket进行初始化
* @param[in]  NULL
* @return     函数执行结果
* - true      server创建成功
*/
bool scheduler_manager::scheduler_manager_init() 
{
	//int timeout = 1000;
	
	m_plc_feeder_state_publisher.bind(TOPIC_FEEDER_BASICK_SORT_STATE);	
	m_plc_goods_info_publisher.bind(TOPIC_FEEDER_BASICK_SORT_GOODS);
	m_plc_exception_publisher.bind(TOPIC_EXCEPTION_FEEDER_BASICK_SORT);

	m_sort_task_cancel_publisher.connect(TOPIC_TASK_STATE);

	m_plc_sys_state_subscriber.connect(TOPIC_SYS_STATE);	
	m_plc_sys_state_subscriber.setsockopt( ZMQ_SUBSCRIBE, "", 0);

	m_sort_task_replayer.bind(SERVICE_TASK);

	
    
	SPDLOG_INFO("[ZMQ] scheduler_manager_init ok");

	return true;
}


/**@brief     scheduler_manager 日志系统初始化
* @param[in]  bool &opt     ---  日志系统开关设置
* @return     NULl
*/
void scheduler_manager::scheduler_manager_log_init(bool &opt) 
{

	m_zmq_log_switch = opt;
	
	SPDLOG_INFO("[ZMQ] scheduler_manager_log_init :{} / :{} ", opt, m_zmq_log_switch);
}



/**@brief     scheduler_manager 运行函数，创建线程并运行
* @param[in]  NULL
* @return     函数执行结果
* - true      server创建成功
*/
bool scheduler_manager::scheduler_manager_run(void) 
{
	scheduler_msg = new std::thread(&scheduler_manager::scheduler_manager_scheduler_thread, this);
	//scheduler_msg2 = new std::thread(&scheduler_manager::scheduler_manager_scheduler_thread2, this);
	//scheduler_msg3 = new std::thread(&scheduler_manager::scheduler_manager_scheduler_thread3, this);
	//scheduler_vehicle_msg = new std::thread(&scheduler_manager::scheduler_manager_scheduler_vehicle, this);
	//scheduler_vehicle_msg2 = new std::thread(&scheduler_manager::scheduler_manager_scheduler_vehicle2, this);
	//scheduler_msg->detach();
    return true;
}


/**@brief     单个switch状态变化消息的发布函数，基于ZMQ 的PUB-SUB模式
* @param[in]  switch_state_single *dev_state --- 待操作的switch状态数据结构体指针
* @return     操作结构
* - true      发布成功
* - false     发布失败
*/
bool scheduler_manager::scheduler_manager_plc_switch_state_pub_single(switch_state_single *dev_state) 
{
	uint8_t state_msg[256];
	pb_ostream_t stream_out;
	//int ret = 0;
	switch_state_multiple dev_state_temp;

	dev_state_temp.switches_count = 1;
	memcpy(&dev_state_temp.switches[0], dev_state, sizeof(switch_state_single));
	
	stream_out = pb_ostream_from_buffer(state_msg, sizeof(state_msg));

	if(m_zmq_log_switch)
	{
		SPDLOG_INFO("[ZMQ] scheduler_manager_plc_switch_state_pub_single ");
		SPDLOG_INFO("[ZMQ] singel state id:{} / pos:{} / state:{} / err:{}",dev_state->switch_id, dev_state->position, dev_state->state, dev_state->err_code );
	}
	

	if( 0x00!=stream_out.bytes_written )
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: msg init fail\n\n"<< std::endl;
#endif

		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] msg init fail ");
		}			
		return false;

	}
	
	if (!pb_encode(&stream_out, switch_state_multiple_fields, &dev_state_temp))
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: pb_encode fail\n\n"<< std::endl;
#endif

		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] pb_encode fail ");
		}	
		return false;
	}
	else
	{
		m_plc_switch_state_publisher.send(zmq::buffer(state_msg, stream_out.bytes_written), zmq::send_flags::none);
	}
	
#ifdef SCHEDULER_MSG_DEBUG
	OUTPUT_LOG;
	std::cout << "log>>: msg m_plc_switch_state_publisher send\n\n"<< std::endl;
#endif

	if(m_zmq_log_switch)
	{
		SPDLOG_INFO("[ZMQ] scheduler_manager_plc_switch_state_pub_single send finish ");
	}

	return true;	
}



/**@brief     所有switch状态变化消息的发布函数，基于ZMQ 的PUB-SUB模式
* @param[in]  switch_state_multiple *dev_state --- 待操作的switch状态数据结构体指针
* @return     操作结构
* - true      发布成功
* - false     发布失败
*/
bool scheduler_manager::scheduler_manager_plc_switch_state_pub_total(switch_state_multiple *dev_state) 
{
	uint8_t state_msg[1024];
	pb_ostream_t stream_out;
	//int ret = 0;
	int i;
	zmq::send_result_t result_temp;

	if(m_zmq_log_switch)
	{
		// ljl SPDLOG_INFO("[ZMQ] scheduler_manager_plc_switch_state_pub_total ");
		for(i=0; i<dev_state->switches_count; i++)
		{
			SPDLOG_INFO("[ZMQ] switch multi:{}  id:{} / pos:{} / state:{} / err:{}",i, dev_state->switches[i].switch_id, dev_state->switches[i].position, dev_state->switches[i].state, dev_state->switches[i].err_code );
		}
	}		


	stream_out = pb_ostream_from_buffer(state_msg, sizeof(state_msg));

	if( 0x00!=stream_out.bytes_written )
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: msg init fail\n\n"<< std::endl;
#endif

		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] msg init fail ");
		}
		return false;

	}
	
	if (!pb_encode(&stream_out, switch_state_multiple_fields, dev_state))
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: pb_encode fail\n\n"<< std::endl;
#endif

		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] pb_encode fail ");
		}	
		return false;
	}
	else
	{
		result_temp = m_plc_switch_state_publisher.send(zmq::buffer(state_msg, stream_out.bytes_written), zmq::send_flags::none);
	}
	
#ifdef SCHEDULER_MSG_DEBUG
	OUTPUT_LOG;
	std::cout << "log>>: msg m_plc_switch_state_publisher send\n\n"<< std::endl;
#endif

	if(m_zmq_log_switch)
	{
		// ljl SPDLOG_INFO("[ZMQ] scheduler_manager_plc_switch_state_pub_total send finish :{} :{}", result_temp.has_value(), result_temp.value());
	}

	return true;	
}


/**@brief     格口状态变化消息的发布函数，基于ZMQ 的PUB-SUB模式
* @param[in]  slot_state *dev_state --- 待操作的格口状态数据结构体指针
* @return     操作结构
* - true      发布成功
* - false     发布失败
*/

bool scheduler_manager::scheduler_manager_plc_feeder_state_pub(feeder_dev_state_total *dev_state) 
//bool scheduler_manager::scheduler_manager_plc_feeder_state_pub(scanner_state *dev_state)
{
	uint8_t state_msg[feeder_dev_state_total_size ];
	pb_ostream_t stream_out;
	//int ret = 0;

	stream_out = pb_ostream_from_buffer(state_msg, sizeof(state_msg));

#ifdef PLC_ZMQ_LOG
	//SPDLOG_INFO("[ZMQ] scheduler_manager_plc_feeder_state_pub,length {}",stream_out.bytes_written);
	//SPDLOG_INFO("[ZMQ] slot state id:{} / state:{}",dev_state->id, dev_state->st );
#endif

	if( 0x00!=stream_out.bytes_written )
	{
		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] msg init fail ");
		}

		return false;

	}
	
	if (!pb_encode(&stream_out, feeder_dev_state_total_fields, dev_state))
	//if (!pb_encode(&stream_out,scanner_state_fields, dev_state))
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: pb_encode fail\n\n"<< std::endl;
#endif
		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] pb_encode fail ");
		}	
		//m_plc_feeder_state_publisher.send(zmq::buffer(state_msg, stream_out.bytes_written), zmq::send_flags::none);
		return false;
	}
	else
	{
		m_plc_feeder_state_publisher.send(zmq::buffer(state_msg, stream_out.bytes_written), zmq::send_flags::none);
	}
	
#ifdef SCHEDULER_MSG_DEBUG
	OUTPUT_LOG;
	std::cout << "log>>: msg scheduler_manager_plc_slot_state_pub send\n\n"<< std::endl;
#endif

	if(m_zmq_log_switch)
	{
		SPDLOG_INFO("[ZMQ] msg feeder state except pub, send finish ");
	}
	
	return true;	
}


/**@brief     按键状态变化消息的发布函数，基于ZMQ 的PUB-SUB模式
* @param[in]  int *dev_state --- 按键状态数据指针
* @return     操作结构
* - true      发布成功
* - false     发布失败
*/
bool scheduler_manager::scheduler_manager_plc_button_state_pub(int *dev_state) 
{
	uint8_t state_msg[80];
	pb_ostream_t stream_out;
	//int ret = 0;
	
	stream_out = pb_ostream_from_buffer(state_msg, sizeof(state_msg));

#ifdef PLC_ZMQ_LOG
	SPDLOG_INFO("[ZMQ] scheduler_manager_plc_button_state_pub ");
	SPDLOG_INFO("[ZMQ] slot state state:{}", *dev_state);
#endif


	if( 0x00!=stream_out.bytes_written )
	{

		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] msg init fail ");
		}

		return false;

	}
	
	if (!pb_encode(&stream_out, slot_state_fields, dev_state))
	{

		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] pb_encode fail ");
		}	
		
		return false;
	}
	else
	{
		// TBD 20211129
		//m_plc_slot_state_publisher.send(zmq::buffer(state_msg, stream_out.bytes_written), zmq::send_flags::none);
	}
	
	if(m_zmq_log_switch)
	{
		SPDLOG_INFO("[ZMQ] msg scheduler_manager_plc_button_state_pub send finish ");
	}
	
	return true;	
}



/**@brief     商品扫码信息的发布函数，基于ZMQ 的PUB-SUB模式
* @param[in]  goods_info *info --- 商品信息结构体
* @return     操作结构
* - true      发布成功
* - false     发布失败
*/
bool scheduler_manager::scheduler_manager_plc_goods_info_pub(sorting_task_msg *info) 
{
	uint8_t state_msg[256];
	pb_ostream_t stream_out;
	//int ret = 0;
	
	stream_out = pb_ostream_from_buffer(state_msg, sizeof(state_msg));

	if(m_zmq_log_switch)
	{
		SPDLOG_INFO("[ZMQ] scheduler_manager_plc_goods_info_pub ");
		//SPDLOG_INFO("[ZMQ] slot state id:{} / pos:{} / sku:{}",info->scanner_id, info->position, info->codes );
	}

	if( 0x00!=stream_out.bytes_written )
	{
		if(1)
		{
			SPDLOG_INFO("[ZMQ] msg init fail ");
		}
		
		return false;

	}
	
	if (!pb_encode(&stream_out, sorting_task_msg_fields, info))
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: pb_encode fail\n\n"<< std::endl;
#endif
		if(1)
		{
			SPDLOG_INFO("[ZMQ] pb_encode fail ");
		}	
		
		return false;
	}
	else
	{
		m_plc_goods_info_publisher.send(zmq::buffer(state_msg, stream_out.bytes_written), zmq::send_flags::none);
	}
	
	if(1)
	{
		SPDLOG_INFO("[ZMQ] msg scheduler_manager_plc_goods_info_pub send finish ");
	}
	
	return true;	
}

/**@brief     商品扫码信息的发布函数，基于ZMQ 的PUB-SUB模式
* @param[in]  goods_info *info --- 商品信息结构体
* @return     操作结构
* - true      发布成功
* - false     发布失败
*/
bool scheduler_manager::scheduler_manager_plc_sort_info_pub(sorting_task_msg *info) 
{
	uint8_t state_msg[256];
	pb_ostream_t stream_out;
	//int ret = 0;
	
	stream_out = pb_ostream_from_buffer(state_msg, sizeof(state_msg));

	if(m_zmq_log_switch)
	{
		SPDLOG_INFO("[ZMQ] scheduler_manager_plc_goods_info_pub ");
		//SPDLOG_INFO("[ZMQ] slot state id:{} / pos:{} / sku:{}",info->scanner_id, info->position, info->codes );
	}

	if( 0x00!=stream_out.bytes_written )
	{
		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] msg init fail ");
		}
		
		return false;

	}
	
	if (!pb_encode(&stream_out, goods_info_fields, info))
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: pb_encode fail\n\n"<< std::endl;
#endif
		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] pb_encode fail ");
		}	
		
		return false;
	}
	else
	{
		m_plc_sort_info_publisher.send(zmq::buffer(state_msg, stream_out.bytes_written), zmq::send_flags::none);
	}
	
	if(m_zmq_log_switch)
	{
		SPDLOG_INFO("[ZMQ] msg scheduler_manager_plc_goods_info_pub send finish ");
	}
	
	return true;	
}

bool scheduler_manager::scheduler_manager_plc_sort_info_pub(sorting_task_state_msg *info) 
{
	uint8_t state_msg[256];
	pb_ostream_t stream_out;
	//int ret = 0;
	
	stream_out = pb_ostream_from_buffer(state_msg, sizeof(state_msg));

	if(m_zmq_log_switch)
	{
		SPDLOG_INFO("[ZMQ] scheduler_manager_plc_goods_info_pub ");
		//SPDLOG_INFO("[ZMQ] slot state id:{} / pos:{} / sku:{}",info->scanner_id, info->position, info->codes );
	}

	if( 0x00!=stream_out.bytes_written )
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: msg init fail\n\n"<< std::endl;
#endif
		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] msg init fail ");
		}
		
		return false;

	}
	
	if (!pb_encode(&stream_out, sorting_task_state_msg_fields, info))
	{
		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] pb_encode fail ");
		}	
		
		return false;
	}
	else
	{
		m_sort_task_cancel_publisher.send(zmq::buffer(state_msg, stream_out.bytes_written), zmq::send_flags::none);
	}
	
#ifdef SCHEDULER_MSG_DEBUG
	OUTPUT_LOG;
	std::cout << "log>>: msg scheduler_manager_plc_goods_info_pub send\n\n"<< std::endl;
#endif

	if(m_zmq_log_switch)
	{
		SPDLOG_INFO("[ZMQ] msg scheduler_manager_plc_goods_info_pub send finish ");
	}
	
	return true;	
}
/**@brief     PLC设备异常状态的发布函数，基于ZMQ 的PUB-SUB模式
* @param[in]  exception_info *exce --- 异常消息结构体
* @return     操作结构
* - true      发布成功
* - false     发布失败
*/
bool scheduler_manager::scheduler_manager_plc_exception_pub(event_exception *exce) 
{
	uint8_t state_msg[event_exception_size];
	pb_ostream_t stream_out;
	//int ret = 0;
	
	stream_out = pb_ostream_from_buffer(state_msg, sizeof(state_msg));

	

	if(m_zmq_log_switch)
	{
		SPDLOG_INFO("[ZMQ] scheduler_manager_plc_exception_pub ");
		SPDLOG_INFO("[ZMQ] excep src:{} / code:{} / dev:{} ",exce->evt_except.except.src, exce->evt_except.except.code, exce->evt_except.except.dev );
	}

	if( 0x00!=stream_out.bytes_written )
	{
		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] msg init fail ");
		}
		
		return false;

	}

	//exce->evt_except.except.description={{NULL}, NULL};
	
	if (!pb_encode(&stream_out, event_exception_fields, exce))   
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: pb_encode fail\n\n"<< std::endl;
#endif
		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] pb_encode fail ");
		}
		
		return false;
	}
	else
	{
		m_plc_exception_publisher.send(zmq::buffer(state_msg, stream_out.bytes_written), zmq::send_flags::none);
	}
	
#ifdef SCHEDULER_MSG_DEBUG
	OUTPUT_LOG;
	std::cout << "log>>: msg scheduler_manager_plc_exception_pub send\n\n"<< std::endl;
#endif
	if(m_zmq_log_switch)
	{
		SPDLOG_INFO("[ZMQ] msg scheduler_manager_plc_exception_pub send finish ");
	}

	return true;	
}


bool scheduler_manager::scheduler_manager_scheduler_vehicle(void) 
{
	zmq::message_t msg;
	pb_istream_t stream_in;

	vehicle_state vehicle_task_msg;

	//uint8_t rep_msg[512];

	while(1)
	{
		// 3. 处理 reply消息 --- 供包指令
		if(m_vehicle_task_subscriber.recv(&msg))//, ZMQ_DONTWAIT))
		{
			//SPDLOG_INFO("++++++++++++++++++++++++++++++++recv vehicle id {},postion {}");
            //SPDLOG_INFO("[ZMQ] m_feeder_replayer receive msg");
			stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
			if (!pb_decode(&stream_in, vehicle_state_fields, &vehicle_task_msg))
			{

				if(m_zmq_log_switch)
				{
					SPDLOG_INFO("[ZMQ] m_feeder_replayer pb decode fail");
				}
			}
			else
			{
				// 解析
				std::lock_guard<std::mutex> lck(m_vehicle_lock); 
				m_vehicle_map[vehicle_task_msg.dev_id] = vehicle_task_msg.motion.p;

				//SPDLOG_LOGGER_INFO(sub_logger,"[ZMQ] recv vehicle id {},postion {}",vehicle_task_msg.dev_id,vehicle_task_msg.motion.p);

			}
		}

	}

}
uint32_t scheduler_manager::scheduler_crc_task_info(uint32_t pos)
{

	// 第2层计算
	float fb2 = pos - (860+ 255*2 + 150.8*2 + 255.5 + 1960.4 + 303.7);

	for (int n = 0; n < 3; n++)
	{
		// 第1列
		float fy2 = (fb2 - 1960.4 * n) / 451.0 + 1;

		uint32_t fm = ((uint32_t)((fy2 + 0.05) * 100)) % 100;

		if (fm < 10)
		{
			SPDLOG_LOGGER_INFO(sub_logger,"layer {},gorup {},row {},",2,n + 1,uint32_t(fy2 + 0.05));
			break;
		}
	}

	// 第3层计算
	float fb3 = pos - (860+ 255*2 + 150.8*2 + 255.5 + 1960.4 + 303.7) -452.4;

	for (int n = 0; n < 3; n++)
	{
		// 第1列
		float fy2 = (fb3 - 1960.4 * n) / 451.0 + 1;

		uint32_t fm = ((uint32_t)((fy2 + 0.05) * 100)) % 100;

		if (fm < 10)
		{
			SPDLOG_LOGGER_INFO(sub_logger,"layer {},gorup {},row {},",3,n + 1,uint32_t(fy2 + 0.05));
			break;
		}
	}

	// 第4层计算
	float fb4 = pos - (860+ 255*2 + 150.8*2 + 255.5 + 1960.4 + 303.7) - 904.8;

	for (int n = 0; n < 3; n++)
	{
		// 第1列
		float fy2 = (fb4 - 1960.4 * n) / 451.0 + 1;

		uint32_t fm = ((uint32_t)((fy2 + 0.05) * 100)) % 100;

		if (fm < 10)
		{
			SPDLOG_LOGGER_INFO(sub_logger,"layer {},gorup {},row {},",4,n + 1,uint32_t(fy2 + 0.05));
			break;
		}
	}
	return 0;
}

bool scheduler_manager::scheduler_manager_scheduler_vehicle2(void) 
{
	zmq::message_t msg;
	pb_istream_t stream_in;

	//int i;
	vehicle_task_state task_state_msg;

	//uint8_t rep_msg[512];
	//pb_ostream_t stream_out;

	//msg_queue temp;
	while(1)
	{
		// 3. 处理 reply消息 --- 供包指令
		if(m_vehicle_task_state_subscriber.recv(&msg))//, ZMQ_DONTWAIT))
		{
			
            SPDLOG_INFO("[ZMQ] ---------------------------------------------------");
			stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
			if (!pb_decode(&stream_in, vehicle_task_state_fields, &task_state_msg))
			{

				if(m_zmq_log_switch)
				{
					SPDLOG_INFO("[ZMQ] m_feeder_replayer pb decode fail");
				}
			}
			else
			{
				// 解析
				std::lock_guard<std::mutex> lck(m_vehicle_lock); 
				if(task_state_msg.type == task_type_SHIFT_OUT)
				{
					if(task_state_msg.state == task_state_SUCCEED_OVER)
					{
		 				uint32_t postion;
						auto iter = m_vehicle_map.find(task_state_msg.dev_id);
						if (iter != m_vehicle_map.end())
						{
							//postion = m_vehicle_map[task_state_msg.dev_id] ;
							postion = iter->second;
							// scheduler_crc_task_info(postion);

							SPDLOG_INFO("vehicle id {},pos {}",task_state_msg.dev_id,postion);

							SPDLOG_LOGGER_INFO(sub_logger,"\n####### vehicle id {},pos {}",task_state_msg.dev_id,postion);

						}

						for(auto vehicle : m_vehicle_map)
						{
							SPDLOG_LOGGER_INFO(sub_logger," ++++++ vehicle id {},pos {}",vehicle.first ,vehicle.second);
						}
					
						// 根据postion 就算格口id
						// func (根据postion)
						//SPDLOG_INFO("vehicle id {},pos {},cnt {}",);
					}
				}

			}
		}

	}

}
/**@brief     同调度软件通信的线程函数，响应调度端发布的任务并存储至对应的blocking queue中，
 *            将网络端接收到的设备状态解析后的状态信息pop并发布出去
* @param[in]  NULL
* @return     操作结构
* - true      成功
* - false     失败
*/
bool scheduler_manager::scheduler_manager_scheduler_thread2(void) 
{
	zmq::message_t msg;
	pb_istream_t stream_in;

	//feeder_cmd feeder_task;
	//ack feeder_task_ack;

	// 分播请求任务
	//sorting_task_msg sorting_task;
	//ack  sorting_task_ack;

	// 分播动作请求
	sorting_task_msg sorting_action;
	//ack  sorting_action_ack;
	sorting_task_msg sorting_action_ack;

	uint8_t rep_msg[512];
	pb_ostream_t stream_out;

	//msg_queue temp;
	while(1)
	{
				// 2. 处理分播动作请求 任务id，对调度任务请求应答
        if (m_sort_action_replayer.recv(&msg) )// zmq::recv_flags::none))
		{
            //SPDLOG_INFO("[ZMQ] m_sort_action_replayer receive msg");
						
            //SPDLOG_INFO("[ZMQ] m_sort_task_replayer receive msg");
			stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
			if (!pb_decode(&stream_in, sorting_task_msg_fields, &sorting_action))
			{
				SPDLOG_INFO("[ZMQ] m_feeder_replayer pb decode fail");
			}
			else
			{
				// 1.缓存到缓冲队列

				if (sorting_action.dev_id >= 2 )//|| sorting_action.id < 1)
				{
					
					SPDLOG_INFO("[ZMQ] sorting_action.id err = {}",sorting_action.dev_id);
					sorting_action.dev_id = 1;
					//std::this_thread::sleep_for(std::chrono::milliseconds(5));
					// return 0;
				}
				if(1)
				{
				    std::lock_guard<std::mutex> lck(m_sort_action_lock);
				    sorting_action_ack = m_sort_action_recv[sorting_action.dev_id];
				}
				sorting_action_ack.sequence = sorting_action.sequence;
				sorting_action_ack.dev_id = sorting_action.dev_id;

                if(m_sort_action_recv[sorting_action.dev_id].container)
				SPDLOG_INFO("====== [ZMQ] m_sort_action_replayer receive msg id = {},container={}",sorting_action_ack.dev_id,m_sort_action_recv[sorting_action.dev_id].container);

				if(sorting_action_ack.container != 0)
				{
					uint32_t postion;
					// 记录当前请求响应
					sorting_action_record[sorting_action.dev_id] = sorting_action_ack;
                    #if 1
	                 // 增加车辆id
					{
					//sorting_action.vehicle_id;
					// 解析
				    std::lock_guard<std::mutex> lck(m_vehicle_lock); 
					//if(m_vehicle_map.find(sorting_action.vehicle_id))
					//sorting_action.vehicle_id = 3004;
					auto iter = m_vehicle_map.find(sorting_action.vehicle_id);
					if(iter != m_vehicle_map.end())
					{
						postion = iter->second;
					}
					else
					{
						postion = 1234567;
					}
					}
				    #endif
					SPDLOG_INFO("====== send sort task/action info :  \n====== fid:{}\n====== seq:{}\n====== ctn:{}\n====== gds:{}\n====== tsk:{}\
					\n====== vid:{}\n====== pos:{}",\
					sorting_action_ack.dev_id,\
					sorting_action_ack.sequence,\
					sorting_action_ack.container,\
					sorting_action_ack.gd_codes,\
					sorting_action_ack.task_id,
					sorting_action.vehicle_id,
					postion
					);

					SPDLOG_LOGGER_INFO(sub_logger,"====== send sort task/action info :  \n====== fid:{}\n====== seq:{}\n====== ctn:{}\n====== gds:{}\n====== tsk:{}\
					\n====== vid:{}\n====== pos:{}",\
					sorting_action_ack.dev_id,\
					sorting_action_ack.sequence,\
					sorting_action_ack.container,\
					sorting_action_ack.gd_codes,\
					sorting_action_ack.task_id,
					sorting_action.vehicle_id,
					postion
					);
					
					memset(&m_sort_action_recv[sorting_action.dev_id ], 0, sizeof(m_sort_action_recv[sorting_action.dev_id]));
				}

				// 3.ack		
				stream_out = pb_ostream_from_buffer(rep_msg, sizeof(rep_msg));
				
				if(m_zmq_log_switch)
				{
					//SPDLOG_INFO("[ZMQ] m_feeder_replayer ack msg ready");
				}
				
				if (!pb_encode(&stream_out, sorting_task_msg_fields, &sorting_action_ack))
				{
					if(m_zmq_log_switch)
					{
						SPDLOG_INFO("[ZMQ] sorting_task_msg_fields ack msg pb_encode fail");
					}
					
					//return false;
				}
				else
				{
					m_sort_action_replayer.send(zmq::buffer(rep_msg, stream_out.bytes_written), zmq::send_flags::none);
					if(m_zmq_log_switch)
					{
						//SPDLOG_INFO("[ZMQ] m_sort_action_replayer ack msg send");
					}

				}
		
			}
		}

		//std::this_thread::sleep_for(std::chrono::milliseconds(10));
	}

}

bool scheduler_manager::scheduler_manager_scheduler_thread3(void) 
{
	zmq::message_t msg;
	pb_istream_t stream_in;
	
	feeder_cmd feeder_task;
	ack feeder_task_ack;

	// 分播请求任务

	

	// 分播动作请求

	//ack  sorting_action_ack;
	

	uint8_t rep_msg[512];
	pb_ostream_t stream_out;

	msg_queue temp;
	while(1)
	{
			// 3. 处理 reply消息 --- 供包指令
		if(m_feeder_replayer.recv(&msg))//, ZMQ_DONTWAIT))
		{			
            //SPDLOG_INFO("[ZMQ] m_feeder_replayer receive msg");
			stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
			if (!pb_decode(&stream_in, feeder_cmd_fields, &feeder_task))
			{
				if(m_zmq_log_switch)
				{
					SPDLOG_INFO("[ZMQ] m_feeder_replayer pb decode fail");
				}
			}
			else
			{
				feeder_task_ack.sequence = feeder_task.sequnce;
		
				temp.type = PLC_FEEDER_ACTION;
				temp.dev_id = 0x00;
				memcpy(temp.msg_data, (uint8_t *)(&feeder_task), sizeof(feeder_task) );
				m_feeder_task_msg.push(temp);
						
				stream_out = pb_ostream_from_buffer(rep_msg, sizeof(rep_msg));
                // 获取供包-接包的设备id
				if(1)
				{
					//SPDLOG_INFO("recv supply cmd info ");
				}
				
				if(m_zmq_log_switch)
				{
					//SPDLOG_INFO("[ZMQ] m_feeder_replayer ack msg ready");
				}
				
				if (!pb_encode(&stream_out, ack_fields, &feeder_task_ack))
				{	
					if(m_zmq_log_switch)
					{
						SPDLOG_INFO("[ZMQ] m_feeder_replayer ack msg pb_encode fail");
					}
					
					// return false;
				}
				else
				{
					m_feeder_replayer.send(zmq::buffer(rep_msg, stream_out.bytes_written), zmq::send_flags::none);

					if (feeder_task.feeder_id < 2)
					{
						if (feeder_task.act == 5)
						{
							supply_cmd_flag[feeder_task.feeder_id] = 1;
						    SPDLOG_INFO("[ZMQ] m_feeder_replayer ack msg send,id {},supply_cmd_flag {}",feeder_task.feeder_id,supply_cmd_flag[feeder_task.feeder_id]);

						}

					}
					else
					{
						SPDLOG_INFO("[ZMQ] m_feeder_replayer feeder_id {}",feeder_task.feeder_id);
					}

					if(m_zmq_log_switch)
					{
						//SPDLOG_INFO("[ZMQ] m_feeder_replayer feeder cmd msg ack send,id {}",feeder_task.feeder_id);
					}

				}
		
			}
		}

		//std::this_thread::sleep_for(std::chrono::milliseconds(10));
	}

}
bool scheduler_manager::scheduler_manager_scheduler_thread(void) 
{
	zmq::message_t msg;
	pb_istream_t stream_in;

	//feeder_cmd feeder_task;
	//ack feeder_task_ack;

	// 分播请求任务
	sorting_task_msg sorting_task ;
	ack  sorting_task_ack;

	// 分播动作请求
	//sorting_task_msg sorting_action;
	//ack  sorting_action_ack;
	//sorting_task_msg sorting_action_ack;

	uint8_t rep_msg[512];
	pb_ostream_t stream_out;

	msg_queue temp;
	
	while(1)
	{

		// 1. 处理分播请求 任务id，处理物控pub的格口信息，及任务取消后的回复
        if(m_sort_task_replayer.recv(&msg, ZMQ_DONTWAIT))
		{
            // 务必在pb中的接收结构体中的数据清零，因为局部变量不初始化，且pb为解码方式，结构中有可变的长度。导致数据不能完全赋值。
            memset(&sorting_task,0,sizeof(sorting_task));
			if(1)
			{
				SPDLOG_INFO("[ZMQ] ======= receive  container msg {},{},{},{}",sorting_task.containers[0],sorting_task.containers[1],sorting_task.containers[2],sorting_task.containers[3]);
                memset(&sorting_task.containers[0],0,sizeof(sorting_task.containers));
			}
						
            //SPDLOG_INFO("[ZMQ] m_sort_task_replayer receive msg");
			stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
			if (!pb_decode(&stream_in, sorting_task_msg_fields, &sorting_task))
			{
				if(1)
				{
					SPDLOG_INFO("[ZMQ] m_sort_task_replayer pb decode fail");
				}
			}
			else
			{
				// 1.缓存到缓冲队列
				sorting_task_ack.sequence = sorting_task.sequence;
				temp.type = PLC_SORT_TASK_ID;
				temp.dev_id = 0x00;
				memcpy(temp.msg_data, (uint8_t *)(&sorting_task), sizeof(sorting_task) );
				//m_sort_task_msg.push(temp);

                // 2.赋值给供包机 带id
				if(sorting_task.dev_id >= 2)
				{
                    SPDLOG_INFO("[ZMQ] sorting task msg ,feeder id err, {}",sorting_task.dev_id);
					sorting_task.dev_id = 0;
				}
                // add 判断设备id
				if(strcmp((char *)sorting_task.task_id, m_req_task_queue[0]) == 0)
				{
					sorting_task.dev_id = 0;
					SPDLOG_INFO("recv task id find in the req queue 0 ");
				}
				else if (strcmp((char *)sorting_task.task_id, m_req_task_queue[1]) == 0)
				{
					sorting_task.dev_id = 1;
					SPDLOG_INFO("recv task id find in the req queue 1 ");
				}
				else
				{
					//
					SPDLOG_INFO("recv task id not find in the req queue,fault req0 {},req 1 {},recv{} ",m_req_task_queue[0],m_req_task_queue[1],(char *)sorting_task.task_id);
				}
				
	            memcpy(&m_sort_task_recv[sorting_task.dev_id], (uint8_t *)(&sorting_task), sizeof(sorting_task) );
				
				if((sorting_task.container == 0) && (sorting_task.containers[0] == 0))
				{
					m_sort_cancel_flag[sorting_task.dev_id] = 1;
					SPDLOG_INFO("[ZMQ] sort task flag m_sort_cancel_flag = 1,id {}",sorting_task.dev_id);
				}
				else 
				{
					m_sort_task_flag[sorting_task.dev_id] = 1;
					SPDLOG_INFO("[ZMQ] sort task flag m_sort_task_flag = 1,id {}",sorting_task.dev_id);
				}
				//m_sort_task_flag = 1;
				// 3.ack		
				stream_out = pb_ostream_from_buffer(rep_msg, sizeof(rep_msg));
				
				if(m_zmq_log_switch)
				{
					//SPDLOG_INFO("[ZMQ] m_sort_task_replayer ack msg ready");
				}
				
				if (!pb_encode(&stream_out, ack_fields, &sorting_task_ack))
				{	
					if(1)
					{
						SPDLOG_INFO("[ZMQ] m_sort_task_replayer ack msg pb_encode fail");
					}
					//continue;
					// return false; ljl 20220916
				}
				else
				{
					m_sort_task_replayer.send(zmq::buffer(rep_msg, stream_out.bytes_written), zmq::send_flags::none);
					sort_cmd_flag = 1;
					if(m_zmq_log_switch)
					{
						//SPDLOG_INFO("[ZMQ] m_sort_task_replayer ack msg send");
					}

				}
		
			}
		}

		//scheduler_manager_get_led_cmd();
		scheduler_manager_get_system_state();

		//处理需要publish出去的消息
		if( !m_scheduler_msg_queue.empty() )
		{
			//int size = m_scheduler_msg_queue.size();
			m_scheduler_msg_queue.pop(temp);
			
#ifdef SCHEDULER_MSG_DEBUG
			OUTPUT_LOG;
			std::cout << "log>>: need publish msg : " << size << "\n" <<std::endl;
			std::cout << "log>>: msg type : " << temp.type << "\n" <<std::endl;
#endif

			if(m_zmq_log_switch)
			{
				//SPDLOG_INFO("[ZMQ] need publish msg:{}, msg type :{}", size, temp.type);
			}

			switch(temp.type)
			{
				case PLC_SWITCH_STATE_PUB:
					//scheduler_manager_plc_switch_state_pub_single((switch_state_single*)(temp.msg_data)); 
					break;

				case PLC_SLOT_STATE_PUB:
					//scheduler_manager_plc_slot_state_pub((slot_state*)(temp.msg_data)); 
					break;

				case PLC_EXCEP_PUB: // 5 异常信息上报
					scheduler_manager_plc_exception_pub( (event_exception*)(temp.msg_data)); 
					break;
				
				case PLC_GOODS_INS_PUB: // 1 任务+sku code
					scheduler_manager_plc_goods_info_pub( (sorting_task_msg*)(temp.msg_data)); 
					break;

				case PLC_SWITCH_STATE_TOTAL_PUB:
					//scheduler_manager_plc_switch_state_pub_total( (switch_state_multiple*)(temp.msg_data)); 
					break;
				
				case PLC_BUTTON_STATE_PUB: // 4.按键取消（？）
					scheduler_manager_plc_button_state_pub( (key_event*)(temp.msg_data)); 
					break;
				
				case PLC_FEEDER_STATE_PUB: // 3 供包机状态 （取消？）

				    feeder_dev_state_total  tmp;
					memcpy(&tmp,temp.msg_data,sizeof(tmp));
					scheduler_manager_plc_feeder_state_pub(&tmp);
					//scheduler_manager_plc_feeder_state_pub((feeder_dev_state_total*)(temp.msg_data)); 
					//scheduler_manager_plc_feeder_state_pub((scanner_state*)(temp.msg_data));
					break;
				case PLC_SORT_SORT_INFO_PUB :
				    scheduler_manager_plc_sort_info_pub( (sorting_task_msg*)(temp.msg_data));
					break;
                case PLC_SORT_CANCEL_PUB : // 2 任务取消
                    scheduler_manager_plc_sort_info_pub( (sorting_task_state_msg*)(temp.msg_data));
				    break;

				default:
					break;
				
			}


		}
		
		//int pid = getpid();
		//OUTPUT_LOG;
		//std::cout << "pid>>: pid= "<<pid <<", lwp = "<< syscall(SYS_gettid) << "--"<< __FILE__ <<"--"<< __FUNCTION__<<"--" << __LINE__ << std::endl;

		std::this_thread::sleep_for(std::chrono::milliseconds(5));


	}

}




/**@brief     从database获取当前的设备map信息，用来解析并获取所有switch的信息
* @param[out]  data_map *map --- 获取的map信息数据
* @return     操作结构
* - true      获取成功
* - false     获取失败
*/
bool scheduler_manager::scheduler_manager_get_plc_switch_info(data_map *map) 
{
	uint8_t req_msg[1024];
	pb_ostream_t stream_out;
	pb_istream_t stream_in;
	data_request request;
	zmq::message_t zmq_reply;
	
	//data_map data;

	if( !m_data_requester.connected() )
	{
		OUTPUT_ERR;
	}

	strncpy(request.key, "map", sizeof(request.key));
	request.type = data_request_cmd_READ;
	
	if(m_zmq_log_switch)
	{
		SPDLOG_INFO("[ZMQ] m_data_requester ready to send");
	}

	stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
	if (!pb_encode(&stream_out, data_request_fields, &request))
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "log>>: msg pb_encode fail\n"<< std::endl;
#endif

		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] m_data_requester pb_encode fail ");
		}

		return false;
	}
	else
	{
		zmq_send(m_data_requester, req_msg, stream_out.bytes_written, 0);
		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] m_data_requester msg send finish ");
		}
	}
	
	m_data_requester.recv(zmq_reply, zmq::recv_flags::none);

	if( 0==zmq_reply.size() )
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "log>>: data base rec nothing \n"<< std::endl;
#endif
		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] m_data_requester rec nothing ");
		}
		
		return false;
	}
	
	stream_in = pb_istream_from_buffer((const uint8_t *)zmq_reply.data(), zmq_reply.size());
	if (!pb_decode(&stream_in, data_map_fields, map))
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "log>>: pb_decode fail\n\n"<< std::endl;
#endif
		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] m_data_requester pb_decode fail ");
		}
		
		return false;
	}

	return true;			

}


/**@brief     blocking queue push函数的二次封装，避免直接访问成员变量
* @param[in]  msg_queue &data --- 待操作的数据
* @return     NULL
*/
void scheduler_manager::scheduler_manager_scheduler_msg_queue_push(msg_queue &data)
{
    m_scheduler_msg_queue.push(data);
}


/**@brief     blocking queue empty函数的二次封装，避免直接访问成员变量。用来查询队列的空满状态
* @param[in]  NULL
* @return     队列空满状态
* - true      blocking queue为空
* - false     blocking queue非空
*/
bool scheduler_manager::scheduler_manager_scheduler_msg_queue_empty(void)
{
	return m_scheduler_msg_queue.empty();
}



/**@brief     blocking queue pop函数的二次封装，避免直接访问成员变量
* @param[out]  msg_queue *data --- 待操作的数据指针
* @return     NULL
*/
void scheduler_manager::scheduler_manager_scheduler_msg_pop(msg_queue *data)
{
	msg_queue temp;

	m_scheduler_msg_queue.pop(temp);

	memcpy(data, &temp, sizeof(temp));
}




/**@brief     blocking queue empty函数的二次封装，避免直接访问成员变量。用来查询队列的空满状态
* @param[in]  NULL
* @return     队列空满状态
* - true      blocking queue为空
* - false     blocking queue非空
*/
bool scheduler_manager::scheduler_manager_feeder_task_msg_queue_empty(void)
{
	return m_feeder_task_msg.empty();
}


/**@brief     blocking queue pop函数的二次封装，避免直接访问成员变量
* @param[out]  msg_queue *data --- 待操作的数据指针
* @return     NULL
*/
void scheduler_manager::scheduler_manager_feeder_task_msg_pop(msg_queue *data)
{
	msg_queue temp;

	m_feeder_task_msg.pop(temp);

	memcpy(data, &temp, sizeof(temp));
}



/**@brief     blocking queue empty函数的二次封装，避免直接访问成员变量。用来查询switch open 任务队列的空满状态
* @param[in]  NULL
* @return     队列空满状态
* - true      blocking queue为空
* - false     blocking queue非空
*/
bool scheduler_manager::scheduler_manager_switch_open_task_msg_queue_empty(void)
{
	return m_switch_open_task_msg.empty();
}


/**@brief     blocking queue pop函数的二次封装，避免直接访问成员变量
* @param[out] NULL
* @return     待open的switch id
*/
int scheduler_manager::scheduler_manager_switch_open_task_msg_pop(void)
{
	int temp;

	m_switch_open_task_msg.pop(temp);

	return temp;
}
int scheduler_manager::scheduler_manager_switch_open_task_msg_push(int dev)
{
	m_switch_open_task_msg.push(dev);

	return 0;
}


/**@brief     blocking queue empty函数的二次封装，避免直接访问成员变量。用来查询switch open队列当前深度
* @param[in]  NULL
* @return     队列深度
*/
int scheduler_manager::scheduler_manager_get_switch_open_task_msg_size(void)
{
	return m_switch_open_task_msg.size();
}


/**@brief     blocking queue empty函数的二次封装，避免直接访问成员变量。用来查询switch close 任务队列的空满状态
* @param[in]  NULL
* @return     队列空满状态
* - true      blocking queue为空
* - false     blocking queue非空
*/
bool scheduler_manager::scheduler_manager_switch_close_task_msg_queue_empty(void)
{
	return m_switch_close_task_msg.empty();
}


/**@brief     blocking queue pop函数的二次封装，避免直接访问成员变量
* @param[out] NULL
* @return     待open的switch id
*/
int scheduler_manager::scheduler_manager_switch_close_task_msg_pop(void)
{
	int temp;

	m_switch_close_task_msg.pop(temp);

	return temp;
}


/**@brief     blocking queue empty函数的二次封装，避免直接访问成员变量。用来查询switch close队列当前深度
* @param[in]  NULL
* @return     队列深度
*/
int scheduler_manager::scheduler_manager_get_switch_close_task_msg_size(void)
{
	return m_switch_close_task_msg.size();
}




/**@brief     从database获取当前的设备map信息，用来解析并获取所有switch的信息
* @param[out]  data_map *map --- 获取的map信息数据
* @return     操作结构
* - true      获取成功
* - false     获取失败
*/
bool scheduler_manager::scheduler_manager_get_led_cmd(void) 
{
	zmq::message_t msg;
	pb_istream_t stream_in;
	led_cmd cmd;
	msg_queue temp;

#ifdef SCHEDULER_MSG_DEBUG
	//OUTPUT_LOG;
	//std::cout << "log>>: receive task msg(pb)\n\n"<< std::endl;
#endif

	if(m_plc_led_cmd_subscriber.recv(&msg, ZMQ_DONTWAIT))
	{

#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_LOG;
		std::cout << "log>>: receive task msg(pb)\n\n"<< std::endl;
#endif
		stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
		OUTPUT_LOG;

		if (!pb_decode(&stream_in, led_cmd_fields, &cmd))
		{
#ifdef SCHEDULER_MSG_DEBUG
			OUTPUT_ERR;
			std::cout << "log>>: msg pb decode fail "<< stream_in.errmsg <<  "\n\n"<< std::endl;
#endif	
			return false;
		}
		else
		{
			temp.type = PLC_LED_CMD;
			temp.dev_id = 0x00;
			memcpy(temp.msg_data, (uint8_t *)(&cmd), sizeof(cmd) );
			m_feeder_task_msg.push(temp);
		}
	
	}
	else
	{
		return false;
	}

	return true;


}

/**@brief     分播架按键状态变化消息的发布函数，基于ZMQ 的PUB-SUB模式
* @param[in]  slot_state *dev_state --- 待操作的格口状态数据结构体指针
* @return     操作结构
* - true      发布成功
* - false     发布失败
*/
bool scheduler_manager::scheduler_manager_plc_button_state_pub(key_event *dev_state) 
{
	uint8_t state_msg[80];
	pb_ostream_t stream_out;

	
	stream_out = pb_ostream_from_buffer(state_msg, sizeof(state_msg));

#ifdef PLC_ZMQ_LOG
	// SPDLOG_INFO("[ZMQ] scheduler_manager_plc_button_state_pub ");
	// SPDLOG_INFO("[ZMQ] key_id id:{} / evt_type:{}",dev_state->key_id, dev_state->evt_type );
#endif
 
	if( 0x00!=stream_out.bytes_written )
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: msg init fail\n\n"<< std::endl;
#endif

		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] msg init fail ");
		}

		return false;

	}
	
	if (!pb_encode(&stream_out, key_event_fields, dev_state))
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: pb_encode fail\n\n"<< std::endl;
#endif
		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] pb_encode fail ");
		}	
		
		return false;
	}
	else
	{
		m_plc_button_state_publisher.send(zmq::buffer(state_msg, stream_out.bytes_written), zmq::send_flags::none);
	}
	
#ifdef SCHEDULER_MSG_DEBUG
	OUTPUT_LOG;
	std::cout << "log>>: msg scheduler_manager_plc_slot_state_pub send\n\n"<< std::endl;
#endif

	if(m_zmq_log_switch)
	{
		SPDLOG_INFO("[ZMQ] msg scheduler_manager_plc_button_state_pub send finish ");
	}
	
	return true;	
}



/**@brief     从database获取当前的设备map信息，用来解析并获取所有switch的信息
* @param[out]  data_map *map --- 获取的map信息数据
* @return     操作结构
* - true      获取成功
* - false     获取失败
*/
bool scheduler_manager::scheduler_manager_get_system_state(void) 
{
	zmq::message_t msg;
	pb_istream_t stream_in;
	sys_mode_state state;
	msg_queue temp;

#ifdef SCHEDULER_MSG_DEBUG
	//OUTPUT_LOG;
	//std::cout << "log>>: receive task msg(pb)\n\n"<< std::endl;
#endif

	if(m_plc_sys_state_subscriber.recv(&msg, ZMQ_DONTWAIT))
	{

#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_LOG;
		std::cout << "log>>: receive task msg(pb)\n\n"<< std::endl;
#endif
		stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
		//OUTPUT_LOG;

		if (!pb_decode(&stream_in, sys_mode_state_fields, &state))
		{
#ifdef SCHEDULER_MSG_DEBUG
			OUTPUT_ERR;
			std::cout << "log>>: msg pb decode fail "<< stream_in.errmsg <<  "\n\n"<< std::endl;
#endif	
			return false;
		}
		else
		{
			if( (state.state >= e_wkstate_STOP)&&(state.state <= e_wkstate_ERROR_RECOVERY) )
			{
				temp.type = PLC_SYS_MODE;
				temp.dev_id = 0x00;
				memcpy(temp.msg_data, (uint8_t *)(&state), sizeof(state) );
				//m_feeder_task_msg.push(temp);
				m_state = state.state;
				SPDLOG_INFO("[ZMQ] recv system fsm state[2/3:stop, 4:start, 5:emc] {},",m_state);
			}
			
		}
	
	}
	else
	{
		return false;
	}

	return true;


}



/**@brief     格口状态变化消息的发布函数，基于ZMQ 的PUB-SUB模式
* @param[in]  slot_state *dev_state --- 待操作的格口状态数据结构体指针
* @return     操作结构
* - true      发布成功
* - false     发布失败
*/
bool scheduler_manager::scheduler_manager_plc_abnormal_slot_total_pub(abnormal_slot_list *dev_state) 
{
	uint8_t state_msg[abnormal_slot_list_size];
	pb_ostream_t stream_out;
	//int ret = 0;
	
	stream_out = pb_ostream_from_buffer(state_msg, sizeof(state_msg));

#ifdef PLC_ZMQ_LOG
	SPDLOG_INFO("[ZMQ] scheduler_manager_plc_abnormal_slot_total_pub ");
	SPDLOG_INFO("[ZMQ] slot state cnt:{}",dev_state->slot_count);
#endif


	if( 0x00!=stream_out.bytes_written )
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: msg init fail\n\n"<< std::endl;
#endif

		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] msg init fail ");
		}

		return false;

	}
	
	if (!pb_encode(&stream_out, abnormal_slot_list_fields, dev_state))
	{
#ifdef SCHEDULER_MSG_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: pb_encode fail\n\n"<< std::endl;
#endif
		if(m_zmq_log_switch)
		{
			SPDLOG_INFO("[ZMQ] pb_encode fail ");
		}	
		
		return false;
	}
	else
	{
		m_plc_abslot_state_publisher.send(zmq::buffer(state_msg, stream_out.bytes_written), zmq::send_flags::none);
	}
	
	if(m_zmq_log_switch)
	{
		SPDLOG_INFO("[ZMQ] msg scheduler_manager_plc_abnormal_slot_total_pub send finish ");
	}
	
	return true;	
}

