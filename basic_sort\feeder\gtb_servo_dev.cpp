﻿#include "gtb_servo_dev.hpp"
#include "linux/can.h"


void gtb_servo_dev::forward_turn_position_ctrl(int speed, int pos, int delay_time, can_frame &f)
{
    servo_ctrl(FORWARD_TURN_POS_MODE, speed, pos, delay_time, f);
}
void gtb_servo_dev::reserve_turn_position_ctrl(int speed, int pos, int delay_time, can_frame &f)
{
    servo_ctrl(RESERVE_TURN_POS_MODE, speed, pos, delay_time, f);
}
void gtb_servo_dev::forward_turn_time_ctrl(int speed, int time, int delay_time, can_frame &f)
{
    servo_ctrl(FORWARD_TURN_TIME_MODE, speed, time, delay_time, f);
}
void gtb_servo_dev::reserve_turn_time_ctrl(int speed, int time, int delay_time, can_frame &f)
{
    servo_ctrl(RESERVE_TURN_TIME_MODE, speed, time, delay_time, f);
}
void gtb_servo_dev::set_acc(int acc, can_frame &f)
{
    if(acc > 127)
        acc = 127;
    else if(acc < 0)
        acc = 0;
    para_set(SET_ACC_PARA, acc, f);
}

void gtb_servo_dev::query_acc(can_frame &f)
{
    para_query(SET_ACC_PARA, f);
}

void gtb_servo_dev::servo_ctrl(int mode, int speed, int para_1, int para_2, can_frame &f)
{
    f.can_id = CAN_ID;
    f.can_dlc = 8;
    f.data[0] = START_FRAME;
    f.data[1] = mode;
    f.data[2] = dev_id;
    f.data[3] = speed;
    f.data[4] = para_1;
    f.data[5] = para_2;
    uint16_t crc = calc_crc16_modbus((unsigned char *)&f.data[0], 6);
    f.data[6] = crc & 0xff;
    f.data[7] = (crc >> 8) & 0xff;    
}
void gtb_servo_dev::para_set(int type, int para, can_frame &f)
{
    f.can_id = CAN_ID;
    f.can_dlc = 8;
    f.data[0] = SET_PARA_FRAME;
    f.data[1] = (dev_id >> 8) & 0xff;
    f.data[2] = dev_id & 0xff;
    f.data[3] = type;
    f.data[4] = (para >> 8) & 0xff;
    f.data[5] = para & 0xff;
    uint16_t crc = calc_crc16_modbus((unsigned char *)&f.data[0], 6);
    f.data[6] = crc & 0xff;
    f.data[7] = (crc >> 8) & 0xff;    
}
void gtb_servo_dev::para_query(int type, can_frame &f)
{
    f.can_id = CAN_ID;
    f.can_dlc = 8;
    f.data[0] = QUERY_PARA_FRAME;
    f.data[1] = (dev_id >> 8) & 0xff;
    f.data[2] = dev_id & 0xff;
    f.data[3] = type;
    f.data[4] = 0;
    f.data[5] = 0;
    uint16_t crc = calc_crc16_modbus((unsigned char *)&f.data[0], 6);
    f.data[6] = crc & 0xff;
    f.data[7] = (crc >> 8) & 0xff;    
}
void gtb_servo_dev::query(can_frame &f)
{
    f.can_id = CAN_ID;
    f.can_dlc = 8;
    f.data[0] = QUERY_FRAME;
    f.data[1] = RESERVE_TURN_TIME_MODE;
    f.data[2] = dev_id;
    f.data[3] = 0;
    f.data[4] = 0;
    f.data[5] = 0;
    uint16_t crc = calc_crc16_modbus((unsigned char *)&f.data[0], 6);
    f.data[6] = crc & 0xff;
    f.data[7] = (crc >> 8) & 0xff;    
}
#define GET_SYMBOL(x)  (x==0)?1:-1
int gtb_servo_dev::data_parse(can_frame f)
{
    if(f.can_id == CAN_ID)
    {
        if(f.data[0] == RECV_START_FRAME)
        {
            if(f.data[2] == dev_id)
            {
                if(calc_crc16_modbus((unsigned char *)&f.data[0], 6) == (f.data[6]+f.data[7]*256))
                {
                    sensor_state = (f.data[1] >> 6) & 0x01;
                    error_code = (f.data[3] >> 5) & 0x07; 
                    position = GET_SYMBOL((f.data[3] >> 4) & 0x01) * (((f.data[3] & 0x0f) << 16) | (f.data[4] << 8) | f.data[5]);
                    return 0;
                }
            }
        }
        else if(f.data[0] == QUERY_PARA_FRAME)
        {
            if(f.data[2] == dev_id)
            {
                if(calc_crc16_modbus((unsigned char *)&f.data[0], 6) == (f.data[6]+f.data[7]*256))
                {
                    if(f.data[3] == SET_DEV_ADDRESS)
                        address = f.data[5];
                    else if(f.data[3] == SET_ACC_PARA)
                        acc = f.data[5];
                    else if(f.data[3] == SET_BAUDRATE)
                        baud = f.data[5];

                    return 0;
                }
            }
        }
    }
    return -1;
}
int gtb_servo_dev::get_id()
{
    return dev_id;
}
void gtb_servo_dev::set_id(int id)
{
    dev_id = id;
}
int gtb_servo_dev::get_state()
{
    return state;
}
int gtb_servo_dev::get_position()
{
    return position;
}
int gtb_servo_dev::get_error_code()
{
    return error_code;
}
int gtb_servo_dev::get_sensor_state()
{
    return sensor_state;
}

int gtb_servo_dev::get_address()
{
    return address;
}
int gtb_servo_dev::get_acc()
{
    return acc;
}
int gtb_servo_dev::get_baudrate()
{
    return baud;
}

void gtb_servo_dev::clear_sensor_state()
{
    sensor_state = SENSOR_NO_TRIGGER;
}

uint16_t gtb_servo_dev::calc_crc16_modbus(uint8_t *data, int length)
{
    uint8_t i;
    //SPDLOG_LOGGER_DEBUG(spdlog::get("feeder"), "data0:{}, data1:{}, data2:{}, data3:{}, data4:{}, data5:{}, data6:{}, data7:{}", data[0], data[1], data[2], data[3], data[4], data[5], data[6], data[7]);
    uint16_t crc = 0xffff;
    while(length--)
    {
        crc ^= *data++;
        for (i = 0; i < 8; ++i)
        {
            if (crc & 1)
                crc = (crc >> 1) ^ 0xA001;
            else
                crc = (crc >> 1);
        }
    }
    return crc;
}

 