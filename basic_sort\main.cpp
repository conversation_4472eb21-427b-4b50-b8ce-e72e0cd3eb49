

/**@mainpage  plc_agent 设备管理软件
* <table>
* <tr><th>Project  <td>plc_agent
* <tr><th>Author   <td>lizhy
* <tr><th>Source   <td>
* </table>
* @section   项目描述
*   基于TCP长连接为数据交互基础，建立TCP服务器，接入PLC设备作为Client端实现通信及设备管理
* 主要实现同PLC设备实现基于TCP的消息收发机制，协议编解码、设备行为管理、状态更新，将PLC设备的
* 状态变化(自主/受控)通过ZMQ消息字发送给调度软件，同时从调度软件端通过ZMQ接收对PLC设备的控制指令
* 并实现对PLC设备的实际控制管理
* 
* @section   版本更新 
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/06/25  <td>1.0.1    <td>lizhy     <td>初始版本，仅含软件框架                  </tr>
* <tr><td>2021/06/30  <td>1.1.1    <td>lizhy     <td>
* -# net文件夹下设计TCP通信处理及epoll处理两种class，实现TCP操作及epoll操作 
* -# 移除原有threadpool设计，仅保留blockingqueue作为线程间通信使用
* </tr>
* <tr><td>2021/07/05  <td>1.2.0    <td>lizhy     <td>
* -# peotocol开始设计及与字符串形式的通信协议编码及解码软件
* </tr>
* <tr><td>2021/07/10  <td>1.3.0    <td>lizhy     <td>
* -# scheduler_msg文件夹下设计基于ZMQ机制的同scheduler软件的线程间通信机制，基于IPC，相应IPC基础文件见share文件夹下设计
* -# plc_manage文件夹下设计设备管理软件 \n
* </tr>
* <tr><td>2021/07/15  <td>1.3.1    <td>lizhy     <td>
* -# 编写顶层业务代码，实现设备联调
* </tr>
* </table>
**********************************************************************************
*/




/**@file 	   main.c
* @brief       项目主函数文件
* @details     main函数入口
* <AUTHOR>
* @date        2021-07-15
* @version     v1.0.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.1.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/06/11  <td>1.0.0    <td>lizhy     <td>初始版本                  		  </tr>
* <tr><td>2021/08/09  <td>1.1.0    <td>lizhy     <td>添加SPDLOG日志             	  </tr>
* </table>
*
**********************************************************************************
*/




#include "net/tcp_socket.hpp"



#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>
#include <spdlog/sinks/vehicle_file_sink.h>
#include <spdlog/async.h>


#include "plc_agent_config.hpp"

#include "plc_agent.hpp"

#include <sys/types.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <netdb.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <errno.h>
#include <iostream>
#include <stdexcept>
#include "setting/setting.hpp"


using namespace std;

#define SUPLLY_FILE_PATH     "/auto_sort/bin/"
#define SUPPLLY_FILE_NAME    "plc_agent_config.json"


int init_log(spdlog::level::level_enum &level,plc_agent &r_plc_agent)
{
	//std::string str_home_path = getenv("HOME"); //GetHomePath();
    std::string str_home_path = "/home/<USER>";
	std::string str_log_path = str_home_path+"/auto_sort/logs/basic_sort/sort.log";

    //auto daily_logger = spdlog::daily_logger_mt("plc_agent", str_log_path, 0, 0);
	std::shared_ptr<spdlog::logger> dev_logger = spdlog::vehicle_logger_mt<spdlog::async_factory_nonblock>("feeder_agent", str_log_path, 256*1024*1024, 5);
	
	auto stdout_sink = std::make_shared<spdlog::sinks::stdout_sink_mt>();

	dev_logger->set_level(level);
	
	dev_logger->sinks().push_back(stdout_sink); 		//增加从stdout输出

	// 1 默认日志 -全局日志开启
    spdlog::set_default_logger(dev_logger);
	spdlog::flush_every(std::chrono::seconds(3));
	spdlog::set_pattern("[%Y-%m-%d_%H:%M:%S.%e] [%s: %!: %#] [%l] %v");

    // 2 sub log 局部日志
	std::string str_log_path2 = str_home_path+"/auto_sort/logs/plc_agent/task_info/vehicle_task.log";
	r_plc_agent.get_schel_msg()->sub_logger = spdlog::vehicle_logger_mt<spdlog::async_factory_nonblock>("task_info", str_log_path2, 256*1024*1024, 5);

    auto stdout_sink2 = std::make_shared<spdlog::sinks::stdout_sink_mt>();

	r_plc_agent.get_schel_msg()->sub_logger->set_level(level);
	
	r_plc_agent.get_schel_msg()->sub_logger->sinks().push_back(stdout_sink2); 		//增加从stdout输出

    SPDLOG_INFO("initialize main loging ok");
	SPDLOG_LOGGER_INFO(r_plc_agent.get_schel_msg()->sub_logger,"initialize sub loging ok");
	return 0;	
}

int main()
{
	zmq::context_t context(1);
	
	plc_agent     plc_local_agent(context);
	server_info   net_server_cfg;
	log_cfg       plc_log_cfg;
	plc_work_mode dev_mode;
	feeder_info   feeder_para;
	
	plc_agent_get_config(&net_server_cfg, &plc_log_cfg, &dev_mode,&feeder_para);

	init_log(plc_log_cfg.log_level,plc_local_agent);
	plc_local_agent.plc_agent_init(net_server_cfg,      plc_log_cfg, dev_mode.mode,&feeder_para);
	
	SPDLOG_INFO("plc_agent_init ok");
	

	plc_local_agent.plc_agent_run();


	return 0;
}


