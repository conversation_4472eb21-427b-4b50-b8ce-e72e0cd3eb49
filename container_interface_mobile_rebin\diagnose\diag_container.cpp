
#include "share/lwshell/lwshell.h"
#include "share/lwshell/user_cmds.h"
#include "container_interface_mobile_rebin/ext_interface.hpp"

static int bind(const char* args[], const struct lwshell_interface *intf)
{
	if (args[1] == NULL || (args[2] == NULL))
	{
		shell_output(intf, "2 argument is needed\r\n", 0);
		return 0;
	}

	uint32_t id = atoi(args[1]);
	uint64_t rfid = atoi(args[2]);

	box_info_multiple state;
	state.boxes_count = 1;
	state.boxes[0].box_id = id;
	state.boxes[0].box_st = box_state_BIND;
	sprintf(state.boxes[0].RFID, "%02llu", rfid);
	ext_interface::get_instance()->send_container_state(state);

	char buf[64];
    int l = sprintf(buf, "group bind");
    shell_output(intf, buf, l);

	return 0;
};

static int unbind(const char* args[], const struct lwshell_interface *intf)
{
	if (args[1] == NULL)
	{
		shell_output(intf, "1 argument is needed\r\n", 0);
		return 0;
	}

	uint32_t id = atoi(args[1]);

	box_info_multiple state;
	state.boxes_count = 1;
	state.boxes[0].box_id = id;
	state.boxes[0].box_st = box_state_UNBIND;
	strcpy(state.boxes[0].RFID, "0");
	ext_interface::get_instance()->send_container_state(state);

	char buf[64];
    int l = sprintf(buf, "group unbind");
    shell_output(intf, buf, l);

	return 0;
};

static const struct cmd_handler cmds[] = 
{
	{"bind", "bind container group.", &bind},
	{"unbind", "unbind container group.", &unbind}
};

int container_interface_init(void)
{
	for(unsigned int i=0; i<ARRAY_SIZE(cmds); i++)
	{
		lwshell_register_cmd(&cmds[i]);
	}

	return 0;
}

