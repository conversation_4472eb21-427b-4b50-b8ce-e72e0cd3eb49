﻿
/**@file  	   device_manage.hpp
* @brief       本播墙调度系统的设备管理接口文件
* @details     NULL
* <AUTHOR>
* @date        2022-02-14
* @version     v1.0.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.1.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/06/25  <td>1.0.0    <td>lizhy     <td>初始版本	                   </tr>
* </table>
*
**********************************************************************************
*/




#ifndef __SLOT_MANAGER_HPP__
#define __SLOT_MANAGER_HPP__

//#include "../threadpool/blocking_queue.hpp"



#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"
#include "share/pb/idl/data_map.pb.h"
#include "share/pb/idl/data_request.pb.h"


#include "share/pb/idl/plane_slot.pb.h"
#include "share/pb/idl/container_cmd.pb.h"



#include <string>
#include <netinet/ip.h>

#include <iostream>

#include <vector>
#include <map>
#include <sys/epoll.h>
#include <functional>
#include <memory>
#include <unordered_map>
#include <list>
#include <zmq.h>
#include <cppzmq/zmq.hpp>

#include <thread>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <errno.h>
#include <iostream>
#include <stdexcept>
#include <memory>
#include <mutex> 


using namespace std;


typedef unordered_map<int, state> plane_slot_state_map;

typedef unordered_map<int, container_seal_state> seal_state_map;


/**
* @brief 基于unordered_map设计的车辆动态管理模块
*/
class slot_manager
{
public:
	
	/**@brief  vehicle_list_map class构造函数
	* @param[in]  NULL
	* @return	  NULL
	*/
	explicit slot_manager(zmq::context_t &context);

	
	/**@brief  vehicle_list_map class析构函数
	* @param[in]  NULL
	* @return	  NULL
	*/
	~slot_manager();

	void slot_manager_init(int add_id,string zmq_bind0,string zmq_bind1);

	
	void slot_manager_run();

	void slot_manager_update_slot_state();

	bool slot_manager_check_slot_state();

	state slot_manager_get_slot_state(int slot_id);

	int create_container();

	container_seal_state slot_manager_get_seal_state(int seal_id);

	void slot_manager_update_seal_state();



private:
	
	zmq::socket_t m_slot_state_sub;  	   	  ///< 地图数据获取socket REQ 类型

	plane_slot_state_map m_slot_state;		

	std::thread *m_slot_state_thread;

	zmq::socket_t m_seal_state_sub;  	   	  ///< 地图数据获取socket REQ 类型
	seal_state_map m_seal_state;
	std::thread *m_seal_state_thread;

	int m_add_id = 0;
	string zmq_tcp_slot_addr;
	string zmq_tcp_seal_addr;



};





#endif
