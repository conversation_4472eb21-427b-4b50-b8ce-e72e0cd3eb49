
#include "share/lwshell/lwshell.h"
#include "share/lwshell/user_cmds.h"
#include <spdlog/spdlog.h>
#include "share/global_def.h"

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

#include "share/pb/idl/task.pb.h"

//#include "controller/thing/thing_impl.hpp"
//#include "map_manager/map_interface.hpp"

#include "share/pb/nanopb/pb_common.h"
#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"

#include "share/lwshell/builtin_cmds.h"
#include "share/lwshell/user_cmds.h"
#include "diag_sort.hpp"
#include <map>


supply_package *p_supply0;
supply_package *p_supply1;
std::unordered_map<std::string,int> cmd_map0 = {{"run0",0},{"run1",1},{"run3",3}};
std::unordered_map<std::string,int> cmd_map1 = {{"r0",0},{"r1",1},{"r3",3}};

static int action_msg(const char* args[], const struct lwshell_interface *intf)
{

	if(args[1] == NULL)
	{
		shell_output(intf, "1 argument is needed\r\n", 0);
		return -1;
	}
	
	std::string cmd = std::string(args[1]);
	
	if(cmd_map0.find(cmd) != cmd_map0.end())
	{
		p_supply0->action_msg.run_dir = cmd_map0[cmd];
		shell_output(intf, " sort 0 run dir \r\n", 0);
	}
	else if (cmd_map1.find(cmd)!= cmd_map1.end())
	{
		shell_output(intf, " sort 1 run dir \r\n", 0);
		p_supply1->action_msg.run_dir = cmd_map1[cmd];
	}
	
}

static const struct cmd_handler cmds[] = 
{
    {"help", "print this information", &help},

	{"sort", "sort servo test ", &action_msg},


};

int diag_sort_init(supply_package *p_m_supply0,supply_package *p_m_supply1)
{
	p_supply0 = p_m_supply0;
	p_supply1 = p_m_supply1;
	for(uint32_t i=0; i<ARRAY_SIZE(cmds); i++)
	{
		lwshell_register_cmd(&cmds[i]);
	}

	return 0;
}

