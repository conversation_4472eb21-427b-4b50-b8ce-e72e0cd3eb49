﻿

#include "../plc_agent_debug.h"

#include "thp_mutex.hpp"

#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <errno.h>
#include <iostream>
#include <stdexcept>

 thp_mutex::thp_mutex() 
 {
	pthread_mutex_init(&m_mutex, nullptr);
 }

thp_mutex::~thp_mutex() 
{
	pthread_mutex_destroy(&m_mutex);
}

/*!
* @brief 互斥量加锁
* @return 成功与否
*/
bool thp_mutex::lock() 
{
	return 0 == pthread_mutex_lock(&m_mutex);
}

/*!
* @brief 互斥量解锁
* @return 成功与否
*/
bool thp_mutex::unlock()
{
	return 0 == pthread_mutex_unlock(&m_mutex);
}

/*!
* @brief 获取底层互斥量id的指针
* @return 底层互斥量id的指针
 */
pthread_mutex_t* thp_mutex::get_thp_mutex() 
{
	return &m_mutex;
}


/*!
* @brief 构造函数
* @param [in] mutex_ptr 用于初始化底层互斥量的互斥量指针
*/
auto_lock_mutex::auto_lock_mutex(thp_mutex* mutex_ptr)
: m_mutex_ptr(mutex_ptr) 
{
	m_mutex_ptr -> lock();  // 构造时自动上锁
}

/*!
* @brief 析构函数
*/
auto_lock_mutex::~auto_lock_mutex() 
{
	m_mutex_ptr -> unlock();  // 析构时自动解锁
}




