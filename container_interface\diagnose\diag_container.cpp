
#include "share/lwshell/lwshell.h"
#include "share/lwshell/user_cmds.h"

#include "container_interface/controller.hpp"

static int set_state(const char* args[], const struct lwshell_interface *intf)
{
	if (args[1] == NULL)
	{
		shell_output(intf, "1 argument is needed\r\n", 0);
		return 0;
	}

	uint32_t container_id = atoi(args[1]);
	controller::get_instance()->set_contain(container_id);
	return 0;
}

static int set_baudrate(const char* args[], const struct lwshell_interface *intf)
{
	if (args[1] == NULL)
	{
		shell_output(intf, "1 argument is needed\r\n", 0);
		return 0;
	}

	uint32_t baud = atoi(args[1]);

	controller::get_instance()->set_baudrate(baud);

	char buf[64];
    int l = sprintf(buf, "set container baud to [%d]\r\n", baud);
    shell_output(intf, buf, l);

	return 0;
};

static int set_requery_tms(const char* args[], const struct lwshell_interface *intf)
{
	if (args[1] == NULL)
	{
		shell_output(intf, "1 argument is needed\r\n", 0);
		return 0;
	}

	uint32_t tms = atoi(args[1]);

	controller::get_instance()->set_requery_tms(tms);

	char buf[64];
    int l = sprintf(buf, "set container requery times to [%d]\r\n", tms);
    shell_output(intf, buf, l);

	return 0;
};

static int fault_inquiry(const char* args[], const struct lwshell_interface *intf)
{
	if (args[1] == NULL)
	{
		shell_output(intf, "1 argument is needed\r\n", 0);
		return 0;
	}

	uint32_t id = atoi(args[1]);

	controller::get_instance()->fault_inquiry(id);

	char buf[64];
    int l = sprintf(buf, "fault inquiry to [%d]\r\n", id);
    shell_output(intf, buf, l);

	return 0;
};

static int set_debug(const char* args[], const struct lwshell_interface *intf)
{
	controller::get_instance()->set_debug();

	char buf[64];
    int l = sprintf(buf, "set debug to containers \r\n");
    shell_output(intf, buf, l);

	return 0;
};

static const struct cmd_handler cmds[] = 
{
	{"set_state", "set seal to container.", &set_state},
	{"set_baudrate", "set baudrate to all containers.", &set_baudrate},
	{"set_requery_tms", "set requery times to all containers.", &set_requery_tms},
	{"fault_inquiry", "inquire fault to container.", &fault_inquiry},
	{"set_debug", "set debug to stop query.", &set_debug}
};

int container_interface_init(void)
{
	for(unsigned int i=0; i<ARRAY_SIZE(cmds); i++)
	{
		lwshell_register_cmd(&cmds[i]);
	}

	return 0;
}

