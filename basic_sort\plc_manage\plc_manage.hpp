﻿
/**@file  	   plc_manage.hpp
* @brief       PLC设备管理软件，实现PLC通信指令帧的生成和PLC设备的运行中管理
* @details     NULL
* <AUTHOR> @date        2021-07-14
* @version     v1.1.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.2.1
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/07/10  <td>1.0.0    <td>lizhy     <td>初始版本                         </tr>
* <tr><td>2021/07/14  <td>1.1.0    <td>lizhy     <td>
* -# 重新设计switch任务通信指令，将原有的单switch 号更改为支持多switch id 下发
* -# 重新设计switch任务队列，将open任务和close任务设计为2个队列，分别处理
* <tr><td>2021/08/09  <td>1.2.0    <td>lizhy     <td>
* -# 增加SPDLOG日志设计
* <tr><td>2021/08/16  <td>1.2.1    <td>lizhy     <td>
* -# 针对网络发送字段Value为空的问题，优化判断机制，字符串分割后检测得到的新vector长度，
* -# 若vector长度不满足要求，则返回默认错误
* <tr><td>2021/11/26  <td>1.3.1    <td>lizhy     <td>
* -# 添加部分指令,适配PLC通信协议(1.5.2)版本
* </table>
*
**********************************************************************************
*/


#ifndef __PLC_MANAGE_HPP__
#define __PLC_MANAGE_HPP__

#include "../net/tcp_socket.hpp"
#include "../net/epoll_poller.hpp"


#include "../protocol/plc_proto_preprocess.hpp"
#include "../protocol/plc_protocol.hpp"


#include "../blocking_queue/blocking_queue.hpp"


#include "share/pb/idl/plane_switch_state.pb.h"
#include "share/pb/idl/plane_switch_action.pb.h"
#include "share/pb/idl/plane_slot.pb.h"
#include "share/pb/idl/feeder_goods.pb.h"
#include "share/pb/idl/feeder_cmd.pb.h"
#include "share/pb/idl/exception.pb.h"

#include "share/pb/idl/sys_state.pb.h"
#include "share/pb/idl/plane_hmi.pb.h"
#include "share/pb/idl/task.pb.h"

#include <string>
#include <netinet/ip.h>

#include <iostream>

#include <vector>
#include <sys/epoll.h>
#include <functional>
#include <memory>

#include <vector>
#include <unordered_map>
#include <thread>
#include <mutex>
#include <queue>
#include <condition_variable>
#include <atomic>

using namespace std;

#define PLC_DEV_SWITCH_CTROL_SRC_SCHEDULER								( 1 )
#define PLC_DEV_SWITCH_CTROL_SRC_SENSOR									( 2 )

#define PLC_DEV_SWITCH_CTROL_SRC_DEFAULT								( PLC_DEV_SWITCH_CTROL_SRC_SCHEDULER  )

#define IS_PLC_DEV_PROTOCOL_VALUE_STRING_CNT_VALID(n)					( n>=2 )

#define PLC_DEV_SWITCH_MAX_NUM											( 50 )

/**@enum PLC_SWITCH_STAE
* @brief PLC设备的变轨器状态枚举
*/
typedef enum
{
	PLC_SWITCH_OPEN     = 0,
	PLC_SWITCH_CLOSE    = 1,
	PLC_SWITCH_INTERMEDIATE   = 2,
	PLC_SWITCH_UNKNOWN   = 3,
}PLC_SWITCH_STAE;


/**@enum PLC_SLOT_STAE
* @brief PLC设备的格口状态枚举
*/
typedef enum
{
	PLC_SLOT_UNKNOWN    = 0,
	PLC_SLOT_FULL       = 1,
	PLC_SLOT_NORMAL     = 2,
	PLC_SLOT_ABSENT     = 3
}PLC_SLOT_STAE;



/**@enum PLC_GOOD_LOCATION
* @brief PLC设备的商品位置枚举
*/
typedef enum
{
	PLC_G_LOCATION_UNKNOWN       = 0,
	PLC_G_LOCATION_FIRST_BELT    = 1,
	PLC_G_LOCATION_SECOND_BELT   = 2,
	PLC_G_LOCATION_THIRD_BELT    = 3,
	PLC_G_LOCATION_FOURTH_BELT   = 4, 
	PLC_G_LOCATION_LAST_BELT     = 255,
}PLC_GOOD_LOCATION;



/**@enum PLC_REBIN_STAE
* @brief PLC设备的Rebin车状态枚举
*/
typedef enum
{
	PLC_REBIN_UNKNOWN    = 0,
	PLC_REBIN_LOCK       = 1,
	PLC_REBIN_UNLOCK     = 2,
	PLC_REBIN_ABSENT     = 3
}PLC_REBIN_STAE;


/**@enum PLC_REBIN_LED
* @brief PLC设备的塔灯控制类型枚举
*/
typedef enum
{
	PLC_REBIN_LED_OFF            = 0,
	PLC_REBIN_LED_YELLOW         = 1,
	PLC_REBIN_LED_GREEN          = 2,
	PLC_REBIN_LED_YELLOW_TWIN    = 3, 
	PLC_REBIN_LED_RED     	     = 4,
	PLC_REBIN_LED_RED_TWIN       = 5
}PLC_REBIN_LED;


/**@enum PLC_SWITCH_ACT
* @brief PLC设备的switch控制类型枚举
*/
typedef enum
{
	PLC_SWITCH_NO_ACT            = 0,
	PLC_SWITCH_ACT_OPEN		     = 1,
	PLC_SWITCH_ACT_CLOSE      	 = 2,
}PLC_SWITCH_ACT;


/**@enum PLC_FEEDER_POSITION
* @brief PLC设备的供包台控制类型枚举
*/
typedef enum
{
	PLC_FEEDER_UNKNOWN       = 0,
	PLC_FEEDER_FIRST_BELT    = 1,
	PLC_FEEDER_SECOND_BELT   = 2,
	PLC_FEEDER_THIRD_BELT    = 3,
	PLC_FEEDER_FOURTH_BELT   = 4,
	PLC_FEEDER_LAST_BELT	 = 8,
	PLC_FEEDER_ALL_BELT      = 9
}PLC_FEEDER_POSITION;


/**@enum PLC_FEEDER_DIRE
* @brief PLC设备的供包台皮带运动方向控制类型枚举
*/
typedef enum
{
	PLC_FEEDER_DIRE_UNKNOWN     = 0,
	PLC_FEEDER_DIRE_FORWARD     = 1,
	PLC_FEEDER_DIRE_BACKWARD    = 2,
	PLC_FEEDER_DIRE_STOP	    = 3,
	PLC_FEEDER_CMD_CLEAR		= 4,
}PLC_FEEDER_DIRE;


/**@enum PLC_DEV_MODE
* @brief PLC设备的工作模式控制枚举
*/
typedef enum
{
	PLC_DEV_MODE_UNKNOWN     = 0,
	PLC_DEV_MODE_AUTO        = 1,
	PLC_DEV_MODE_MANUAL      = 2,
	PLC_DEV_MODE_DEMO	     = 3,
}PLC_DEV_MODE;


/**@enum PLC_BUTTON_STATE
* @brief PLC设备的供包台按键状态枚举
*/
typedef enum
{
	PLC_BUTTON_INVERT_RELEASE      = 0,
	PLC_BUTTON_START_PUSH    	   = 1,
	PLC_BUTTON_STOP_PUSH           = 2,
	PLC_BUTTON_PAUSE_PUSH    	   = 3,
	PLC_BUTTON_RESET_PUSH   	   = 4,
	PLC_BUTTON_EMERGT_PUSH	 	   = 5,
	PLC_BUTTON_EMERGT_RELEASE	   = 6,
	PLC_BUTTON_SAFE_GATE_OPEN	   = 7,
	PLC_BUTTON_SAFE_GATE_CLOSE	   = 8,
	PLC_BUTTON_INVERT_PUSH	       = 9,
	PLC_BUTTON_UNKNOWN             = 10
}PLC_BUTTON_STATE;


/**@brief     解析PLC当前通信帧类型
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中MsgType字段
* @return     PLC_MSG_TYPE  通信帧类型
*/
extern PLC_MSG_TYPE plc_manage_get_msg_type(const string &str_in );



/**@brief     解析PLC当前通信设备ID
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中DeviceID字段
* @return     当前设备ID
*/
extern int plc_manage_get_msg_dev_id(const string &str_in );


/**@brief     解析PLC当前通信帧序号
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中Sequence字段
* @return     当前通信帧序号
*/
extern int plc_manage_get_msg_sequence(const string &str_in );


/**@brief     解析PLC当前设备状态
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中Status字段
* @return     当前设备状态
*/
extern _PLC_DEV_STATUS plc_manage_get_msg_dev_status(const string &str_in );


/**@brief     解析PLC当前上报故障状态
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中Status字段
* @return     当前设备故障状体
*/
extern int plc_manage_get_dev_err_status(const string &str_in );


/**@brief     解析PLC当前上报故障码
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中FaultCode字段
* @return     当前设备故障码
*/
extern int plc_manage_get_dev_fault_code(const string &str_in );


/**@brief     解析PLC当前上报button状态
* @param[in]  const string &str_in   ---  输入的字符串，对应PLC通信协议中Status字段
* @return     当前设备按键状态
*/
extern int plc_manage_get_deb_button_status(const string &str_in );


/**@brief     解析PLC当前上报switch的状态
* @param[in]  string *str_in     ---  输入的字符串，对应PLC通信协议中Rail相关字段
* @param[out] uint32_t *dev_no   ---  解析得到的switch ID 数组
* @param[out] PLC_SWITCH_STAE *dev_state    ---  解析得到的switch 状态
* @return     当前switch ID 数组内有效数据个数
*/
extern int plc_manage_get_switch_state(string *str_in , uint32_t *dev_no, PLC_SWITCH_STAE *dev_state);


/**@struct msg_key_info
* @brief 定义MSG消息的关键信息
*/
typedef struct _msg_key_info
{
	PLC_MSG_TYPE msg_type;
	uint32_t msg_sequence;
	uint32_t msg_dev_id;
}msg_key_info;

/**@struct msg_full_info
* @brief 定义MSG消息的全部信息
*/
typedef struct _msg_full_info
{
	msg_key_info msg_info;
	string msg_data;
}msg_full_info;


/**@struct plc_dev_state_upload
* @brief PLC上报信息的数据结构体
*/
typedef struct _plc_dev_state_upload
{
	PLC_MSG_TYPE type;
	uint16_t len;
	uint8_t data[1050];
}plc_dev_state_upload;
// feed ctrl
typedef struct _feed_ctrl
{
	std::atomic<uint8_t> type;
	std::atomic<uint8_t> cmd;
	std::atomic<uint8_t> part;
    std::atomic<uint8_t> flag;
	int32_t belt_speed; 

	uint32_t vehicle_id;
	//uint8_t  type;
	//uint8_t  cmd;

}feed_ctrl;
/**
* @brief 基于blocking queue/thread等进行封装，用于实现PLC 设备的消息交互及设备管理
*/
class plc_dev_manager
{
	
public:

	/**@brief  plc_dev_manager class构造函数
	* @param[in]  NULL
	* @return	  NULL
	*/
    explicit plc_dev_manager();

	/**@brief  plc_dev_manager class 析构函数
	* @param[in]  NULL
	* @return     NULL
	*/
    ~plc_dev_manager();


	/**@brief	  plc_dev_manager class 初始化函数
	* @param[in]  int dev_mode   --- PLC设备默认的控制变轨策略
	* @return	  函数执行结果
	* - true	  执行完成
	*/
	bool plc_dev_manager_init(int dev_mode) ;
	
	/**@brief	  plc_dev_manager 运行函数，创建线程并运行
	* @param[in]  NULL
	* @return	  函数执行结果
	* - true	  创建完成
	*/	
	bool plc_dev_manager_run(void); 


	/**@brief	  PLC指令消息下发管理线程运行函数
	* @param[in]  NULL
	* @return	  NULL
	*/
	void plc_dev_manager_ins_downlink_thread(void); 


	/**@brief	  plc_dev_manager 内部网络通信文件描述符的初始赋值函数，当前fd<0，说明网络建立通信，则通信指令无法下发
	* @param[in]  int fd		   ---	传入的网络通信描述符
	* @return	  函数执行结果
	* - true	  创建完成
	*/
	bool plc_dev_manager_net_fd_init(int fd); 

	void plc_dev_manager_sequenct_monitor(void);

	/**@brief	  检测当前网络通信是否有效的判据
	* @param[in]  NULL
	* @return	  函数执行结果
	* - true	  网络已建立，具备发送条件
	* - false	  网络通信尚未建立，不具备发送条件
	*/
	bool plc_dev_manager_dev_id_valid(void);

	
	/**@brief	  PLC网络数据包的处理函数，网络接收数据并预处理后，即传入此处进行进一步行为级管理
	* @param[in]  vector<string> &msg_vec			  ---  预处理完成的字符串vector
	* @return	  NULL
	*/
	void plc_dev_manager_net_msg_manage(vector<string> &msg_vec             ); 

	
	/**@brief	  通信完成后的标志清除
	* @param[in]  NULL
	* @return	  NULL
	*/	
	void plc_dev_manager_previous_msg_done(void);

	
	/**@brief	  组帧生成对PLC的变轨器的实际控制指令，并准备下发
	* @param[in]  int *action		   ---	待操作的switch ID
	* @param[in]  int cnt			   ---	待操作的switch ID 数量
	* @param[in]  PLC_SWITCH_ACT act   ---	待操作的switch ID 对应的操作 OPEN/CLOSE 
	* @return	  NULL 
	*/
	void plc_dev_manager_get_switch_action(int *action, int cnt, PLC_SWITCH_ACT act);

	
	/**@brief	  组帧生成对PLC供包台的实际控制指令，并准备下发
	* @param[in]  feeder_cmd *action   ---	待执行的供包台操作
	* @return	  NULL 
	*/
	void plc_dev_manager_get_feeder_action(feeder_cmd *action);


	/**@brief	  获取当前PLC通信的时间tick
	* @param[in]  NULL
	* @return	  NULL
	*/
	void plc_dev_manager_update_comm_tick(void);


	/**@brief	  获取当前PLC最近一次有效通信的时间信息
	* @param[in]  NULL
	* @return	  最近一次通信的时间信息
	*/
	struct timespec plc_dev_manager_get_last_comm_tick(void);


	/**@brief	  blocking queue empty函数的二次封装，避免直接访问成员变量。用来查询待上报的设备状态任务队列的空满状态
	* @param[in]  NULL
	* @return	  队列空满状态
	* - true	  blocking queue为空
	* - false	  blocking queue非空
	*/
	bool plc_dev_manager_dev_state_queue_empty(void);

	
	/**@brief	  blocking queue pop函数的二次封装，避免直接访问成员变量
	* @param[in]  plc_dev_state_upload state_data --- 待操作的数据指针
	* @return	  NULL
	*/
	void plc_dev_manager_dev_state_queue_push(plc_dev_state_upload state_data);

	
	/**@brief	  blocking queue pop函数的二次封装，避免直接访问成员变量
	* @param[in]  NULL
	* @return	  队首元素
	*/
	plc_dev_state_upload plc_dev_manager_dev_state_queue_pop(void);

	
	/**@brief	  控制PLC进入默认工作模式，需要在收到REG指令后自动下发
	* @param[in]  NULL
	* @return	  NULL 
	*/
	void plc_dev_manager_set_plc_auto(void);

	
	/**@brief	  获取当前设备ID
	* @param[in]  NULL 
	* @return	  server 描述符
	*/
	inline int plc_dev_manager_get_dev_id() const 
	{
		return m_plc_dev_id; 
	}


	void plc_dev_manager_send_system_state(sys_mode_state dev_state);

	void plc_dev_manager_clear_good_info(void);

	void plc_dev_manager_send_main_led_cmd(led_cmd cmd);
    //std::atomic<feed_ctrl> stfeeder_ctrl
    feed_ctrl stfeeder_ctrl[2];
private:

	// 消息下发记录及重发机制相关成员
	bool m_is_previous_comm_finished;      					///< 前序通信是否完成的标志
	msg_full_info m_previous_comm_msg;   				    ///< 前序通信的完整数据


	blocking_queue<msg_full_info> m_net_downlink_queue;     ///< PLC设备待下发处理的消息队列

	int m_net_comm_fd;	                               		///< 网络通信文件描述符
	struct timespec m_last_msg_upload_tick;            		///< 网络通信的时间tick记录

	struct timespec m_last_msg_download_tick;            		///< 网络通信的时间tick记录

	std::thread *plc_dev_comm_downlin;	
	
	int m_plc_dev_id;										///< PLC设备ID
	uint16_t m_plc_dev_comm_sequenct; 							///< PLC通信序列号(仅下行)
	int m_plc_dev_default_mode;
	
	uint16_t m_plc_dev_up_sequence; 
	
	blocking_queue<plc_dev_state_upload> m_plc_dev_state_queue; ///< PLC上报状态队列

	bool m_plc_dev_last_err_state;
	
	
};







#endif
