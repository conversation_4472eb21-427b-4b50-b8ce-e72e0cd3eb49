#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <errno.h>
#include <net/if.h>
#include <sys/ioctl.h> 
#include <arpa/inet.h>
#include <linux/tcp.h>
#include <netdb.h>
#include <unistd.h>
#include <fcntl.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>

#include "share/lwshell/lwshell.h"

#define LOG_ERR printf
#define LOG_INF printf

static int tcp_set_keep_alive(int fd, int period, int retry_interval, int retry_times);
static int tcp_write(uint32_t dev, const char *data, uint32_t len);
static int tcp_readch(uint32_t dev, char *ch);

void tcp_console_run(uint16_t port)
{
    int listenfd, connectfd;
    struct sockaddr_in server;
    struct sockaddr_in client;
    socklen_t addrlen;

    listenfd = socket(AF_INET, SOCK_STREAM, 0);
    if (listenfd == -1)
    {
        LOG_ERR("tcp console create socket failed:%s\n", strerror(errno));
        return;
    }
    int option;
    option = SO_REUSEADDR;
    setsockopt(listenfd, SOL_SOCKET, option, &option, sizeof(option));

    bzero(&server, sizeof(server));
    server.sin_family = AF_INET;
    server.sin_port = htons(port);
    server.sin_addr.s_addr = htonl(INADDR_ANY);
    if (bind(listenfd, (struct sockaddr *)&server, sizeof(server)) == -1)
    {
        LOG_ERR("tcp console socket bind failed:%s\n", strerror(errno));
        return;
    }

    if (listen(listenfd, 1) == -1)
    {
        LOG_ERR("tcp console socket listen failed:%s\n", strerror(errno));
        return;
    }

    LOG_INF("waiting for clinet's request.....\n");
    while (1)
    {
        addrlen = sizeof(client);
        connectfd = accept(listenfd, (struct sockaddr *)&client, &addrlen);
        if (connectfd == -1)
        {
            LOG_ERR("tcp console socket accept error:%s\n", strerror(errno));
            break;
        }
        else
        {
            LOG_INF("client connected: %s:%d\n", inet_ntoa(client.sin_addr), client.sin_port);
        }

        tcp_set_keep_alive(connectfd, 120, 3, 3);

        lwshell_interface_t intf;

        intf.write = tcp_write;
        intf.read_char = tcp_readch;
        intf.dev = connectfd;       

        lwshell_run_console(&intf);
        LOG_INF("console quite\r\n");
#if 0
        if ((childpid = fork()) == 0)
        {
            close(listenfd);
            printf("client from %s\n", inet_ntoa(client.sin_addr));
            //memset(buff,'\0',sizeof(buff));
            printf("ready to read\n");
            while ((n = read(connectfd, buff, 4096)) > 0)
            {
                buff[n] = '\0';
                printf("recv msg from client: %s\n", buff);
            }
            printf("end read\n");
            exit(0);
        }
        else if (childpid < 0)
            printf("fork error: %s\n", strerror(errno));
#endif
        close(connectfd);
    }

    return ;
}


/* Set TCP keep alive option to detect dead peers. The interval option
 * is only used for Linux as we are using Linux-specific APIs to set
 * the probe send time, interval, and count. */
static int tcp_set_keep_alive(int fd, int period, int retry_interval, int retry_times)
{
    int val = 1;

	//����keepalive����
    if (setsockopt(fd, SOL_SOCKET, SO_KEEPALIVE, &val, sizeof(val)) == -1)
    {
        return -1;
    }
 
    /* Default settings are more or less garbage, with the keepalive time
     * set to 7200 by default on Linux. Modify settings to make the feature
     * actually useful. */
 
    /* Send first probe after interval. */
    val = period;
    if (setsockopt(fd, IPPROTO_TCP, TCP_KEEPIDLE, &val, sizeof(val)) < 0) {
        return -1;
    }
 
    /* Send next probes after the specified interval. Note that we set the
     * delay as interval / 3, as we send three probes before detecting
     * an error (see the next setsockopt call). */
    val = retry_interval;
    if (val == 0) val = 1;
    if (setsockopt(fd, IPPROTO_TCP, TCP_KEEPINTVL, &val, sizeof(val)) < 0) {
        return -1;
    }
 
    /* Consider the socket in error state after three we send three ACK
     * probes without getting a reply. */
    val = retry_times;
    if (setsockopt(fd, IPPROTO_TCP, TCP_KEEPCNT, &val, sizeof(val)) < 0) {
        return -1;
    }
 
    return 0;    
}

static int tcp_write(uint32_t dev, const char *data, uint32_t len)
{
    int ret;

	ret = send(dev, data, len, MSG_NOSIGNAL);
    if(ret < 0)
    {
        if( (errno == EPIPE) || (errno == ETIMEDOUT) )
            return LWSHELL_INTF_DISCONNECTED;

        return errno;
    }
    else
        return ret;
}

static int tcp_readch(uint32_t dev, char *ch)
{
    int ret;

    /*recv ����ֵ��
        ��ʵ�����ӶϿ�ʱ��recv����:0
        ���������ݿɶ�ʱ����<0, errno=11(EAGAIN)
    */
	ret = recv(dev, ch, 1, MSG_DONTWAIT);
    if(ret < 0)
    {
        if( (errno == EPIPE) || (errno == ETIMEDOUT) )
            return LWSHELL_INTF_DISCONNECTED;

        return errno;
    }
    else if (ret == 0)
        return LWSHELL_INTF_DISCONNECTED;
    
    return ret;
}