﻿

/**@file  	   plc_angent.cpp
* @brief       PLC设备管理软件的业务顶层代码
* @details     NULL
* <AUTHOR>
* @date        2021-07-15
* @version     v1.1.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.2.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/07/10  <td>1.0.0    <td>lizhy     <td>初始版本                         </tr>
* <tr><td>2021/07/15  <td>1.1.0    <td>lizhy     <td>
* -# 重新设计switch的open任务和close任务
* -# switch设备号为负数的设备任务更改还需要进一步设计
* <tr><td>2021/08/09  <td>1.2.0    <td>lizhy     <td>
* -# 添加SPDLOG 日志设计
* -# switch action 缓存队列thread周期修改为10ms
* </table>
*
**********************************************************************************
*/



#include "plc_agent_debug.h"

#include "share/nlohmann_json/json.hpp"

#include <spdlog/spdlog.h>
#include <spdlog/common.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>


#include "plc_agent_config.hpp"

#include <iostream>
#include <stdint.h>
#include <string.h>
#include <arpa/inet.h>
#include <errno.h>
#include <functional>
#include <unistd.h>
#include <fstream>


using namespace std;




void from_json(const nlohmann::json& j, server_info& v) 
{
	j.at("ip_addr").get_to(v.ip_addr);
	j.at("port").get_to(v.port);
}


void from_json(const nlohmann::json& j, plc_work_mode& v) 
{
	j.at("ctrl_mode").get_to(v.mode);
}



typedef struct _log_cfg_locol
{
	string log_level;
	string net_log;
	string protocol_log;
	string zmq_log;
	string dev_log;
	string heart_beat_log;

}log_cfg_locol;

void from_json(const nlohmann::json& j, log_cfg_locol& v) 
{
	j.at("log_level").get_to(v.log_level);
	j.at("net_log").get_to(v.net_log);
	j.at("protocol_log").get_to(v.protocol_log);
	j.at("zmq_log").get_to(v.zmq_log);
	j.at("dev_log").get_to(v.dev_log);
	j.at("heartbeat_log").get_to(v.heart_beat_log);
}


static bool plc_agent_log_config_convers(string &str_in)
{

	if( !strcmp(str_in.c_str(), PLC_AGENT_LOG_CONFIG_ON) )
	{
		return true;
	}
	else if( !strcmp(str_in.c_str(), PLC_AGENT_LOG_CONFIG_OFF) )
	{
		return false;
	}
	else
	{
		return true;
	}
	
}

static spdlog::level::level_enum plc_agent_log_level_convers(string &str_in)
{

	if( !strcmp(str_in.c_str(), PLC_AGENT_LOG_LEVEL_TRACE) )
	{
		return spdlog::level::trace;
	}
	else if( !strcmp(str_in.c_str(), PLC_AGENT_LOG_LEVEL_DEBUG) )
	{
		return spdlog::level::debug;
	}
	else if( !strcmp(str_in.c_str(), PLC_AGENT_LOG_LEVEL_INFO) )
	{
		return spdlog::level::info;
	}
	else if( !strcmp(str_in.c_str(), PLC_AGENT_LOG_LEVEL_WARN) )
	{
		return spdlog::level::warn;
	}
	else if( !strcmp(str_in.c_str(), PLC_AGENT_LOG_LEVEL_ERR) )
	{
		return spdlog::level::err;
	}
	else if( !strcmp(str_in.c_str(), PLC_AGENT_LOG_LEVEL_OFF) )
	{
		return spdlog::level::off;
	}
	else
	{
		return spdlog::level::off;
	}
	
}







/**@brief     vector容器查找函数，用于在全部switch表中查找对应设备
* @param[in]  vector<int> &vec      ----   存储switch号信息的vector
* @param[in]  int value     	    ----   待查找的数据
* @return     函数执行结果
* - true      完成匹配，数据存在
* - false     未完成匹配，数据不存在
*/
bool plc_agent_get_config(  server_info *s_info, log_cfg *log_config, plc_work_mode *default_mode,feeder_info *p_feeder_info)
{
#if 0
	json root;
	
	std::ifstream setting_file(file_name, std::ifstream::in);
	if (!setting_file.is_open())
	{
		SPDLOG_ERROR("Error opening file");
		return 0;
	}
	setting_file >> root;
#endif
	char buff[200] = {0};
	std::string file_name;

	getcwd(buff, sizeof(buff));

	file_name = buff;
    file_name = "/home/<USER>/auto_sort/bin";
#ifdef 	PLC_AGENT_DEBUG
	OUTPUT_LOG;
	std::cout << "log>>: dir name buff "<< buff  << std::endl;
	std::cout << "log>>: dir name curr_dir "<< file_name  << std::endl;
#endif

	file_name += "/sort_config.json";
	std::cout << "log>>: dir name curr_dir "<< file_name  << std::endl;
	
    nlohmann::json j;
    ifstream jfile(file_name.c_str());

#if 1
	try
	{
		jfile >> j;;
	}
	catch(nlohmann::detail::exception &fe)
	{
		SPDLOG_ERROR("open file occurs error: {}", fe.what());

		s_info->ip_addr = "0.0.0.0";
		s_info->port = 3000;
		
		log_config->log_level = spdlog::level::debug;
		
		log_config->net_log = true;
		log_config->protocol_log = true;
		log_config->zmq_log = true;
		log_config->dev_log = true;
		log_config->heartbeat_log = true;
		default_mode->mode = 1;

		return false;
	}
#endif


	
    server_info vi = j.at("server");
	
	std::cout << vi.ip_addr << std::endl; 
	std::cout << vi.port << std::endl; 

#if 0	
    log_cfg_locol log = j.at("log");
	
	std::cout << log.log_level << std::endl; 
	std::cout << log.net_log << std::endl; 
	std::cout << log.protocol_log << std::endl; 
	std::cout << log.zmq_log << std::endl; 
	std::cout << log.dev_log << std::endl; 
	std::cout << log.heart_beat_log << std::endl; 


    plc_work_mode dev_mode = j.at("mode");

	default_mode->mode = dev_mode.mode;
	std::cout << dev_mode.mode << std::endl; 
	std::cout << default_mode->mode << std::endl; 
#endif 
    p_feeder_info->scan_dev1_addr = j["feeder"]["scan_dev1_addr"];
	p_feeder_info->scan_dev2_addr = j["feeder"]["scan_dev2_addr"];
	
    p_feeder_info->feeder_count = j["feeder"]["feeder_count"];
	p_feeder_info->belt_count = j["feeder"]["belt_count"];
    p_feeder_info->first_feeder_dir =  j["feeder"]["first_feeder_dir"];
	p_feeder_info->second_feeder_dir =  j["feeder"]["second_feeder_dir"];
	p_feeder_info->double_feeder_scan = j["feeder"]["double_feeder_scan"];
	p_feeder_info->feeder_belt_virtual = j["feeder"]["feeder_belt_virtual"];
	p_feeder_info->cancel_container_change =j["feeder"]["cancel_container_change"];
	p_feeder_info->cancel_container_id = j["feeder"]["cancel_container_id"];

	p_feeder_info->code_count = j["feeder"]["code_count"];

	p_feeder_info->belt1_spd = j["feeder"]["belt1_spd"];
	p_feeder_info->belt2_spd = j["feeder"]["belt2_spd"];
	p_feeder_info->belt3_spd = j["feeder"]["belt3_spd"];
	p_feeder_info->belt4_spd = j["feeder"]["belt4_spd"];

	p_feeder_info->belt_acc = j["feeder"]["belt_acc"];
	p_feeder_info->belt_dcc = j["feeder"]["belt_dcc"];

	p_feeder_info->belt1_ratio = j["feeder"]["belt1_ratio"];
	p_feeder_info->belt2_ratio = j["feeder"]["belt2_ratio"];
	p_feeder_info->belt3_ratio = j["feeder"]["belt3_ratio"];
	p_feeder_info->belt4_ratio = j["feeder"]["belt4_ratio"];

	p_feeder_info->belt1_diameter = j["feeder"]["belt1_diameter"];
	p_feeder_info->belt2_diameter = j["feeder"]["belt2_diameter"];
	p_feeder_info->belt3_diameter = j["feeder"]["belt3_diameter"];
	p_feeder_info->belt4_diameter = j["feeder"]["belt4_diameter"];

	p_feeder_info->belt1_len = j["feeder"]["belt1_len"];
	p_feeder_info->belt2_len = j["feeder"]["belt2_len"];
	p_feeder_info->belt3_len = j["feeder"]["belt3_len"];
	p_feeder_info->belt4_len = j["feeder"]["belt4_len"];

	p_feeder_info->belt1_dis = j["feeder"]["belt1_dis"];
	p_feeder_info->belt2_dis = j["feeder"]["belt2_dis"];
	p_feeder_info->belt3_dis = j["feeder"]["belt3_dis"];
	p_feeder_info->belt4_dis = j["feeder"]["belt4_dis"];

	p_feeder_info->belt1_dis_stop = j["feeder"]["belt1_dis_stop"];
	p_feeder_info->belt2_dis_stop = j["feeder"]["belt2_dis_stop"];
	p_feeder_info->belt3_dis_stop = j["feeder"]["belt3_dis_stop"];
	p_feeder_info->belt4_dis_stop = j["feeder"]["belt4_dis_stop"];

	p_feeder_info->isuse_fuse     = j["feeder"]["isuse_fuse"];

	p_feeder_info->weight_enable = j["feeder"]["weight_enable"];
	
	p_feeder_info->weight_max = j["feeder"]["weight_max"];

    p_feeder_info->volume_enable = j["feeder"]["volume_enable"];
	p_feeder_info->high_max = j["feeder"]["len_max"];
	p_feeder_info->wide_max = j["feeder"]["wide_max"];
	p_feeder_info->high_max = j["feeder"]["high_max"];
	p_feeder_info->goods_len_max = j["feeder"]["goods_len_max"];
	p_feeder_info->volume_key = j["feeder"]["volume_key"];
	p_feeder_info->auto_scan_static =  j["feeder"]["auto_scan_static"];
	p_feeder_info->sku_code_len =  j["feeder"]["sku_code_len"];
	p_feeder_info->noread_stop = j["feeder"]["noread_stop"];
	p_feeder_info->weight_min = j["feeder"]["weight_min"];

	p_feeder_info->para.belt_turn_speed = j["sort"]["belt_turn_speed"];
	p_feeder_info->para.belt_turn_time = j["sort"]["belt_turn_time"];
	p_feeder_info->para.belt_turn_delay_time = j["sort"]["belt_turn_dalay_time"];
	p_feeder_info->para.belt_acc = j["sort"]["belt_acc"];
	p_feeder_info->para.work_mode = j["sort"]["work_mode"];

	if (p_feeder_info->para.belt_turn_speed > 0xff)
		p_feeder_info->para.belt_turn_speed = 0xff;
	if (p_feeder_info->para.belt_turn_time > 0xff)
		p_feeder_info->para.belt_turn_time = 0xff;
	if (p_feeder_info->para.belt_turn_delay_time > 0xff)
		p_feeder_info->para.belt_turn_delay_time = 0xff;

	p_feeder_info->para.run_dir_config[0] = j["sort"]["run_dir_config_0"];
	p_feeder_info->para.run_dir_config[1] = j["sort"]["run_dir_config_1"];

	p_feeder_info->ip_info.client_udp_ip = j["client"]["client_udp_ip"];
	p_feeder_info->ip_info.client_udp_port = j["client"]["client_udp_port"];

	p_feeder_info->ip_info.scan_client_tcp_port0 = j["client"]["scan_client_tcp_port0"];
	p_feeder_info->ip_info.scan_client_tcp_ip0 = j["client"]["scan_client_tcp_ip0"];

	p_feeder_info->ip_info.scan_client_tcp_port1 = j["client"]["scan_client_tcp_port1"];
	p_feeder_info->ip_info.scan_client_tcp_ip1 = j["client"]["scan_client_tcp_ip1"];

	p_feeder_info->ip_info.feeder_client_tcp_port0 = j["client"]["feeder_client_tcp_port0"];
	p_feeder_info->ip_info.feeder_client_tcp_ip0 = j["client"]["feeder_client_tcp_ip0"];
	p_feeder_info->ip_info.feeder_client_tcp_port1 = j["client"]["feeder_client_tcp_port1"];
	p_feeder_info->ip_info.feeder_client_tcp_ip1 = j["client"]["feeder_client_tcp_ip1"];

    if (j["client"].contains("zmq tcp") == true)
	{
		p_feeder_info->ip_info.zmq_tcp_slot_addr = j["client"]["zmq_tcp_slot_addr"];

	    p_feeder_info->ip_info.zmq_tcp_seal_addr = j["client"]["zmq_tcp_seal_addr"];
	}

	 if (j["sort"].contains("auto_feeder") == true)
	 {
		p_feeder_info->auto_feeder = j["sort"]["auto_feeder"];

	 }
	 else
	 {
		p_feeder_info->auto_feeder = 0;
	 }

	std::cout <<"auto_feeder:" << p_feeder_info->auto_feeder << std::endl; 


	std::cout <<"belt count :" << p_feeder_info->belt_count \
	<<"feeder count :" <<  p_feeder_info->feeder_count\
	<<"servo code :" <<  p_feeder_info->code_count<< std::endl; 
	std::cout \
	 << "spd1 :" << p_feeder_info->belt1_spd\
	 << "spd2 :" <<p_feeder_info->belt2_spd \
	 << "spd3 :" <<p_feeder_info->belt3_spd \
	 << "spd4 :" <<p_feeder_info->belt4_spd \
	 << std::endl; 
	std::cout \
	<< "ratio1 :" <<p_feeder_info->belt1_ratio \
	<<"ratio2 :" <<p_feeder_info->belt2_ratio \
	<<"ratio3 :" <<p_feeder_info->belt3_ratio \
	<<"ratio4 :" << p_feeder_info->belt4_ratio\
	<< std::endl; 
	std::cout \
	<<  "diameter1 :" << p_feeder_info->belt1_diameter\
	<< "diameter2 :" << p_feeder_info->belt2_diameter \
	<< "diameter3 :" << p_feeder_info->belt3_diameter \
	<< "diameter4 :" << p_feeder_info->belt4_diameter \
	<< std::endl; 

	std::cout \
	<< "len1 :" << p_feeder_info->belt1_len \
	<< "len2 :" << p_feeder_info->belt2_len \
	<< "len3 :" << p_feeder_info->belt3_len \
	<< "len4 :" << p_feeder_info->belt4_len \
	<< std::endl; 
	std::cout \
	<< "dis1 :" << p_feeder_info->belt1_dis \
	<< "dis2 :" <<p_feeder_info->belt2_dis \
	<< "dis3 :" <<p_feeder_info->belt3_dis \
	<< "dis4 :" <<p_feeder_info->belt4_dis \
	<< std::endl;
	std::cout <<"len max" << p_feeder_info->goods_len_max  << std::endl;

#if 0
    package_over_info over_length_info = j.at("over");
	*pover_length_info = over_length_info;

	std::cout << over_length_info.over_length << std::endl; 
	std::cout << over_length_info.length_max << std::endl; 
#endif
	int i;


	s_info->ip_addr = vi.ip_addr;
	s_info->port = vi.port;
	s_info->udp_port = vi.udp_port;
#if 0
	log_config->log_level = plc_agent_log_level_convers(log.log_level);

	log_config->net_log = plc_agent_log_config_convers(log.net_log);
	log_config->protocol_log = plc_agent_log_config_convers(log.protocol_log);
	log_config->zmq_log = plc_agent_log_config_convers(log.zmq_log);
	log_config->dev_log = plc_agent_log_config_convers(log.dev_log);
	log_config->heartbeat_log = plc_agent_log_config_convers(log.heart_beat_log);
	
	std::cout << s_info->ip_addr << std::endl; 
	std::cout << s_info->port  << std::endl; 

	std::cout << log_config->log_level  << std::endl; 
	std::cout << log_config->net_log  << std::endl; 
	std::cout << log_config->protocol_log  << std::endl; 
	std::cout << log_config->zmq_log  << std::endl; 
	std::cout << log_config->dev_log  << std::endl; 
	std::cout << log_config->heartbeat_log  << std::endl; 
#endif
}




