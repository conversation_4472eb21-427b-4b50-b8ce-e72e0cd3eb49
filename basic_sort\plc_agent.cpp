﻿

/**@file  	   plc_angent.cpp
 * @brief       PLC设备管理软件的业务顶层代码
 * @details     NULL
 * <AUTHOR>
 * @date        2021-07-15
 * @version     v1.1.0
 * @copyright   Copyright (c) 2050
 **********************************************************************************
 * @attention
 * 主程序版本：v1.2.0
 * @par 修改日志:
 * <table>
 * <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
 * <tr><td>2021/07/10  <td>1.0.0    <td>lizhy     <td>初始版本                         </tr>
 * <tr><td>2021/07/15  <td>1.1.0    <td>lizhy     <td>
 * -# 重新设计switch的open任务和close任务
 * -# switch设备号为负数的设备任务更改还需要进一步设计
 * <tr><td>2021/08/09  <td>1.2.0    <td>lizhy     <td>
 * -# 添加SPDLOG 日志设计
 * -# switch action 缓存队列thread周期修改为10ms
 * </table>
 *
 **********************************************************************************
 */

#include "plc_agent_debug.h"
#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>

#include "protocol/plc_proto_preprocess.hpp"
#include "protocol/plc_protocol.hpp"

#include "plc_manage/plc_manage.hpp"

#include "scheduler_msg/scheduler_msg.hpp"
#include "diagnose/diagnose.hpp"

#include "share/pb/idl/plane_switch_state.pb.h"
#include "share/pb/idl/plane_switch_action.pb.h"
#include "share/pb/idl/plane_slot.pb.h"
#include "share/pb/idl/feeder_goods.pb.h"
#include "share/pb/idl/feeder_state.pb.h"
#include "share/pb/idl/exception.pb.h"

#include "share/pb/idl/data_map.pb.h"
#include "share/pb/idl/sys_state.pb.h"
#include "share/pb/idl/task.pb.h"

#include "plc_agent.hpp"
#include "uart.hpp"
#include "can.hpp"
#include <iostream>
#include <stdint.h>
#include <string.h>
#include <arpa/inet.h>
#include <errno.h>
#include <functional>
#include <thread>
#include <chrono>
#include <linux/can.h>
#include <linux/can/raw.h>
#include <stdexcept> // 包含标准异常类

using namespace std;

/**@brief     vector容器查找函数，用于在全部switch表中查找对应设备
 * @param[in]  vector<int> &vec      ----   存储switch号信息的vector
 * @param[in]  int value     	    ----   待查找的数据
 * @return     函数执行结果
 * - true      完成匹配，数据存在
 * - false     未完成匹配，数据不存在
 */
bool vector_find(vector<int> &vec, int value)
{
	if (std::find(vec.begin(), vec.end(), value) != vec.end())
		return true;
	else
		return false;
}

/**@brief  plc_agent class构造函数
 * @param[in]  NULL
 * @return	  NULL
 */
plc_agent::plc_agent(zmq::context_t &context)
	: m_scheduler_msg(context)
	,m_dev_slot(context)
	,m_dev_slot1(context)

{
}

/**@brief  plc_agent class析构函数
 * @param[in]  NULL
 * @return	  NULL
 */
plc_agent::~plc_agent()
{
}

/**@brief  plc_agent class初始化函数，实现网络服务器/epoll监听/ZMQ管理及设备管理的初始化设置
 * @param[in]  NULL
 * @return	  NULL
 */
void plc_agent::plc_agent_init(server_info server, log_cfg log_config, int plc_mode, feeder_info *p_feeder_info)
{
	// string ip_addr = "0.0.0.0";
	// string ip_addr = "************";
	// string ip_addr = "*************";
	struct sockaddr_in c_addr;
	// int port = 3000

	OUTPUT_LOG;
	std::cout << "log>>: ip_addr " << server.ip_addr << "port : " << server.port << "\n"
			  << std::endl;
	m_tcp_server_socket.tcp_socket_init();

	m_tcp_server_socket.tcp_socket_server_cfg(server.ip_addr, server.port);
	m_tcp_server_socket.tcp_socket_set_reuseaddr(true);
	m_tcp_server_socket.tcp_socket_set_tcp_no_delay(true);

	SPDLOG_INFO("tcp server init ok");
	SPDLOG_INFO("tcp server address :{} / port:{} ", server.ip_addr, server.port);
	SPDLOG_INFO("tcp server start listen, ready for connect ");

	m_tcp_server_socket.tcp_socket_bind();
	m_tcp_server_socket.tcp_socket_set_listen_on();

	// udp server
	m_udp_server_socket.udp_socket_init();
	m_udp_server_socket.udp_socket_server_cfg(server.ip_addr, server.port );
	m_udp_server_socket.udp_socket_set_reuseaddr(true);
	m_udp_server_socket.udp_socket_bind();

	SPDLOG_INFO("udp server init ok");
	SPDLOG_INFO("udp server address :{} / port:{} ", server.ip_addr, server.port);

	m_scheduler_msg.scheduler_manager_init();
	m_scheduler_msg.scheduler_manager_log_init(log_config.zmq_log);

	SPDLOG_INFO("inner msg manager(zmq) init ok, ready for communication");

	m_dev_manager.plc_dev_manager_init(plc_mode);

	SPDLOG_INFO("plc dev manager init ok");

	epoll_init(m_tcp_server_socket.tcp_socket_get_fd(),m_udp_server_socket.udp_socket_get_fd());
    // EPOLLET
	epoll_add_fd(m_tcp_server_socket.tcp_socket_get_fd(),EPOLLIN, false);
	epoll_add_fd(m_udp_server_socket.udp_socket_get_fd(),EPOLLIN, false);

	SPDLOG_INFO("epoll manager init ok , add socket:{} ", m_tcp_server_socket.tcp_socket_get_fd());

	m_feeder_info = *p_feeder_info;
}

/**@brief  plc_agent class 运行函数，创建class内线程，创建类内成员线程
 * @param[in]  NULL
 * @return	  NULL
 */
void plc_agent::plc_agent_run()
{

	diagnose_init(&m_supply[0],&m_supply[1]);
	diagnose_run();

	m_scheduler_msg.scheduler_manager_run();

	package_supply_manage_thread = new std::thread(&plc_agent::package_supply_manage, this);

	uart_recv_thread = new std::thread(&plc_agent::package_supply_uart_recv, this);
	can0_recv_thread = new std::thread(&plc_agent::package_supply_udp_recv_msg, this);
	can0_init_thread = new std::thread(&plc_agent::package_supply_udp_send_msg, this);


	m_dev_slot.slot_manager_init(0,m_feeder_info.ip_info.zmq_tcp_slot_addr,m_feeder_info.ip_info.zmq_tcp_seal_addr);
	m_dev_slot1.slot_manager_init(2000,m_feeder_info.ip_info.zmq_tcp_slot_addr,m_feeder_info.ip_info.zmq_tcp_seal_addr);

	m_dev_slot.slot_manager_run();
	m_dev_slot1.slot_manager_run();
	
	SPDLOG_INFO(" plc_agent_run run ");
	// epoll tcp 上指定端口信息
	epoll_main_loop(20);
}
// 
void plc_agent::string_split(const string& str, const string& split, vector<string>& res)
{
	//std::regex ws_re("\\s+"); // 正则表达式,匹配空格 
	std::regex reg(split);		// 匹配split
	std::sregex_token_iterator pos(str.begin(), str.end(), reg, -1);
	decltype(pos) end;              // 自动推导类型 
	for (; pos != end; ++pos)
	{
		res.push_back(pos->str());
	}
}
/**@brief  plc_agent class 网络消息接收处理函数
 * @param[in]  int fd        ---   网络通信的文件描述符
 * @return	  NULL
 */
#define  GOODS_INTERVAL_TIME 400 
void plc_agent::dev_ctrl_func(int fd)
{
	// 接收消息处理变量组
	int tlv_cnt_temp;
	uint32_t id_temp;
	uint32_t seue_temp;
	uint32_t seue_down_temp;

	int rs = 1;

	int i;

	////////////////////////////
	vector<string> msg_total;
	PLC_MSG_TYPE msg_type_temp;
	char recvBuf[2048] = {0};
	int ret = 999;
	bool result;

	// 接受客户端消息
	ret = recv(fd, recvBuf, 2048, 0);

	SPDLOG_INFO("[NET] [IN] message :{}, len :{}, :{}", fd, ret, recvBuf);

	// 设置QuickACK特性
	m_tcp_server_socket.tcp_socket_set_quick_ack();

	// 若属于断开连接消息
	if (0x00 == ret)
	{
		SPDLOG_INFO("[NET] [IN] client :{},{},{} dis connect", fd, ret, recvBuf);
		// epoll 移除事件监听
		epoll_remove_fd(fd);
		// 从设备map中删除
		epoll_poller_dev_delete(fd);
		// 关闭TCP连接
		close(fd);
		m_current_clnt--;
		// 自动扫网络断开
		// p_supply->warning = 4;
		// 断开
		m_auto_scan_state = 3;

		return;
	}

	// 若属于出错消息
	if (0x00 > ret)
	{
		epoll_reset_fd(fd, EPOLLET, true);

		SPDLOG_DEBUG("the msg ret < 0,err ");

		return;
	}
	if (ret == 2)
	{
		SPDLOG_DEBUG("the msg length more of 2 :<> ");
		return;
	}

	// PLC 消息解码并预处理
	result = plc_protocol_decodec(recvBuf, ret, &msg_total);
	SPDLOG_DEBUG("[NET] plc_protocol_decodec result:{}", result);

    if(result == false)
	{
		// 无法解析，则采用 ，default code 
		//msg_total[0] = "==========";
		msg_total.emplace_back("default code =====");
		SPDLOG_DEBUG("###### ###### ###### WARNING:tcp protocol decodec false,and  default goods code noread" );
	}

	// 若数据有效，进行下一步处理
	if (result)
	{
		struct sockaddr_in client_ip = epoll_poller_dev_get_sock_addr(fd);
		for (i = 0; i < msg_total.size(); i++)
		{
			// SPDLOG_DEBUG("[PROTOCOL] [{}] :{}", i, msg_total[i]);
		}
		if (1)
		{
			std::lock_guard<std::mutex> lck(belt4_lock);
			// 码值暂存到缓存
			// 相机
			if (client_ip.sin_addr.s_addr == m_supply[0].client_ip[0].sin_addr.s_addr)
			{
				if (msg_total[0].length() > SKU_CODE_LEN)
				{
					m_supply[0].warning = 2;
					SPDLOG_DEBUG("feeder 0 the code msg length more of MAX {}", SKU_CODE_LEN);
				}
				else
				{
					goods_taskid_msg goods_msg;
					char *ip = inet_ntoa(client_ip.sin_addr);
					SPDLOG_DEBUG("feeder 0 scan dev ip:{}", ip);
					vector<string> str_list;

					std::chrono::high_resolution_clock::time_point start_time;
					static std::chrono::high_resolution_clock::time_point last_time;

					start_time = std::chrono::high_resolution_clock::now();
					// SPDLOG_INFO("start_time {}",start_time);
					std::chrono::milliseconds interval = std::chrono::duration_cast<std::chrono::milliseconds>(start_time - last_time);

					SPDLOG_INFO("距离上次的时间间隔,sort = {},interval={}", 0, interval.count());

					if ((interval.count() > GOODS_INTERVAL_TIME ))
					{

						string_split(msg_total[0], ",task_id:", str_list);

						if (str_list.size() == 2)
						{
							// 取商品码
							strcpy(m_supply[0].goods_code, str_list[0].c_str());
							// 带有taskid
							uint32_t num = 0;
							try
							{
								num = std::stoi(str_list[1]);
								SPDLOG_DEBUG("The number is:{}", num);
							}
							catch (const std::out_of_range &e)
							{
								num = 1;
								SPDLOG_DEBUG("Error: Number out of range for int::{}", e.what());
								// std::cerr << "Error: Number out of range for int: " << e.what() << std::endl;
							}
							catch (const std::invalid_argument &e)
							{
								num = 1;
								SPDLOG_DEBUG("Error: Invalid argument:{}", e.what());
								// std::cerr << "Error: Invalid argument: " << e.what() << std::endl;
							}
							goods_msg.task_id = num;
							goods_msg.str = str_list[0];
							m_supply[0].m_goods_msg_queue.push(goods_msg);
							//
						}
						else if (str_list.size() == 1)
						{
							goods_msg.task_id = 0;
							strcpy(m_supply[0].goods_code, str_list[0].c_str());

							goods_msg.str = str_list[0];
							m_supply[0].m_goods_msg_queue.push(goods_msg);
						}
						else
						{
							// err
							// m_supply[0].task_id = 0;
							strcpy(m_supply[0].goods_code, msg_total[0].c_str());

							SPDLOG_DEBUG("feeder 0 recv tag char err......");
						}
					}
					else
					{
						SPDLOG_INFO("sort  0短时间内接收到2个sku码,舍弃1个,interval={}", interval.count());
						//SPDLOG_INFO("id : {},the uart data len:{},code:{},num ={}", p_supply->id, iRecv, p_belt1->goods_code, p_belt1->sensor_cnt);
					}

					last_time = start_time;
				}
			}
			else if(client_ip.sin_addr.s_addr == m_supply[0].client_ip[1].sin_addr.s_addr)
			{
				char *ip = inet_ntoa(client_ip.sin_addr);
				SPDLOG_DEBUG("feeder 0 metering weight dev ip:{}", ip);
				
				// 协议
				m_supply[0].weight = std::stof(msg_total[0].substr(11,9)) * 1000 + 1;
				SPDLOG_DEBUG("feeder 0 metering weight ascii:{},data {} g", msg_total[0],m_supply[0].weight);

			}
			else if(client_ip.sin_addr.s_addr == m_supply[0].client_ip[2].sin_addr.s_addr)
			{
				char *ip = inet_ntoa(client_ip.sin_addr);
				
				SPDLOG_DEBUG("feeder 0 metering volume dev ip:{}", ip);
				
				// 按键触发-人为触发测量，赋值给皮带
				if((m_feeder_info.belt_count == BELT3) && (m_feeder_info.volume_key == 1))
				{
					vector<string> str_list;
					string_split(msg_total[0],",",str_list);

					// 协议
				    m_supply[0].st_belt[1].vol.length  = std::stoi(str_list[0])  + 1;
					m_supply[0].st_belt[1].vol.width   = std::stoi(str_list[1])  + 1;
					m_supply[0].st_belt[1].vol.height  = std::stoi(str_list[2])  + 1;

					SPDLOG_DEBUG("feeder 0 metering volume length:{},width:{},height:{}",m_supply[0].st_belt[1].vol.length,m_supply[0].st_belt[1].vol.width,m_supply[0].st_belt[1].vol.height);
				}
				// 自动触扫码
				else 
				{

				}

			}
			// feeder: 1.99 对接供包机
			else if(client_ip.sin_addr.s_addr == m_supply[0].client_ip[3].sin_addr.s_addr)
			{
				char *ip = inet_ntoa(client_ip.sin_addr);

				SPDLOG_DEBUG("feeder 0 feeder client ip:{}", ip);

				vector<string> str_list;
				string_split(msg_total[0], ",", str_list);

				m_supply_fd[0] = fd;

				if (str_list.size() == 2)
				{
					m_supply[0].sort_info[0].task_num = std::stoi(str_list[0]);
					m_supply[1].sort_info[0].task_num = std::stoi(str_list[1]);
				}
				else
				{
					SPDLOG_DEBUG("err : feeder 0 feeder client ip:{},revc date {} ,not = 2", ip, str_list.size());
				}
			}
			// 1.199
			else if(client_ip.sin_addr.s_addr == m_supply[1].client_ip[3].sin_addr.s_addr)
			{
				char *ip = inet_ntoa(client_ip.sin_addr);

				SPDLOG_DEBUG("feeder 0 feeder client ip:{}", ip);

				vector<string> str_list;
				string_split(msg_total[0], ",", str_list);
				
				m_supply_fd[1] = fd;
				if(str_list.size() ==  2)
				{
					m_supply[0].sort_info[1].task_num= std::stoi(str_list[0]);
					m_supply[1].sort_info[1].task_num = std::stoi(str_list[1]);
				}
				else
				{
					SPDLOG_DEBUG("feeder 0 feeder client ip:{},revc date {}", ip,str_list.size());
				}
			}
			else if (client_ip.sin_addr.s_addr == m_supply[1].client_ip[0].sin_addr.s_addr)
			{
				if (msg_total[0].length() > SKU_CODE_LEN)
				{
					m_supply[1].warning = 2;
					SPDLOG_DEBUG("warning: feeder 1 the code msg length more of MAX {}", SKU_CODE_LEN);
				}
				else
				{
					goods_taskid_msg goods_msg;
					char *ip = inet_ntoa(client_ip.sin_addr);
					SPDLOG_DEBUG("feeder 1 scan dev ip:{}", ip);

					vector<string> str_list;

					std::chrono::high_resolution_clock::time_point start_time;
					// static 没有初始化，调用默认的构造函数初始化，一般是初始化为1970year
					static std::chrono::high_resolution_clock::time_point last_time;

					start_time = std::chrono::high_resolution_clock::now();
					// SPDLOG_INFO("start_time {}",start_time);
					std::chrono::milliseconds interval = std::chrono::duration_cast<std::chrono::milliseconds>(start_time - last_time);

					SPDLOG_INFO("sort 1 距离上次的时间间隔,sort  = {},interval={}",1, interval.count());
					//string_split(msg_total[0],"task_id:",str_list);

					if ((interval.count()) > GOODS_INTERVAL_TIME)
					{

						string_split(msg_total[0], ",task_id:", str_list);
						if (str_list.size() == 2)
						{
#if 0
			            // 取商品码 
                        strcpy(m_supply[1].goods_code, str_list[0].c_str());

						// 带有taskid
						goods_msg.task_id = std::stoi(str_list[1]);
						goods_msg.str = str_list[0];
					    m_supply[1].m_goods_msg_queue.push(goods_msg);
#endif

							// 取商品码
							strcpy(m_supply[1].goods_code, str_list[0].c_str());
							// 带有taskid
							uint32_t num = 0;
							try
							{
								num = std::stoi(str_list[1]);
								SPDLOG_DEBUG("The number is:{}", num);
							}
							catch (const std::out_of_range &e)
							{
								// num = 1;
								SPDLOG_DEBUG("Error: Number out of range for int::{}", e.what());
								// std::cerr << "Error: Number out of range for int: " << e.what() << std::endl;
							}
							catch (const std::invalid_argument &e)
							{
								// num = 1;
								SPDLOG_DEBUG("Error: Invalid argument:{}", e.what());
								// std::cerr << "Error: Invalid argument: " << e.what() << std::endl;
							}
							goods_msg.task_id = num;
							goods_msg.str = str_list[0];
							m_supply[1].m_goods_msg_queue.push(goods_msg);
							//
						}
						else if (str_list.size() == 1)
						{
							goods_msg.task_id = 0;
							strcpy(m_supply[1].goods_code, str_list[0].c_str());

							goods_msg.str = str_list[0];
							m_supply[1].m_goods_msg_queue.push(goods_msg);
						}
						else
						{
							// err
							// m_supply[1].task_id = 0;
							strcpy(m_supply[1].goods_code, msg_total[0].c_str());

							SPDLOG_DEBUG("feeder 1 recv tag char err......");
						}
					}
					else
					{
						SPDLOG_INFO("sort 1 短时间内接收到2个sku码,舍弃1个,interval={}", interval.count());
					}
					last_time = start_time;
				}
			}
			else if(client_ip.sin_addr.s_addr == m_supply[1].client_ip[1].sin_addr.s_addr)
			{
				char *ip = inet_ntoa(client_ip.sin_addr);
				SPDLOG_DEBUG("feeder 1 metering weight dev ip:{}", ip);
				// 协议
				m_supply[1].weight = std::stof(msg_total[0].substr(11,9)) * 1000 + 1;
				SPDLOG_DEBUG("feeder 1 metering weight ascii:{},data {} g", msg_total[0],m_supply[1].weight);

			}
			else if(client_ip.sin_addr.s_addr == m_supply[1].client_ip[2].sin_addr.s_addr)
			{
				char *ip = inet_ntoa(client_ip.sin_addr);
				
				SPDLOG_DEBUG("feeder 1 metering volume dev ip:{}", ip);
				
				// 按键触发-人为触发测量，赋值给皮带
				if((m_feeder_info.belt_count == BELT3) && (m_feeder_info.volume_key == 1))
				{
					vector<string> str_list;
					string_split(msg_total[0],",",str_list);

					// 协议
				    m_supply[1].st_belt[1].vol.length  = std::stoi(str_list[0])  + 1;
					m_supply[1].st_belt[1].vol.width   = std::stoi(str_list[1])  + 1;
					m_supply[1].st_belt[1].vol.height  = std::stoi(str_list[2])  + 1;

					SPDLOG_DEBUG("feeder 1 metering volume length:{},width:{},height:{}",m_supply[1].st_belt[1].vol.length,m_supply[1].st_belt[1].vol.width,m_supply[1].st_belt[1].vol.height);
				}
				// 自动触扫码
				else 
				{

				}
			}
		}

#if 0
        // 供包台五面扫码值 赋值
		if((strlen(p_supply->st_belt[3].goods_code) == 0 ) && (p_supply->st_belt[3].goods_over == true))
		{
			// 商品码赋值给第四段皮带,但此时应无码值上报超时问题
			if(p_supply->warning == false)
			{
				strcpy(p_supply->st_belt[3].goods_code,msg_total[0].c_str());
			    p_supply->st_belt[3].scan_id = 1;

			    SPDLOG_INFO("WARNING:the goods code come,to belt 4, code:{}" ,p_supply->st_belt[3].goods_code);
			}
			else
			{
				SPDLOG_INFO("WARNING:discard the comed goods code ,too later for belt 4, code:{}" ,p_supply->st_belt[3].goods_code);
			}

			// id
		}
		else
		{
			// 商品码赋值给第3段皮带，并上报
			p_supply->st_belt[2].scan_id = 1;
			strcpy(p_supply->st_belt[2].goods_code,msg_total[0].c_str());
			// 第3段皮带自动扫码，上报 关闭 ljl
			// goods_info_push(p_supply->st_belt[2].goods_code,p_supply->st_belt[3].scan_id,3);
			SPDLOG_INFO("WARNING:the goods code come,to belt 3, code:{}",p_supply->st_belt[2].goods_code);
		}
		//m_dev_manager.plc_dev_manager_net_msg_manage(msg_total); ljl
#endif
	}
	else
	{

	}

	epoll_reset_fd(fd, EPOLLET, true);

	return;
}

/**@brief  plc_agent class 网络节点新接入事件的处理
 * @param[in]  int fd        ---   新接入的连接，文件描述符
 * @return	  NULL
 */
void plc_agent::epoll_newConnection(int fd)
{
	struct sockaddr_in clnt_addr;
	int clnt_socket_fd;

	uint8_t recvBuf[1024] = {0};
	int ret = 999;
	int rs = 1;

	// 将新接入的设备加入TCP Server支持列表
	clnt_socket_fd = m_tcp_server_socket.tcp_socket_accept_client(&clnt_addr);

	// EPOLLIN|EPOLLET 两种方式均支持
	epoll_add_fd(clnt_socket_fd, EPOLLET, true); // 设置EPOLLONESHOT

	epoll_poller_dev_insert(clnt_socket_fd, clnt_addr);
	m_dev_manager.plc_dev_manager_net_fd_init(clnt_socket_fd);

#ifdef PLC_NET_EVENT_LOG
	SPDLOG_INFO("[EPOLL] src:{} a new client connected", clnt_socket_fd);
#endif

#ifdef PLC_AGENT_DEBUG
	OUTPUT_LOG;
	std::cout << "log>>: client info " << clnt_addr.sin_addr.s_addr << clnt_addr.sin_port << "\n"
			  << std::endl;
	display();
#endif
#ifdef PLC_NET_EVENT_LOG
	char *ip_addr = inet_ntoa(clnt_addr.sin_addr);
	SPDLOG_INFO(" client info :{}: {} :{}", clnt_socket_fd, ip_addr, clnt_addr.sin_port);
#endif

	m_current_clnt++;
}

/**@brief  plc_agent class 已接入节点的消息到达事件处理
 * @param[in]  int fd        ---   新接入的连接，文件描述符
 * @return	  NULL
 */
void plc_agent::epoll_existConnection(int fd)
{

	dev_ctrl_func(fd);
}

/**@brief  plc_agent class 主线程操作
 * @param[in]  int timeout      ---   epoll时间监听的超时时间设置
 * @return	  NULL
 */
int plc_agent::net_to_can_recv(int fd)
{
	can_frame can_recv_msg;
	int frame_len = 13;
	int ret = 0;
	int pos = 0;
	char recv_buf[2048] = {0};
	bool result;
	socklen_t clientaddrlen = sizeof(struct sockaddr_in);
	struct sockaddr clentad;
	struct sockaddr_in *p_addr = (struct sockaddr_in *)&client_udp_addr;

	memset(recv_buf,0,sizeof(recv_buf));

    //ret = recv(fd, recv_buf, sizeof(recv_buf), 0);
	ret = recvfrom(fd, recv_buf, 2048, 0,&client_udp_addr,&clientaddrlen);

	//SPDLOG_INFO("recv udp client : client ip :{}, port {}",inet_ntoa(p_addr->sin_addr),ntohs(p_addr->sin_port));
    if(ret < frame_len)
	{
		SPDLOG_INFO("recv length {}",frame_len);
		return -1;
	}
   #if 0 
   // ljl 20240729   
    SPDLOG_INFO("net_to_can recv data,ret = {},recv_buf = {:02x},{:02x},{:02x},{:02x},{:02x},{:02x},{:02x},{:02x},{:02x},{:02x},{:02x},{:02x},{:02x}",ret,
	recv_buf[0],recv_buf[1],recv_buf[2],
	recv_buf[3],
	recv_buf[4],
	recv_buf[5],
	recv_buf[6],
	recv_buf[7],
	recv_buf[8],
	recv_buf[9],
	recv_buf[10],
	recv_buf[11],
	recv_buf[12]

	); 
	#endif 
    for(pos = 0; pos <= ret - frame_len;)
    {
		// tcp:0x0x udp:0x2x
        if((((recv_buf[pos] & 0x20)== 0x20) && (recv_buf[pos + 1] == 0x00) && (recv_buf[pos + 2] == 0x00)))
        {
            can_recv_msg.can_id  = recv_buf[4 + pos] | (recv_buf[3 + pos] << 8) | (recv_buf[2 + pos] << 16) | (recv_buf[1 + pos] << 24);
            can_recv_msg.data[0] = recv_buf[5 + pos];
            can_recv_msg.data[1] = recv_buf[6 + pos];
            can_recv_msg.data[2] = recv_buf[7 + pos];
            can_recv_msg.data[3] = recv_buf[8 + pos];
            can_recv_msg.data[4] = recv_buf[9 + pos];
            can_recv_msg.data[5] = recv_buf[10 + pos];
            can_recv_msg.data[6] = recv_buf[11 + pos];
            can_recv_msg.data[7] = recv_buf[12 + pos];

            pos += frame_len;

            if(can_recv_msg.can_id > 0x500)
            {
                //SPDLOG_INFO("net_to_can recv can_id error,discard the frame data id {}",can_recv_msg.can_id);
                continue;
            }

            //SPDLOG_DEBUG("net recv CAN frame: id:{:x}: data: {:x} {:x} {:x} {:x} {:x} {:x} {:x} {:x} ",
            //    can_recv_msg.can_id, can_recv_msg.data[0], can_recv_msg.data[1], can_recv_msg.data[2], can_recv_msg.data[3],
            //    can_recv_msg.data[4], can_recv_msg.data[5], can_recv_msg.data[6], can_recv_msg.data[7]);
            m_can_data.push(can_recv_msg);
	    }
        else
            pos++;
    }

    return 0;
}
int plc_agent::epoll_main_loop(int timeout)
{
	int event_cnt = -1;
	sockaddr_in clnt_addr;
	struct sockaddr clentad;
	uint32_t cnt_temp[50] = {0};

	int j;
	m_auto_scan_state = 1;

	while (1)
	{

		event_cnt = epoll_wait_fd(timeout);

		if ((event_cnt != -1) && (event_cnt != 0))
		{
			for (int i = 0; i != event_cnt; i++)
			{
				//SPDLOG_INFO("event cnt = {}", event_cnt);
				if (m_events[i].data.fd == m_server_fd)
				{
					m_auto_scan_state = 2;
					epoll_newConnection(m_events[i].data.fd);
				}
				else if ((m_events[i].data.fd == m_udp_server_fd) )
				{
					char recvBuf[2048] = {0};
					int ret = 999;
					bool result;
					socklen_t clientaddrlen = sizeof(struct sockaddr_in); 

					net_to_can_recv(m_udp_server_fd);

				}
				else if (m_events[i].data.fd == m_eventfd)
				{
				}
				else
				{
					cnt_temp[m_events[i].data.fd]++;
					epoll_existConnection(m_events[i].data.fd);
				}
			}
		}

		// 处理内部msg转换处理
		// plc_agent_dev_upload_msg_manage();
		// m_dev_manager.plc_dev_manager_clear_good_info();
	}
}

/**@brief  plc_agent 内部消息处理线程
 * @param[in]  int timeout      ---   epoll时间监听的超时时间设置
 * @return	  NULL
 */
void plc_agent::plc_agent_inner_msg_manage(void)
{
	int event_cnt = -1;
	sockaddr_in clnt_addr;
	uint32_t cnt_temp[50] = {0};

	int j;

	while (1)
	{
		// 处理内部msg转换处理
		plc_agent_dev_upload_msg_manage();
		std::this_thread::sleep_for(std::chrono::microseconds(50));
	}
}

/**@brief  plc_agent class PLC所有switch信息的初始化操作
 * @param[in]  NULL
 * @return	  NULL
 */
void plc_agent::plc_agent_switch_info_init(void)
{
	data_map map_temp;
	int switch_cnt = 0x00;
	int i, j;
	bool result;
	vector<int> switch_no_temp;

	result = m_scheduler_msg.scheduler_manager_get_plc_switch_info(&map_temp);

#ifdef PLC_DEVICE_LOG
	SPDLOG_INFO("[DEV] switch info from data_base result :{}", result);
#endif

	if (true == result)
	{
		for (i = 0; i < map_temp.tunnels_count; i++)
		{
			for (j = 0; j < 8; j++)
			{
				if (!vector_find(switch_no_temp, map_temp.tunnels[i].switches[j]))
				{
					switch_no_temp.push_back(map_temp.tunnels[i].switches[j]);
				}
			}
		}

		m_switch_info.switches_count = switch_no_temp.size();

#ifdef PLC_DEVICE_LOG
		SPDLOG_INFO("[DEVICE] switch info from data_base size :{}", m_switch_info.switches_count);
#endif
	}
	else
	{
#ifdef PLC_AGENT_DEBUG
		OUTPUT_ERR;
		std::cout << "log>>: scheduler_manager_get_plc_switch_info err " << std::endl;
#endif

#ifdef PLC_DEVICE_LOG
		SPDLOG_INFO("[ZMQ] switch info from data_base invalid");
#endif

		m_switch_info.switches_count = 0;
	}

#ifdef PLC_AGENT_DEBUG
	OUTPUT_LOG;
	std::cout << "log>>: switch_no_temp " << switch_no_temp.size() << std::endl;
#endif

	for (i = 0; i < m_switch_info.switches_count; i++)
	{
		m_switch_info.switches[i].switch_id = switch_no_temp[i];
		m_switch_info.switches[i].state = switch_state_def_STATE_UNKNOWN;
		m_switch_info.switches[i].position = switch_position_def_SWITCH_STATE_UNKNOWN;
		m_switch_info.switches[i].err_code = 0x00;
#ifdef PLC_AGENT_DEBUG
		OUTPUT_LOG;
		std::cout << "log>>: switch_no_temp " << i << "  " << m_switch_info.switches[i].switch_id << "  " << m_switch_info.switches[i].state << std::endl;
#endif

#ifdef PLC_DEVICE_LOG
		SPDLOG_INFO("[DEVICE] switch info from data_base no:{} / state:{}", m_switch_info.switches[i].switch_id, m_switch_info.switches[i].state);
#endif
	}
}

/**@brief  plc_agent class 定时上报PLC所有switch状态
 * @param[in]  NULL
 * @return	  NULL
 */
void plc_agent::plc_agent_timed_upload_switch_state()
{
	msg_queue msg;

	while (1)
	{
		msg.type = PLC_SWITCH_STATE_TOTAL_PUB;
		msg.dev_id = 0x00;
		memcpy(msg.msg_data, &m_switch_info, sizeof(m_switch_info));

		m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(msg);

		std::this_thread::sleep_for(std::chrono::seconds(1));
	}
}

/**@brief  plc_agent class 单独上报某switch设备状态
 * @param[in]  int id    			     ---  switch ID
 * @param[in]  switch_state_def state     ---  switch 状态
 * @return	  NULL
 */
void plc_agent::plc_agent_update_switch_state(int id, switch_state_def state)
{
	int i;

	for (i = 0; i < m_switch_info.switches_count; i++)
	{
		if (id == m_switch_info.switches[i].switch_id)
		{
			m_switch_info.switches[i].state = state;

#ifdef PLC_DEVICE_LOG
			SPDLOG_INFO("[DEVICE] switch state update id:{} / state:{}", m_switch_info.switches[i].switch_id, state);
#endif
		}
	}
}

/**@brief  plc_agent class 更新总表内对应switch号的位置信息
 * @param[in]  int id    			     ---  switch ID
 * @param[in]  switch_position_def pos    ---  switch 变轨刀板位置
 * @return	  NULL
 */
void plc_agent::plc_agent_update_switch_postion(int id, switch_position_def pos)
{
	int i;

	for (i = 0; i < m_switch_info.switches_count; i++)
	{
		if (id == m_switch_info.switches[i].switch_id)
		{
			m_switch_info.switches[i].position = pos;

#ifdef PLC_DEVICE_LOG
			SPDLOG_INFO("[DEVICE] switch position update id:{} / position:{}", m_switch_info.switches[i].switch_id, pos);
#endif
		}
	}
}

/**@brief  plc_agent class 心跳检测线程
 * @param[in]  NULL
 * @return	  NULL
 */
void plc_agent::plc_agent_heart_beat_thread_exe()
{
	net_msg msg_temp;

	bool database_dev_info = false;
	bool database_dev_excet_flag = false;
	int dev_cnt = 0;
	int i;

	struct timespec tic_dev;
	struct timespec tic_curr;
	long timedif;

	msg_queue msg;
	event_exception dev_excep;

	// OUTPUT_LOG;

	while (1)
	{
		// OUTPUT_LOG;

		clock_gettime(CLOCK_MONOTONIC, &tic_curr);

		tic_dev = m_dev_manager.plc_dev_manager_get_last_comm_tick();

		timedif = MILLION * (tic_curr.tv_sec - tic_dev.tv_sec) + (tic_curr.tv_nsec - tic_dev.tv_nsec) / 1000;

		if (timedif > PLC_HEART_BEAT_TIME_OUT)
		{
			/// 添加异常处理，待完成
#ifdef PLC_AGENT_DEBUG
			OUTPUT_ERR;
#endif

#ifdef PLC_HEART_BEAT_LOG
			SPDLOG_INFO("[HEART_BEAT] plc device heart beat time out");
#endif
			dev_excep.which_evt_except = 2;

			dev_excep.evt_except.except.src = exception_src_PLANE;
			dev_excep.evt_except.except.level = exception_level_ERROR;
			dev_excep.evt_except.except.code = 0xFF;
			dev_excep.evt_except.except.sub_code = 0xFF;
			// dev_excep.evt_except.except.description = {{NULL}, NULL};
			dev_excep.evt_except.except.dev = m_dev_manager.plc_dev_manager_get_dev_id();

			msg.type = PLC_EXCEP_PUB;
			msg.dev_id = m_dev_manager.plc_dev_manager_get_dev_id();
			;
			memcpy(msg.msg_data, &dev_excep, sizeof(dev_excep));

			m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(msg);
		}

		std::this_thread::sleep_for(std::chrono::seconds(10));
	}
}

/**@brief  plc_agent class 通过ZMQ上报各种消息及状态
 * @param[in]  NULL
 * @return	  NULL
 */
// 上报信息处理 ljl
void plc_agent::plc_agent_dev_upload_msg_manage(void)
{
	msg_queue task_msg_temp;

	plc_dev_state_upload dev_state_up_temp;
	msg_queue msg_temp;
	switch_state_single switch_state_temp;

	slot_state slot_state_temp;

	if (!m_dev_manager.plc_dev_manager_dev_state_queue_empty())
	{
		dev_state_up_temp = m_dev_manager.plc_dev_manager_dev_state_queue_pop();

		if (PLC_MSG_ERR_CODE == dev_state_up_temp.type)
		{
			msg_temp.type = PLC_EXCEP_PUB;
			memcpy(msg_temp.msg_data, dev_state_up_temp.data, dev_state_up_temp.len);
			m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(msg_temp);
			OUTPUT_LOG;
		}
		else if (PLC_MSG_SWITCH_STATUS == dev_state_up_temp.type)
		{
			msg_temp.type = PLC_SWITCH_STATE_PUB;
			memcpy(msg_temp.msg_data, dev_state_up_temp.data, dev_state_up_temp.len);
			m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(msg_temp);
			// 更新总表里对应的设备状态
			SPDLOG_INFO("[ddd] plc_agent_dev_upload_msg_manage");
			memcpy(&switch_state_temp, dev_state_up_temp.data, dev_state_up_temp.len);
			plc_agent_update_switch_postion(switch_state_temp.switch_id, switch_state_temp.position);
			plc_agent_update_switch_state(switch_state_temp.switch_id, switch_state_def_STATE_IDLE);
#ifdef PLC_AGENT_DEBUG
			OUTPUT_LOG;
			std::cout << "log>>: switch_state_temp id " << switch_state_temp.switch_id << std::endl;
			std::cout << "log>>: switch_state_temp position " << switch_state_temp.position << std::endl;
#endif
		}
		else if (PLC_MSG_SLOT_STATUS == dev_state_up_temp.type)
		{
			msg_temp.type = PLC_SLOT_STATE_PUB;
			memcpy(msg_temp.msg_data, dev_state_up_temp.data, dev_state_up_temp.len);
			m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(msg_temp);

			memcpy(&slot_state_temp, dev_state_up_temp.data, dev_state_up_temp.len);
			plc_agent_abnormal_slot_info_update(slot_state_temp);

#ifdef PLC_AGENT_DEBUG
			OUTPUT_LOG;
#endif
		}
		else if (PLC_MSG_GOOD_INFO == dev_state_up_temp.type)
		{
			msg_temp.type = PLC_GOODS_INS_PUB;
			memcpy(msg_temp.msg_data, dev_state_up_temp.data, dev_state_up_temp.len);
			m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(msg_temp);
#ifdef PLC_AGENT_DEBUG
			OUTPUT_LOG;
#endif
		}
		else if (PLC_MSG_GOOD_INFO_END == dev_state_up_temp.type)
		{
			msg_temp.type = PLC_GOODS_INS_PUB;
			memcpy(msg_temp.msg_data, dev_state_up_temp.data, dev_state_up_temp.len);
			m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(msg_temp);
#ifdef PLC_AGENT_DEBUG
			OUTPUT_LOG;
#endif
		}
		else if (PLC_MSG_REBIN_STATUS == dev_state_up_temp.type)
		{
#ifdef PLC_AGENT_DEBUG
			OUTPUT_LOG;
#endif
		}
		else if (PLC_MSG_BUTTON_STATUS == dev_state_up_temp.type)
		{
			msg_temp.type = PLC_BUTTON_STATE_PUB;
			memcpy(msg_temp.msg_data, dev_state_up_temp.data, dev_state_up_temp.len);
			m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(msg_temp);
#ifdef PLC_AGENT_DEBUG
			OUTPUT_LOG;
#endif
			OUTPUT_LOG;
		}
	}
}

/**@brief  plc_agent class PLC管理指令下发线程，由此实现PLC指令的缓存和定时下发
 * @param[in]  NULL
 * @return	  NULL
 */
void plc_agent::plc_agent_dev_download_msg_thread(void)
{
	int switch_opt_id[10];
	int switch_opt_cnt;
	int queue_size = 0;
	int i;
	msg_queue task_msg_temp;
	feeder_cmd feeder_act;
	led_cmd cmd;
	sys_mode_state sys_state_temp;
	// sys_state_temp.state = 1;

	while (1)
	{
#if 0
		if( !m_scheduler_msg.scheduler_manager_switch_open_task_msg_queue_empty() )
		{
			queue_size = m_scheduler_msg.scheduler_manager_get_switch_open_task_msg_size();
			if( PLC_COMM_ROTOTCOL_SWITCH_OPT_MAX_SIZE< queue_size)
			{
				switch_opt_cnt = PLC_COMM_ROTOTCOL_SWITCH_OPT_MAX_SIZE;
			}
			else
			{
				switch_opt_cnt = queue_size;
			}

			for(i=0; i<switch_opt_cnt; i++)
			{
				switch_opt_id[i] = m_scheduler_msg.scheduler_manager_switch_open_task_msg_pop();
				plc_agent_update_switch_state(switch_opt_id[i], switch_state_def_STATE_RUNNING);

			}

			m_dev_manager.plc_dev_manager_get_switch_action(switch_opt_id, switch_opt_cnt, PLC_SWITCH_ACT_OPEN);

		}

		
		queue_size = 0x00;
		switch_opt_cnt = 0x00;

		if( !m_scheduler_msg.scheduler_manager_switch_close_task_msg_queue_empty() )
		{
			queue_size = m_scheduler_msg.scheduler_manager_get_switch_close_task_msg_size();
			if( PLC_COMM_ROTOTCOL_SWITCH_OPT_MAX_SIZE< queue_size)
			{
				switch_opt_cnt = PLC_COMM_ROTOTCOL_SWITCH_OPT_MAX_SIZE;
			}
			else
			{
				switch_opt_cnt = queue_size;
			}

			for(i=0; i<switch_opt_cnt; i++)
			{
				switch_opt_id[i] = m_scheduler_msg.scheduler_manager_switch_close_task_msg_pop();
				plc_agent_update_switch_state(switch_opt_id[i], switch_state_def_STATE_RUNNING);
			}

			m_dev_manager.plc_dev_manager_get_switch_action(switch_opt_id, switch_opt_cnt, PLC_SWITCH_ACT_CLOSE);

		}
#endif
		if (!m_scheduler_msg.scheduler_manager_feeder_task_msg_queue_empty())
		{
			m_scheduler_msg.scheduler_manager_feeder_task_msg_pop(&task_msg_temp);

			switch (task_msg_temp.type)
			{
			case PLC_FEEDER_ACTION:
				memcpy(&feeder_act, task_msg_temp.msg_data, sizeof(feeder_act));
				m_dev_manager.plc_dev_manager_get_feeder_action(&feeder_act);
				break;

			case PLC_LED_CMD:
				memcpy(&cmd, task_msg_temp.msg_data, sizeof(cmd));
				m_dev_manager.plc_dev_manager_send_main_led_cmd(cmd);
				break;

			case PLC_SYS_MODE:
				memcpy(&sys_state_temp, task_msg_temp.msg_data, sizeof(sys_state_temp));
				m_dev_manager.plc_dev_manager_send_system_state(sys_state_temp);
				break;

			case PLC_CLEAR_GOOD:
				m_dev_manager.plc_dev_manager_clear_good_info();
				break;

			default:
				break;
			}
		}
#if 0
		if( m_dev_manager.plc_dev_manager_dev_id_valid() )
		{
			m_scheduler_msg.scheduler_manager_switch_open_task_msg_push(5);
		}
#endif
		std::this_thread::sleep_for(std::chrono::microseconds(10000));
		// usleep(10000);
	}
}

/**@brief  plc_agent class 定时上报PLC所有switch状态
 * @param[in]  NULL
 * @return	  NULL
 */
void plc_agent::plc_agent_timed_upload_slot_state()
{
	msg_queue msg;

	while (1)
	{
		msg.type = PLC_ABNORMAL_SLOT_TOTAL_PUB;
		msg.dev_id = 0x00;
		memcpy(msg.msg_data, &m_abnormal_slot_info, sizeof(m_abnormal_slot_info));

		m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(msg);

		std::this_thread::sleep_for(std::chrono::seconds(5));
	}
}

/**@brief  plc_agent class PLC所有switch信息的初始化操作
 * @param[in]  NULL
 * @return	  NULL
 */
void plc_agent::plc_agent_abnormal_slot_info_init(void)
{
	data_map map_temp;
	int switch_cnt = 0x00;
	int i, j;
	bool result;
	int slot_cnt = 0;

	result = m_scheduler_msg.scheduler_manager_get_plc_switch_info(&map_temp);

#ifdef PLC_DEVICE_LOG
	SPDLOG_INFO("[DEV] switch info from data_base result :{}", result);
#endif

	if (true == result)
	{
		if (map_temp.has_containers)
		{
			// m_abnormal_slot_info.slot_count = map_temp.containers.container_count;
			for (i = 0; i < map_temp.containers.container_count; i++)
			{
				if (data_map_container_type_HOSPICE == map_temp.containers.container[i].type)
				{
					m_abnormal_slot_info.slot[slot_cnt].id = map_temp.containers.container[i].id;
					m_abnormal_slot_info.slot[slot_cnt].state = 2;
					slot_cnt++;
					SPDLOG_INFO("[DEVICE] abnormal slot info  :{} :{}", slot_cnt, m_abnormal_slot_info.slot[i].id);
				}
			}

			m_abnormal_slot_info.slot_count = slot_cnt;
		}

#ifdef PLC_DEVICE_LOG
		SPDLOG_INFO("[DEVICE] abnormal slot info from data_base size :{} :{}", m_abnormal_slot_info.slot_count, map_temp.containers.container_count);
#endif
	}
	else
	{
#ifdef PLC_AGENT_DEBUG
		OUTPUT_ERR;
		std::cout << "log>>: scheduler_manager_get_plc_switch_info err " << std::endl;
#endif

#ifdef PLC_DEVICE_LOG
		SPDLOG_INFO("[ZMQ] switch info from data_base invalid");
#endif

		m_abnormal_slot_info.slot_count = 0;
	}
}

/**@brief  plc_agent class PLC所有switch信息的初始化操作
 * @param[in]  NULL
 * @return	  NULL
 */
void plc_agent::plc_agent_abnormal_slot_info_update(slot_state state_input)
{
	int i;

	for (i = 0; i < m_abnormal_slot_info.slot_count; i++)
	{
		if (m_abnormal_slot_info.slot[i].id == state_input.id)
		{
			// m_abnormal_slot_info.slot[i].id = map_temp.containers.container[i].id;
			m_abnormal_slot_info.slot[i].state = (uint32_t)state_input.st;
			SPDLOG_INFO("[DEVICE] abnormal slot info  :{} :{}", m_abnormal_slot_info.slot[i].id, m_abnormal_slot_info.slot[i].state);
		}
	}
}

// 供包台变量初始化
void plc_agent::package_supply_var_init(supply_package *p_supply, uint16_t id)
{
	struct sockaddr_in client_ip;
	uint8_t ip_base[] = {192, 168, 1, 30};
	//memset(p_supply, 0, sizeof(supply_package));
	p_supply->id = id;
	if (p_supply->id == 0)
	{
		SPDLOG_INFO("feeder 0 package_supply_var_init, {},{},{},{}", m_feeder_info.ip_info.feeder_client_tcp_ip0,m_feeder_info.ip_info.scan_client_tcp_ip0,"************","************");
		// scan   dev
		p_supply->client_ip[0].sin_addr.s_addr = inet_addr(m_feeder_info.ip_info.scan_client_tcp_ip0.c_str());
		// weight dev
		p_supply->client_ip[1].sin_addr.s_addr = inet_addr("************");
		// volume dev
		p_supply->client_ip[2].sin_addr.s_addr = inet_addr("************");
		// feeder
		p_supply->client_ip[3].sin_addr.s_addr = inet_addr(m_feeder_info.ip_info.feeder_client_tcp_ip0.c_str());
	}

	if (p_supply->id == 1)
	{
		SPDLOG_INFO("feeder 1 package_supply_var_init, {},{},{},{}", m_feeder_info.ip_info.feeder_client_tcp_ip1,m_feeder_info.ip_info.scan_client_tcp_ip1,"************","************");
		p_supply->client_ip[0].sin_addr.s_addr = inet_addr(m_feeder_info.ip_info.scan_client_tcp_ip1.c_str());
		p_supply->client_ip[1].sin_addr.s_addr = inet_addr("************");
		p_supply->client_ip[2].sin_addr.s_addr = inet_addr("************");
		
		p_supply->client_ip[3].sin_addr.s_addr = inet_addr(m_feeder_info.ip_info.feeder_client_tcp_ip1.c_str());
	}

	belt *p_belt4 = &p_supply->st_belt[3];
	belt *p_belt3 = &p_supply->st_belt[2];
	belt *p_belt2 = &p_supply->st_belt[1];
	belt *p_belt1 = &p_supply->st_belt[0];

	strcpy(p_belt1->goods_code, "");
	strcpy(p_belt2->goods_code, "");
	strcpy(p_belt3->goods_code, "");
	strcpy(p_belt4->goods_code, "");

	p_belt1->goods = 0;
	p_belt2->goods = 0;
	p_belt3->goods = 0;
	p_belt4->goods = 0;

	strcpy(p_belt1->goods_code_buff[0], "");
	strcpy(p_belt1->goods_code_buff[1], "");
	strcpy(p_belt1->goods_code_buff[2], "");
	strcpy(p_belt1->goods_code_buff[3], "");
	strcpy(p_belt1->goods_code_buff[4], "");

	strcpy(p_belt2->goods_code_buff[0], "");
	strcpy(p_belt2->goods_code_buff[1], "");
	strcpy(p_belt2->goods_code_buff[2], "");
	strcpy(p_belt2->goods_code_buff[3], "");
	strcpy(p_belt2->goods_code_buff[4], "");
	// 清除msg 缓存中的sort信息
	memset(&m_scheduler_msg.m_sort_action_recv[p_supply->id], 0, sizeof(sorting_task_msg));
	m_scheduler_msg.supply_cmd_flag[p_supply->id] = 0;
	
    memset(&p_belt4->task_msg,0,sizeof(p_belt4->task_msg));
    
	//strcpy(p_belt3->task_msg.task_id, "");
	// 控制类指令
	m_dev_manager.stfeeder_ctrl[id].type = 0;
	m_dev_manager.stfeeder_ctrl[id].cmd = 0xff;

	// 初始化指令状态
	p_supply->st_cmd.run_mod = AUTO;
	p_supply->st_cmd.cmd = START;
	p_supply->fault = false;
	p_supply->vsion_scan = false;
	p_supply->vsion_scan_failure = false;

	// 恢复初始值
	p_supply->iCanSocketFd0 = m_CanSocketFd0;

	// 配置参数 ljl 20230314
	p_belt4->code_count = m_feeder_info.code_count;
	p_belt4->gear_ratio = m_feeder_info.belt4_ratio;
	p_belt4->diameter   = m_feeder_info.belt4_diameter;

	p_belt3->code_count = m_feeder_info.code_count;
	p_belt3->gear_ratio = m_feeder_info.belt3_ratio;
	p_belt3->diameter   = m_feeder_info.belt3_diameter;

	p_belt2->code_count = m_feeder_info.code_count;
	p_belt2->gear_ratio = m_feeder_info.belt2_ratio;
	p_belt2->diameter   = m_feeder_info.belt2_diameter;

	p_belt1->code_count = m_feeder_info.code_count;
	p_belt1->gear_ratio = m_feeder_info.belt2_ratio;
	p_belt1->diameter   = m_feeder_info.belt1_diameter;
	// 皮带长度 单位 mm
	p_belt1->belt_len = m_feeder_info.belt1_len;
	p_belt2->belt_len = m_feeder_info.belt2_len;
	p_belt3->belt_len = m_feeder_info.belt3_len;
	p_belt4->belt_len = m_feeder_info.belt4_len;


	m_scheduler_msg.scheduler_manager_set_sort_task_flag(p_supply->id);

	p_supply->m_goods_msg_queue.clear();

	p_supply->action_msg.run_dir_config = m_feeder_info.para.run_dir_config[p_supply->id];

	std::memset(p_supply->sort_info,0,sizeof(p_supply->sort_info));
	p_supply->action_msg.run_dir = 3;
	p_supply->m_req_task_num=0;

	SPDLOG_INFO("package_supply_var_init");
}
void plc_agent::supply_nocode_clear(supply_package *pst_supply)
{
	uint16_t id = pst_supply->id ;
	// 有货无码
	if (pst_supply->warning > 0)
	{
		pst_supply->goods_track_var.flicker[0]++;
		pst_supply->st_cmd.cmd = STOP;
		if (pst_supply->goods_track_var.flicker[0] == 200)
		{
			// 点亮反转的led
			//supply_canio_set(5 , 1, 13+ id);
		}
		else if ((pst_supply->goods_track_var.flicker[0] > 400))
		{
			pst_supply->goods_track_var.flicker[0] = 0;
			//supply_canio_set(5 , 0, 13 + id);
			SPDLOG_INFO("sort id {},the warning is :{}",id,pst_supply->warning);
		}
	}

	return;
}
// 供包台变量初始化
void plc_agent::supply_belt_code_get(supply_package *p_supply)
{
	belt *p_belt4 = &p_supply->st_belt[3];
	belt *p_belt3 = &p_supply->st_belt[2];
	belt *p_belt2 = &p_supply->st_belt[1];
	belt *p_belt1 = &p_supply->st_belt[0];

	if (true)
	{
		// 从缓存中，获取码值
		std::lock_guard<std::mutex> lck(belt4_lock);
		// 码值暂存到缓存
		// strcpy(p_supply->goods_code,msg_total[0].c_str());
		if (p_supply->weight > 0)
		{
			switch (m_feeder_info.belt_count)
			{
				case BELT2:
				{
					break;
				}
				case BELT3:
				case BELT4:
				{
					// 商品码赋值给第3段皮带，并上报
					if (p_belt3->goods == true)
					{
					    if (p_belt3->task_msg.weight == 0)
					    {
							p_belt3->weight = p_supply->weight;
						    p_belt3->task_msg.weight = p_supply->weight;

						    SPDLOG_INFO("id {},the belt3 has get goods weight, weight:{},belt 3 weight {}",p_supply->id,p_belt3->weight, p_supply->weight);
						    p_supply->weight = 0;

							p_supply->sound_ctrl = 1;
							if((p_belt3->weight > m_feeder_info.weight_max) || (p_belt3->weight < m_feeder_info.weight_min))
							{
								p_supply->warning = GOODS_VOER_WEIGHT;
								p_supply->sound_ctrl = 2;
								SPDLOG_INFO("warning:id {}, belt 3 weight {}, over max {},min{}",p_supply->id,p_belt3->weight,m_feeder_info.weight_max,m_feeder_info.weight_min);
							}
					    }
					    else
					    {
						    SPDLOG_INFO("warning : belt3 has goods over and has weight ,discard the weight {} ,belt 3 weight {}", p_supply->weight, p_belt3->task_msg.weight);
						    p_supply->weight = 0;
						    return;
					    }
					}
					else
					{
					    SPDLOG_INFO("warning : belt3 has no goods over ,discard the weight {},belt 3 weight {}", p_supply->weight, p_belt3->task_msg.weight);
					    p_supply->weight = 0;
					    return;
					}
					break;
				}
			}
		}

		// volume
		if (p_supply->vol.length > 0)
		{
			switch (m_feeder_info.belt_count)
			{
				case BELT2:
				{
					break;
				}
				case BELT3:
				case BELT4:
				{
					// 商品码赋值给第3段皮带，并上报
					if ((p_belt3->goods == true) || (m_feeder_info.volume_key == 1))
					{
					    if (p_belt3->task_msg.vol.length == 0)
					    {
							p_belt3->vol = p_supply->vol;
						    p_belt3->task_msg.vol = p_supply->vol;

						    SPDLOG_INFO("id {},the belt3 has get goods vol, length:{},width:{},height{}",p_supply->id, p_belt3->vol.length, p_belt3->vol.width,p_belt3->vol.height);
						   
							memset(&p_supply->vol,0, sizeof(p_supply->vol));

							//p_supply->sound_ctrl = 1;
							if(p_belt3->vol.length > m_feeder_info.len_max)
							{
								p_supply->warning = LENGTH_OVER_LIMIT;
								//p_supply->sound_ctrl = 2;
								SPDLOG_INFO("warning: belt 3 vol {}, over max {}",p_belt3->vol.length,m_feeder_info.len_max);
							}
					    }
					    else
					    {
						    SPDLOG_INFO("warning : belt3 has goods over and has vol ,discard the vol length:{},width:{},height{}", p_supply->vol.length, p_supply->vol.width,p_supply->vol.height);
						    memset(&p_supply->vol,0 ,sizeof(p_supply->vol));
						    return;
					    }
					}
					else if((p_belt3->goods == false) || (m_feeder_info.volume_key == 0))
					{
						// 自动测量时，体积上报数据为完全进入皮带才能测量
					    SPDLOG_INFO("warning : belt3 has no goods over ,discard the vol length:{},width:{},height{}", p_supply->vol.length, p_supply->vol.width,p_supply->vol.height);
					     memset(&p_supply->vol,0, sizeof(p_supply->vol));
					    return;
					}
					break;
				}
			}
		}

		if ((strlen(p_supply->goods_code) > 0) && ((p_belt3->weight > 0) || (m_feeder_info.weight_enable == false)))
		{
			// 供包台五面扫码值 赋值
			// if ((strlen(p_belt4->goods_code) == 0) && (p_belt4->goods_over == true))
			if (0)
			{
				// 商品码赋值给第四段皮带,但此时应无码值上报超时问题
				if (p_supply->warning == false)
				{
					strcpy(p_belt4->goods_code, p_supply->goods_code);
					strcpy(p_supply->goods_code, "");
					p_belt4->scan_id = 1;

					SPDLOG_INFO("WARNING:the goods code come,to belt 4, code:{}", p_belt4->goods_code);
				}
				else
				{
					SPDLOG_INFO("WARNING:discard the comed goods code ,too later for belt 4, code:{}", p_belt4->goods_code);
				}

				// id
			}
			else
			{
				#if 0
				// #ifdef TWO_BELT
				//  2段皮带，如果此时有货，丢弃码值任务重复码值
				if ((p_belt3->task_msg.task_id != 0) && (m_feeder_info.belt_count == BELT2) && (m_feeder_info.double_feeder_scan == 1))
				{
					SPDLOG_INFO("two belt ,discard the scan code {},for has task id task {} ", p_supply->goods_code,p_belt3->task_msg.task_id);
					strcpy(p_supply->goods_code, "");
					return;
				}
				#endif
				// @@@@@@ 注意 @@@@@@  20231016 指针修改blet 4 包含 1段和2两段模型
				if((m_feeder_info.double_feeder_scan == 2) && (m_feeder_info.belt_count <= BELT2))
				{
					p_belt3 = p_belt4;
					SPDLOG_INFO("WARNING:the goods code come,to belt 3 = belt 4, ");
				}
				// 商品码赋值给第3段皮带，并上报
				if (p_belt3->goods_over == true)
				{
					if (strlen((char*)(p_belt3->task_msg.task_id)) == 0)
					{
						p_belt3->scan_id = p_supply->scan_id;
						p_supply->scan_id = 0;
						strcpy(p_belt3->goods_code, p_supply->goods_code);
						strcpy(p_supply->goods_code, "");
						SPDLOG_INFO("belt 3 get the code {},scan id {}",p_belt3->goods_code,p_belt3->scan_id);

						if(m_feeder_info.noread_stop == 1)
						{
							if( strcmp(p_belt3->goods_code, "noread") == 0)
							{
								p_supply->warning = GOODS_NO_READ;
								SPDLOG_INFO("warning: belt 3 goods code noread,and stop {},noread_stop {}",p_belt3->goods_code,m_feeder_info.noread_stop);
								return;
							}
						}
					}
					else
					{
						// 
						SPDLOG_INFO("warning : belt3 has goods over and has goods code,discard the goods code{} ,task id {}", p_supply->goods_code,p_belt3->task_msg.task_id);
						strcpy(p_supply->goods_code, "");
						return;
					}
				}
				else
				{
					SPDLOG_INFO("warning : belt3 has not goods over ,but has goods code ,discard the code {}", p_supply->goods_code);
					strcpy(p_supply->goods_code, "");
					return;
				}

				// 第3段皮带自动扫码，上报 关闭 ljl
				if (p_supply->st_cmd.cmd == START)
				{
					// 只有在2段皮带且在第一段扫码时需要判定noread ，此时noread不上报码值、即不请求任务
					if ((m_feeder_info.double_feeder_scan == 2) ||  (m_feeder_info.belt_count != BELT2) || strcmp(p_belt3->goods_code, "noread") != 0)
					{
						if (strlen((char*)(p_belt3->task_msg.task_id)) == 0)
						{
							goods_info_push(p_supply, p_belt3->goods_code, p_belt3->scan_id, 3,0);

						}
					}
					SPDLOG_INFO("WARNING:the goods code come,to belt 3, code:{},weight {}", p_belt3->goods_code, p_belt3->weight);
				}
			}
		}
		else if ((strlen(p_supply->goods_code) > 0) && ((p_supply->weight == 0) || (m_feeder_info.weight_enable == false)))
		{
			//
		}


	}
	return;
}

void plc_agent::supply_task_cancel(supply_package *p_supply)
{
	belt *p_belt4 = &p_supply->st_belt[3];
	belt *p_belt3 = &p_supply->st_belt[2];
	belt *p_belt2 = &p_supply->st_belt[1];
	belt *p_belt1 = &p_supply->st_belt[0];

	static uint8_t ucase = 1;

	

	// if (ucase == 2)
	if (1)
	{
		uint16_t &pub_count = p_belt4->cancel_num;
		uint16_t &pub_lag = p_belt4->cancel_count;

		if ((pub_lag > 0) && (pub_lag <= 2))
		{
			if (m_scheduler_msg.m_sort_cancel_flag[p_supply->id] == 0)
			{
				pub_count++;
			}
			else
			{
				pub_lag = 0xff;
				m_scheduler_msg.m_sort_cancel_flag[p_supply->id] = 0;
			}
		}
		else if (pub_lag > 2)
		{
			// package_supply_var_init(p_supply);
			memset(&p_belt4->task_msg, 0, sizeof(p_belt4->task_msg));
			if (pub_lag != 0xff)
			{
				if (pub_lag == 3)
					SPDLOG_INFO("cancel:belt4 failure ");
				pub_lag = 0xfe;
			}
		}
		SPDLOG_INFO("feeder id {},supply cmd {}, container {},task id {},cancel:belt4 count {}",p_supply->id,m_scheduler_msg.supply_cmd_flag[p_supply->id],p_belt4->task_msg.container,p_belt4->task_msg.task_id,pub_lag);
		// 1.第4段处理方案：如果第4段有格口信息，但是没有请求后进行应答（无请求），则需要供包机维护兜底完成，否则调度维护
		// 如果收到请求并 应答，调度维护商品信息。
		if (1)
		{
			if (((pub_lag == 0) && (1)) || pub_count > 20)
			{
				sorting_task_state_msg task_state;
				msg_queue task_cancel_msg;

				pub_count = 0;
				// 4段皮带是否已经接收商品信息
				if ((p_belt4->task_msg.containers[0] != 0) || (strlen((char *)p_belt4->task_msg.task_id) > 0))
				{
#if 1              
                    task_state.vehicle_id = p_supply->id;
					task_state.state = sorting_task_state_FINISHED_TOHOSPICE;
					task_state.container = p_belt4->task_msg.container;
					//task_state.exp_info  = p_supply->warning;
					strcpy((char *)task_state.task_id, (const char *)p_belt4->task_msg.task_id);
					strcpy(task_state.gd_codes, p_belt4->task_msg.gd_codes);
                    // cancle cantainer change
					if(m_feeder_info.cancel_container_change == 1)
					{
						task_state.container = m_feeder_info.cancel_container_id;
					}
					// publish
					//  消息发布队列
					pub_lag++;
					SPDLOG_INFO("cancel:belt4 count {}", pub_lag);

					task_cancel_msg.type = PLC_SORT_CANCEL_PUB;
					memcpy(task_cancel_msg.msg_data, &task_state, sizeof(task_state));
					if (pub_lag < 3)
						m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(task_cancel_msg);

					SPDLOG_INFO("cancel:belt4 goods code {}", task_state.gd_codes);
					SPDLOG_INFO("cancel:belt4 task id {}", task_state.task_id);
					SPDLOG_INFO("cancel:belt4 container id {}", task_state.container);
#endif
				}
				else
				{
					pub_lag = 0xff;
					p_belt4->task_msg.container = 0;
					// 停止4段皮带获取商品信息，禁止发布消息
				}

				// m_scheduler_msg.supply_cmd_flag = 0;
			}
		}
		else
		{
			pub_lag = 0xff;
		}
	}


	// 2.第3段如果有商品码-对应已pub信息，则需要发送兜底完成，并追溯兜底完成，不超3次


    sorting_task_msg task_msg;

	if (!p_supply->m_sort_task_queue.empty())
	{
		p_supply->m_sort_task_queue.pop(task_msg);

		{
			sorting_task_state_msg task_state;
			msg_queue task_cancel_msg;

			//pub_count = 0;
			m_scheduler_msg.sort_cmd_flag = 0;
			task_state.vehicle_id = p_supply->id;
			task_state.state = sorting_task_state_FINISHED_TOHOSPICE;
			task_state.container = task_msg.container;
			task_state.exp_info  = p_supply->warning;
			strcpy((char *)task_state.task_id, (const char *)task_msg.task_id);
			strcpy(task_state.gd_codes, task_msg.gd_codes);

			// cancle cantainer change
			if ((m_feeder_info.cancel_container_change == 1))//&& (p_belt3->task_msg.container != 0))
			{
				task_state.container = m_feeder_info.cancel_container_id;
			}

			// 消息发布队列

			task_cancel_msg.type = PLC_SORT_CANCEL_PUB;
			memcpy(task_cancel_msg.msg_data, &task_state, sizeof(task_state));

			m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(task_cancel_msg);
			m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(task_cancel_msg);
			m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(task_cancel_msg);

			SPDLOG_INFO("cancel:task queue goods code {}", task_state.gd_codes);
			SPDLOG_INFO("cancel:task queue task id {}", task_state.task_id);
			SPDLOG_INFO("cancel:task queue container id {}", task_state.container);

			p_supply->m_sort_task_queue.clear();
		}

	}

	

	if( (strlen((char *)p_supply->m_sort_req_task.task_id) > 0))
	{
		sorting_task_msg task_msg = p_supply->m_sort_req_task;
		
		{
			sorting_task_state_msg task_state;
			msg_queue task_cancel_msg;

			// strcpy(p_belt3->goods_code, "");
			//pub_count = 0;
			m_scheduler_msg.sort_cmd_flag = 0;
			task_state.vehicle_id = p_supply->id;
			task_state.state = sorting_task_state_FINISHED_TOHOSPICE;
			task_state.container = task_msg.container;
			task_state.exp_info  = p_supply->warning;
			strcpy((char *)task_state.task_id, (const char *)task_msg.task_id);
			strcpy(task_state.gd_codes, task_msg.gd_codes);

			// cancle cantainer change
			if ((m_feeder_info.cancel_container_change == 1))//&& (p_belt3->task_msg.container != 0))
			{
				task_state.container = m_feeder_info.cancel_container_id;
			}

			// 消息发布队列

			task_cancel_msg.type = PLC_SORT_CANCEL_PUB;
			memcpy(task_cancel_msg.msg_data, &task_state, sizeof(task_state));

			m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(task_cancel_msg);
			m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(task_cancel_msg);
			m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(task_cancel_msg);

			//p_supply->m_sort_req_task.task_id = 0;
			strcpy((char *)p_supply->m_sort_req_task.task_id, "");

			SPDLOG_INFO("cancel:belt goods code {}", task_state.gd_codes);
			SPDLOG_INFO("cancel:belt task id {}", task_state.task_id);
			SPDLOG_INFO("cancel:belt container id {}", task_state.container);
		}

	}

	return;
}

int plc_agent ::supply_ctrl(supply_package *p_supply)
{
	uint8_t &reverse_flag = p_supply->reverse_flag;

	belt *p_belt4 = &p_supply->st_belt[3];
	belt *p_belt3 = &p_supply->st_belt[2];
	belt *p_belt2 = &p_supply->st_belt[1];
	belt *p_belt1 = &p_supply->st_belt[0];


	// 等待伺服初始化通过
    
    // 
	//p_supply->warning    = 0;
	p_belt1->servo_state = 0;
	p_belt1->servo_state = 0;
	p_belt1->servo_state = 0;
	p_belt4->servo_state = 0;

    if(p_supply->dev_heartbeat > 0)
	{
		p_supply->dev_heartbeat++;
		if(p_supply->dev_heartbeat > 80)
		{
			p_supply->warning  = 14;
		}
		else
		{

		}
	}

	// test
	p_supply->st_cmd.run_mod = AUTO;

	if(p_supply->warning > 0)
	{
		if(p_supply->st_cmd.cmd != STOP)
		{
			SPDLOG_INFO("id {} ,feeder state {},to {},for warning = {} ",p_supply->id,p_supply->st_cmd.cmd,STOP,p_supply->warning);
		}
		p_supply->st_cmd.cmd = STOP;

	}
	else
	{
		p_supply->st_cmd.cmd = START;
	}
    
	//p_supply->st_key.reverse = false;

	//  正向控制
	if (p_supply->st_key.reverse == false)
	{
		// 判断上个周期是否反转，如果是则初始化变量
		if (reverse_flag == 1)
		{
			reverse_flag = 0;

			package_supply_var_init(p_supply, p_supply->id);

			SPDLOG_INFO("=========init finished========");
		}
		//supply_belt_code_get(p_supply);
	

		if ((p_supply->st_cmd.run_mod == AUTO) )
		{
			// 启动
			if (p_supply->st_cmd.cmd == START)
			{
				supply_belt_ctrl(p_supply);
			}
			else if(p_supply->st_cmd.cmd == STOP)
			{
				p_supply->action_msg.run_dir =  3;
			}
		}

	}
	// 反转控制
	else if (p_supply->st_key.reverse == true)
	{
		reverse_flag = 1;
		supply_task_cancel(p_supply);
		// 3.请求任务取消，等待应答
		supply_reverse_ctrl(p_supply);     
		std::this_thread::sleep_for(std::chrono::milliseconds(5));
		return 0;
	}

	//supply_goods_code(p_supply);

	supply_nocode_clear(p_supply);
    if(m_feeder_info.auto_feeder == 1)
	{
		servo_ctrl2(p_supply);
	}
	else
	{
		servo_ctrl(p_supply);
	}
	
	feeder_state_info_push(p_supply);

	if(servo_dev[p_supply->id].get_error_code() > 0)
	{
		p_supply->warning = servo_dev[p_supply->id].get_error_code();

	}

	if(m_feeder_info.feeder_count == 1)
	{
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
	}
	else
	{
		std::this_thread::sleep_for(std::chrono::milliseconds(5));

	}
	
	return 0;
}

/**@brief  plc_agent class 供包台皮带控制主线程
 * @param[in]  NULnL
 * @return	  NULL
 */
void plc_agent::package_supply_manage(void)
{
	SPDLOG_INFO(" supply manage program init");

	uint8_t reverse_flag = 0;
	// 1.配置检查
	if (m_feeder_info.feeder_count > 2)
	{
		SPDLOG_INFO(" please check the json config file,the feeder count over 2, err ");
		std::this_thread::sleep_for(std::chrono::milliseconds(3000));
	}
	// 2.init
	for (int i = 0; i < m_feeder_info.feeder_count; i++)
	{
		// 供包机id
		package_supply_var_init(&m_supply[i], i);
	}

	std::this_thread::sleep_for(std::chrono::milliseconds(1000));
	SPDLOG_INFO(" supply manage program run ");
	// 3.供包逻辑
	while (1)
	{
		sys_time++;
		for (int i = 0; i < m_feeder_info.feeder_count; i++)
		{
			// 皮带控制
			supply_ctrl(&m_supply[i]);
			// 供包台
		}

		// std::this_thread::sleep_for(std::chrono::milliseconds(10));
	}
}

// 多面扫触发控制
int16_t plc_agent::supply_vsion_scan(supply_package *p_supply)
{
	belt *p_belt4 = &p_supply->st_belt[3];
	belt *p_belt3 = &p_supply->st_belt[2];
	belt *p_belt2 = &p_supply->st_belt[1];
	belt *p_belt1 = &p_supply->st_belt[0];

	if (p_supply->st_cmd.cmd != START)
	{
#if 1
		// 五面扫停止scan
		if (p_supply->vsion_scan == true)
		{
			p_supply->vsion_scan = false;
			SPDLOG_INFO("the vsion scan stop");
		}
#endif
		// p_supply->vsion_scan = false;
	}
}
// 供包机包裹码值跟踪
int16_t plc_agent::supply_goods_code(supply_package *p_supply)
{
	belt *p_belt4 = &p_supply->st_belt[3];
	belt *p_belt3 = &p_supply->st_belt[2];
	belt *p_belt2 = &p_supply->st_belt[1];
	belt *p_belt1 = &p_supply->st_belt[0];

	uint8_t id = p_supply->id;

	// 监测第四段 的商品码，如有商品码，则上报
	if ((strlen(p_belt4->goods_code) > 0) && (strlen(p_supply->goods_track_var.last_code) == 0))
	{
		p_supply->goods_track_var.push_count++;
		// goods_info_push(p_belt4->goods_code,p_belt4->scan_id,4);

		SPDLOG_INFO("====== publish count: {},belt4 goods code:{}", p_supply->goods_track_var.push_count, p_belt4->goods_code);
	}
	strcpy(p_supply->goods_track_var.last_code, p_belt4->goods_code);

	// 第3段皮带有商品码（商品和第3段皮带有接触），1 正常上升沿触发，2.如果有商品横跨第3和第4段皮带，此时code需要使用下降沿赋值
	// *第3段有商品码（两种情况），需要进行码值跟踪，只有第4段无货且无码的情况下，才能赋值
	if ((strlen(p_belt3->goods_code) > 0) && (strlen(p_belt4->goods_code) == 0) && (p_belt4->goods == false))
	{
		// if((p_belt4->sensor_up != sensor_up_bak))
		// 如果是两端皮带供包段扫码，则不在这里处理码值跟踪 20230412
		if (p_belt4->sensor_up == true && (m_feeder_info.double_feeder_scan == 1))
		{
			// 2023 06 04 移植到belt 4 处理码值数据交接，以前在这里处理主要是为了4段提前报码，现在没必要了
			#if 0 
			// p_belt4->goods_code = p_belt3->goods_code;
			p_belt4->scan_id = p_belt3->scan_id;
			p_belt3->scan_id = 0;
			strcpy(p_belt4->goods_code, p_belt3->goods_code);
			strcpy(p_belt3->goods_code, "");
			SPDLOG_INFO("belt3 code to belt4: goods code \n1:{}\n2:{}\n3:{}\n4:{}", p_belt1->goods_code, p_belt2->goods_code, p_belt3->goods_code, p_belt4->goods_code);
			// 上报商品码值
			// goods_info_push(p_belt4->goods_code,0,4);
			#endif 
		}
	}
	// 20230306
	// sensor_up_bak = p_belt4->sensor_up;

	// 第四段皮带商品码检测机制,有货无码时间超过1s钟，则强制赋值“noread”
	if ((strlen(p_belt4->goods_code) == 0) && (p_belt4->goods_over == true))
	{
		p_supply->goods_track_var.uTime++;
		if (p_supply->goods_track_var.uTime == 500)
		{
			// p_belt4->scan_id = 0;
			// strcpy(p_belt4->goods_code,"noread");
			p_supply->warning = BELT4_HAS_GOODS_NO_CODE;
			SPDLOG_INFO("warning: the belt 4  no code ,time out");
		}
	}
	else
	{
		p_supply->goods_track_var.uTime = 0;
	}

	// 上包任务 启动任务
	if (m_dev_manager.stfeeder_ctrl[id].type == 1 && m_dev_manager.stfeeder_ctrl[id].cmd == 0)
	{

		//p_supply->st_cmd.cmd = START;
		// p_supply->sort_ready = 1;
		m_dev_manager.stfeeder_ctrl[id].type = 0;
		m_dev_manager.stfeeder_ctrl[id].cmd = 0xff;
		SPDLOG_INFO(" =====recv sort ready ,feeder id {},feeder cmd : start  =======", p_supply->id);
	}
	// 反转
	else if (m_dev_manager.stfeeder_ctrl[id].type == 1 && m_dev_manager.stfeeder_ctrl[id].cmd == 1)
	{
		//p_supply->st_cmd.cmd = REVE;

		m_dev_manager.stfeeder_ctrl[id].type = 0;
		m_dev_manager.stfeeder_ctrl[id].cmd = 0xff;

		SPDLOG_INFO(" =====recv sort ready ,feeder id {},feeder cmd : recv  =======", p_supply->id);
	}
	// stop
	else if (m_dev_manager.stfeeder_ctrl[id].type == 1 && m_dev_manager.stfeeder_ctrl[id].cmd == 2)
	{
		if(p_supply->st_cmd.cmd != STOP)
		{
			SPDLOG_INFO(" =====recv sort ready ,feeder id {},feeder cmd : stop  =======", p_supply->id);
		}
		
		//p_supply->st_cmd.cmd = STOP;
		m_dev_manager.stfeeder_ctrl[id].type = 0;
		m_dev_manager.stfeeder_ctrl[id].cmd = 0xff;
		
	}
	// clear
	else if (m_dev_manager.stfeeder_ctrl[id].type == 1 && m_dev_manager.stfeeder_ctrl[id].cmd == 3)
	{
		//p_supply->st_cmd.cmd = START;
		m_dev_manager.stfeeder_ctrl[id].type = 0;
		m_dev_manager.stfeeder_ctrl[id].cmd = 0xff;
		SPDLOG_INFO(" =====recv sort ready ,feeder id {},feeder cmd : clear  =======", p_supply->id);
	}
	// reset
	else if (m_dev_manager.stfeeder_ctrl[id].type == 1 && m_dev_manager.stfeeder_ctrl[id].cmd == 4)
	{
		//p_supply->st_cmd.cmd = START;

		m_dev_manager.stfeeder_ctrl[id].type = 0;
		m_dev_manager.stfeeder_ctrl[id].cmd = 0xff;

		SPDLOG_INFO(" =====recv sort ready ,feeder id {},feeder cmd : reset  =======", p_supply->id);
	}
	else if (m_dev_manager.stfeeder_ctrl[id].type == 1 && m_dev_manager.stfeeder_ctrl[id].cmd == 5)
	// 动作指令
	{
		p_supply->goods_track_var.supply_cmd++;
		p_supply->sort_ready = 1;
		m_dev_manager.stfeeder_ctrl[id].type = 0;
		m_dev_manager.stfeeder_ctrl[id].cmd = 0xff;

		SPDLOG_INFO("====== recv sort ready ,feeder cmd :feeder id {}, supply  {}=====", p_supply->id, p_supply->goods_track_var.supply_cmd);
	}
	else if (m_dev_manager.stfeeder_ctrl[id].type == 1 && m_dev_manager.stfeeder_ctrl[id].cmd == 6)
	// 五面扫控制指令
	{
		p_supply->vsion_scan = true;

		if (p_supply->goods_track_var.ucount == 0)
			SPDLOG_INFO("====== recv scan cmd ,feeder id {},start =====", p_supply->id);

		if (p_supply->vsion_scan == true)
		{
			p_supply->goods_track_var.ucount++;
			if (p_supply->goods_track_var.ucount > 300)
			{
				p_supply->goods_track_var.ucount = 0;
				p_supply->vsion_scan = false;
				m_dev_manager.stfeeder_ctrl[id].type = 0;
				m_dev_manager.stfeeder_ctrl[id].cmd = 0xff;

				SPDLOG_INFO("====== recv scan cmd time over,feeder id {},stop =====", p_supply->id);
			}
		}
	}
	// 皮带控制
	else if (m_dev_manager.stfeeder_ctrl[id].type == 2 && m_dev_manager.stfeeder_ctrl[id].cmd == 5)
	{
		if (m_dev_manager.stfeeder_ctrl[id].part < 5 && m_dev_manager.stfeeder_ctrl[id].part > 0)
		{
			if (fabs(m_dev_manager.stfeeder_ctrl[id].belt_speed) < BELT4_SPEED)
			{
				p_supply->st_belt[0].belt_spd = 0;
				p_supply->st_belt[1].belt_spd = 0;
				p_supply->st_belt[2].belt_spd = 0;
				p_supply->st_belt[3].belt_spd = 0;
				p_supply->st_belt[m_dev_manager.stfeeder_ctrl[id].part - 1].belt_spd = m_dev_manager.stfeeder_ctrl[id].belt_speed;
			}

			SPDLOG_INFO("====== recv scan cmd ,start  belt = {},spd= {}=====", m_dev_manager.stfeeder_ctrl[id].part, m_dev_manager.stfeeder_ctrl[id].belt_speed);
		}

		// m_dev_manager.stfeeder_ctrl.type = 0;
		// m_dev_manager.stfeeder_ctrl.cmd  = 0xff;
	}
}
// 供包台传感器信号预处理
int16_t plc_agent::supply_sensor_filer(supply_package *p_supply)
{
	belt *p_belt4 = &p_supply->st_belt[3];
	belt *p_belt3 = &p_supply->st_belt[2];
	belt *p_belt2 = &p_supply->st_belt[1];
	belt *p_belt1 = &p_supply->st_belt[0];

	static uint16_t ucount[2][5] = {0, 0, 0, 0,0};
	// 第2段皮带传感器过滤，依据第3段光幕传感判定;add 按键触发体积测量时，大眼扫获得码值认为有货，不计时
	if ((m_feeder_info.belt_count == BELT4)&&(p_belt2->goods == true) && (p_belt2->belt_spd > 20) && (p_belt3->sensor_up == false) && (p_belt3->goods == false))
	{
		ucount[p_supply->id][0]++;
		if (ucount[p_supply->id][0] > 200)
		{
			p_belt2->goods = false;
			p_belt2->sensor_cnt = 0;
			p_supply->warning = BELT3_SESOR_NOT_EXP_UP;
			strcpy(p_belt2->goods_code, "");
			SPDLOG_INFO("warning: id {}, filter:belt2 set goods false",p_supply->id);
		}
	}
	else
	{
		ucount[p_supply->id][0] = 0;
	}
	// 第3段皮带传感器过滤，依据第4段光幕传感判定,第4段货物检测传感器失效模式检测:第3段有货，第4段皮带一定时间没有检测到，即为异常
	if ((p_belt3->task_msg.container != 0) && (p_belt3->belt_spd > 20) && (p_belt4->sensor_up == false) && (p_belt4->goods == false))
	{
		ucount[p_supply->id][1]++;
		if (ucount[p_supply->id][1] > 150)
		{
			p_belt3->goods = false;
			p_belt3->goods_over = false;
			p_belt3->sensor_cnt = 0;
			// 异常触发
			p_supply->warning = BELT4_SESOR_NOT_EXP_UP;
			// 清除商品码 2022/07/13
			// strcpy(p_belt3->goods_code,"");
			SPDLOG_INFO(" warning:id {}, check the belt4 sensor ,no exp 'up' single",p_supply->id);
		}
	}
	else
	{
		ucount[p_supply->id][1] = 0;
	}
    // belt 4 皮带传感器一直触发 检测
	if((p_belt4->sensor_up == true ) && (p_belt3->goods == false) && (m_feeder_info.belt_count > BELT2))
	{
		ucount[p_supply->id][2]++;
		if ((ucount[p_supply->id][2] % 100 )== 5)
		{
            p_supply->warning = BELT4_SESOR_UP_NOT_EXP;
			SPDLOG_INFO(" warning:id {}, check the belt4 sensor ,the 'up' single is not exp " ,p_supply->id);
		}
	}
	else
	{
		ucount[p_supply->id][2] = 0;
	}

	// 4 段皮带 belt 3 皮带传感器一直触发 检测
	if((p_belt3->sensor_up == true ) && (p_belt2->goods == false) && (m_feeder_info.belt_count == BELT4))
	{
		ucount[p_supply->id][3]++;
		if ((ucount[p_supply->id][3] % 100 )== 5)
		{
            p_supply->warning = BELT3_SESOR_UP_NOT_EXP;
			SPDLOG_INFO(" warning:id {}, 4 belt feeder check the belt3 sensor ,the 'up' single is not exp " ,p_supply->id);
		}
	}
	else
	{
		ucount[p_supply->id][3] = 0;
	}

    // 超高检测
	if(p_supply->st_key.limit_height == true )
	{
		ucount[p_supply->id][4]++;

		if ((ucount[p_supply->id][4] % 100 )== 5)
		{
			 p_supply->warning = HINGHT_OVER_LIMIT;
			 SPDLOG_INFO(" warning:id {},  the goods too high over limit" ,p_supply->id);
		}

	}
	else
	{
		ucount[p_supply->id][4] = 0;

	}

    static uint32_t sound_count[2][3] = {0};

    // sound 通过
	if(p_supply->sound_ctrl > 0 )
	{
		if (p_supply->sound_ctrl == 1)
		{
			sound_count[p_supply->id][0]++;

			//if(sound_count[p_supply->id][0]  == 1)
			if((sound_count[p_supply->id][0] == 1) || (sound_count[p_supply->id][0] == 10) || (sound_count[p_supply->id][0] == 20))
			{
                // start
				supply_canio_set(5 + 5 * p_supply->id, 1, 15);
				SPDLOG_INFO("success:id {},the pass sound,", p_supply->id);
				if(sound_count[p_supply->id][0] == 1)
				supply_canio_set(5 + 5 * p_supply->id, 0, 16);
			}
			else if (sound_count[p_supply->id][0]  == 50)
			{
                // end 
				supply_canio_set(5 + 5 * p_supply->id, 0, 15);
			
				SPDLOG_INFO("warning:id {},stop the weight sound", p_supply->id);
				p_supply->sound_ctrl = 0;
			}

		}
        // 回退
		else if (p_supply->sound_ctrl == 2)
		{
			sound_count[p_supply->id][1]++;

			if((sound_count[p_supply->id][1] == 1) || (sound_count[p_supply->id][1] == 10) || (sound_count[p_supply->id][1] == 20))
			{
                // start
				supply_canio_set(5 + 5 * p_supply->id, 1, 16);
				SPDLOG_INFO("warning:id {},the over weight sound", p_supply->id);
			}
			else if ((sound_count[p_supply->id][1] % 50 == 49 ))
			{
                // end 
				supply_canio_set(5 + 5 * p_supply->id, 0, 16);
				SPDLOG_INFO("warning:id {},stop the weight sound", p_supply->id);

				if(sound_count[p_supply->id][1] > 150)
				{
					p_supply->sound_ctrl = 0;
				}
				
			}

		}
		else
		{
			sound_count[p_supply->id][0] = 0;
		    sound_count[p_supply->id][1] = 0;
		    sound_count[p_supply->id][2] = 0;

		}

	}
	else
	{
			sound_count[p_supply->id][0] = 0;
		    sound_count[p_supply->id][1] = 0;
		    sound_count[p_supply->id][2] = 0;
	}

#if 0
    if((p_belt4->belt_spd > 100) )
	{
		if(count >)
	}
#endif
	return 0;
}
// 供包台反向运行控制
int16_t plc_agent::supply_reverse_ctrl(supply_package *p_supply)
{
	belt *p_belt4 = &p_supply->st_belt[3];
	belt *p_belt3 = &p_supply->st_belt[2];
	belt *p_belt2 = &p_supply->st_belt[1];
	belt *p_belt1 = &p_supply->st_belt[0];

	// 下使能 todo
	p_belt1->belt_ena = 1;
	p_belt2->belt_ena = 1;
	p_belt3->belt_ena = 1;
	p_belt4->belt_ena = 1;

	// 速度
	p_belt1->belt_spd = -BELT1_SPEED;
	p_belt2->belt_spd = -BELT2_SPEED;
	p_belt3->belt_spd = -BELT3_SPEED;
	p_belt4->belt_spd = -BELT4_SPEED;

	// p_belt1->goods = false;
	p_belt2->goods = false;
	p_belt3->goods = false;
	p_belt4->goods = false;

	// strcpy(p_belt1->goods_code,"");
	strcpy(p_belt2->goods_code, "");
	strcpy(p_belt3->goods_code, "");
	strcpy(p_belt4->goods_code, "");

	strcpy(p_belt1->goods_code_buff[0], "");
	strcpy(p_belt1->goods_code_buff[1], "");
	strcpy(p_belt1->goods_code_buff[2], "");
	strcpy(p_belt1->goods_code_buff[3], "");
	strcpy(p_belt1->goods_code_buff[4], "");

	strcpy(p_belt2->goods_code_buff[0], "");
	strcpy(p_belt2->goods_code_buff[1], "");
	strcpy(p_belt2->goods_code_buff[2], "");
	strcpy(p_belt2->goods_code_buff[3], "");
	strcpy(p_belt2->goods_code_buff[4], "");

	p_belt4->goods_over = 0;
	p_belt4->sensor_cnt = 0;

	p_belt2->sensor_cnt = 0;
	p_belt3->sensor_cnt = 0;

	p_belt4->belt_start = 0;
	p_belt4->belt_start2 = 0;
	p_belt4->belt_dis = 0;
	p_belt4->goods_len = 0;

	p_belt3->belt_start = 0;
	p_belt3->belt_start2 = 0;
	p_belt3->belt_dis = 0;
	p_belt3->goods_len = 0;

	p_supply->sort_ready = 0;
	p_supply->warning = 0;

	return 0;
}
// 第1段供包机控制
int16_t plc_agent::supply_belt1_ctrl(supply_package *p_supply)
{
	belt *p_belt4 = &p_supply->st_belt[3];
	belt *p_belt3 = &p_supply->st_belt[2];
	belt *p_belt2 = &p_supply->st_belt[1];
	belt *p_belt1 = &p_supply->st_belt[0];

	if (p_belt2->goods == true)
	{
		p_belt1->belt_spd = 0;
		// io 控制虚拟第一段皮带 canio 15控制 
		supply_canio_set(5 + p_supply->id * 5, 0, 15);
	}
	else
	{
		// todo
		p_belt1->belt_spd = BELT1_SPEED;
		supply_canio_set(5 + p_supply->id * 5, 1, 15);
	}
	// SPDLOG_INFO(" supply_belt1_ctrl speed :{} ", p_belt1->belt_spd );
}
// 第2段供包机控制
int16_t plc_agent::supply_belt2_ctrl(supply_package *p_supply)
{
	belt *p_belt4 = &p_supply->st_belt[3];
	belt *p_belt3 = &p_supply->st_belt[2];
	belt *p_belt2 = &p_supply->st_belt[1];
	belt *p_belt1 = &p_supply->st_belt[0];

	static uint16_t sensor_count[2] ={0}; 

	// 判断有无货物到来
	if (p_belt2->sensor_up == true && p_belt2->goods_over == false)
	{
		// 获取伺服编码器位置 todo
		p_belt2->goods_over = true;
		p_belt2->belt_start = p_belt2->belt_pos;

		if ((strlen(p_belt1->goods_code) > 0))
		{
			#if 1
			//
			p_belt2->scan_id = p_belt1->scan_id;
			strcpy(p_belt2->goods_code, p_belt1->goods_code);
            strcpy(p_belt1->goods_code, "");
			#else
			strcpy(p_belt2->goods_code, p_belt1->goods_code_buff[0]);
			strcpy(p_belt1->goods_code_buff[0], "");
			strcpy(p_belt1->goods_code, "");
            #endif 
			SPDLOG_INFO("belt2: goods code \n1:{}\n2:{}\n3:{}\n4:{}", p_belt1->goods_code, p_belt2->goods_code, p_belt3->goods_code, p_belt4->goods_code);
			// 在第二段 上报 商品码
			p_belt2->scan_id = 2;
			
		}
	}
	else if (p_belt2->sensor_up == false && (p_belt2->goods_over == true) && (p_belt2->goods == false))
	{
		//p_belt2->goods_over = false;
		p_belt1->goods = false;
		p_belt2->goods = true;

		p_belt1->sensor_cnt = 0;
		// todo
		p_belt2->belt_end = p_belt2->belt_pos;

		p_belt2->sensor_cnt = p_belt2->sensor_cnt + 1;

		SPDLOG_INFO(" belt2 goods = {},count = {},len:{},goods:{}", p_belt2->goods, p_belt2->sensor_cnt, strlen(p_belt1->goods_code_buff[0]), p_belt1->goods_code_buff[0]);

	}

	if ((m_feeder_info.belt_count == BELT3 || p_belt2->goods == true) && p_belt3->goods == true)
	{
		p_belt2->belt_spd = 0;
	}
	else
	{
		// todo
		p_belt2->belt_spd = BELT2_SPEED;
	}

    // 按键触发体积测量方式，在2段/首段
	if(m_feeder_info.volume_key == 1)
	{
		if((p_belt3->goods == false) && ((p_belt2->vol.length > 0) || (p_belt2->goods == true)))
		{
			p_belt2->belt_spd = BELT2_SPEED;
		}
		else 
		{
			p_belt2->belt_spd = 0;
		}
	}
	// SPDLOG_INFO(" supply_belt2_ctrl speed :{} ", p_belt2->belt_spd );
	return 0;
}
int16_t plc_agent::supply_belt21_ctrl(supply_package *p_supply)
{
	belt *p_belt4 = &p_supply->st_belt[3];
	belt *p_belt3 = &p_supply->st_belt[2];
	belt *p_belt2 = &p_supply->st_belt[1];
	belt *p_belt1 = &p_supply->st_belt[0];

	if (p_belt3->goods == true)
	{
		p_belt2->belt_spd = 0;
	}
	else
	{
		// todo
		p_belt2->belt_spd = BELT2_SPEED;
	}
	// SPDLOG_INFO(" supply_belt2_ctrl speed :{} ", p_belt2->belt_spd );
	return 0;
}
// 第3段供包机控制
int16_t plc_agent::supply_belt3_ctrl(supply_package *p_supply)
{
	if (m_feeder_info.belt_count == BELT2)
	{
		supply_belt3_unormal_ctrl(p_supply);
	}
	else if (m_feeder_info.belt_count > BELT2)
	{
		supply_belt3_normal_ctrl(p_supply);
	}

	return 0;
}
// 第3段供包机控制：标准控制
int16_t plc_agent::supply_belt3_normal_ctrl(supply_package *p_supply)
{
	uint8_t id = p_supply->id;
	// SPDLOG_INFO(" supply_belt3_ctrl" );
	belt *p_belt4 = &p_supply->st_belt[3];
	belt *p_belt3 = &p_supply->st_belt[2];
	belt *p_belt2 = &p_supply->st_belt[1];
	belt *p_belt1 = &p_supply->st_belt[0];

	// test
	uint16_t &uCount = p_belt3->ucount;

	if (p_belt3->belt_spd > 0)
	{
		uCount++;
	}
	// 1.接收到分播任务
	if (true == m_scheduler_msg.scheduler_manager_get_sort_task_flag(id))
	{
		m_scheduler_msg.scheduler_manager_set_sort_task_flag(id);
		// 判断分播任务的有效性
		if ((strlen((char *)m_scheduler_msg.m_sort_task_recv[id].task_id) != 0) && (strcmp((const char *)m_scheduler_msg.m_sort_task_recv[id].task_id, (const char *)p_belt3->task_msg.task_id) == 0))
		{
			// 获取有效的 分播地址
			p_belt3->task_msg.container = m_scheduler_msg.m_sort_task_recv[id].container;
			p_belt3->task_msg.vehicle_belt_speed = m_scheduler_msg.m_sort_task_recv[id].vehicle_belt_speed;
			memset(&m_scheduler_msg.m_sort_task_recv[id], 0, sizeof(m_scheduler_msg.m_sort_task_recv[id]));
			SPDLOG_INFO("###### id = {},belt3 recv task info:{},{},{},{}",p_supply->id, p_belt3->task_msg.gd_codes, p_belt3->task_msg.task_id, p_belt3->task_msg.container,p_belt3->task_msg.vehicle_belt_speed);
		}
		else
		{
			// 错误
			p_supply->warning = TASK_ID_NOT_SAME;
			SPDLOG_INFO("warning : id ={} ERR TASK id  ",p_supply->id);
			SPDLOG_INFO("###### id ={}, ERR TASK: belt3  task id {}",p_supply->id, p_belt3->task_msg.task_id);
			SPDLOG_INFO("###### id ={},ERR TASK: thing task id {}", p_supply->id,m_scheduler_msg.m_sort_task_recv[id].task_id);
		}
	}

	// 2.启动皮带传动条件
	if (((p_belt4->goods == false) && (p_belt3->task_msg.container != 0)) || (p_belt3->goods == false))
	{
		if(fabs(p_belt3->belt_spd) == 0)
		{
			SPDLOG_INFO(" start the belt3 ......");
		}
		p_belt3->belt_spd = BELT3_SPEED;
		// SPDLOG_INFO(" supply_belt3_ctrl speed :{},betl3_goods:{},betl4_goods:{} ", p_belt3->belt_spd ,p_belt3->goods,p_belt4->goods);
	}

	#if 0
	else if ((p_belt4->goods == true) && (p_belt3->task_msg.container != 0))
	{
		// 有任务格口时，前方皮带有货必须停止；无任务格口时，按位置控制停
		p_belt3->belt_spd = 0;
	}
    #endif
	// 3.皮带接货控制
	if ((p_belt3->sensor_up == true && p_belt3->goods_over == false) && (p_belt2->belt_spd > 0) && ((p_belt2->goods == true) || m_feeder_info.belt_count == BELT3))
	{
		SPDLOG_INFO("###### id = {}, the belt3 up up up",p_supply->id);
		// 获取伺服编码器位置 todo
		// test 上升沿，默认 container = 0
		p_supply->sound_ctrl = 0;
		if(m_feeder_info.weight_enable == 1)
		{
           p_supply->weight_meter = true;
		}
		
		p_belt3->task_msg.container = 0;
		p_belt3->goods_over = true;
		p_belt3->belt_start = p_belt3->belt_pos_safe;

		// 触发多面扫
		if (strlen(p_belt2->goods_code) == 0)
		{
#if 1
			// if(p_supply->vsion_scan == true)

			// 第3段皮带触发逻辑改为：1）如果前次触发未结束;2)第4段有货无码，上述两条均不触发，后面延时触发
			if ((p_supply->vsion_scan == true) || (((p_belt4->goods_over) == true) && (strlen(p_belt4->goods_code) == 0)) || (p_supply->warning == true))
			{
				if ((p_belt4->goods == 1) && (p_belt3->goods == 1))
				{
					// p_supply->vsion_scan_failure2 = true;
					SPDLOG_INFO("warning :id = {},the 2th scanning goods, try to scan [no]... ",p_supply->id);
				}
				else
				{
					p_supply->vsion_scan_failure = true;
					SPDLOG_INFO("id = {},warning :the 1th scanning goods, try to scan ... ",p_supply->id);
				}
			}
			// 1）前次扫码结束 且 4段有货且有码，2）无货
			else
			{
				// 触发多面扫 todo
				p_supply->vsion_scan = true;
				p_supply->vsion_scan_failure = false;
				SPDLOG_INFO("###### id = {},the belt3 up ,start the scan ",p_supply->id);
			}
			// 若静态扫码,此处关闭触发
			if(m_feeder_info.auto_scan_static == 1)
			{
				p_supply->vsion_scan = false;
			}
#else
			if (p_supply->warning == false)
			{
				p_supply->vsion_scan = true;
				p_supply->vsion_scan_failure = false;
				SPDLOG_INFO("###### the belt3 up ，start the scan ");
			}
			else
			{
				// p_supply->vsion_scan_failure = false;
			}
#endif
		}
		// 码值跟踪 20221211  增加自动扫是否启动的判断
		else if ((strlen(p_belt2->goods_code) > 0) )
		{
			// strcpy(p_supply->goods_code, p_belt2->goods_code);
			SPDLOG_INFO("id = {},the belt 2 code to supply code {}", p_supply->id, p_belt2->goods_code);

			SPDLOG_INFO("id = {},belt3: goods code \n1:{}\n2:{}\n3:{}\n4:{}", p_supply->id, p_belt1->goods_code, p_belt2->goods_code, p_belt3->goods_code, p_belt4->goods_code);
			// scanid
			p_supply->vsion_scan  = false;
			{
				std::lock_guard<std::mutex> lck(scan_lock);
				//strcpy(p_belt3->goods_code, p_belt2->goods_code);
				strcpy(p_supply->goods_code, p_belt2->goods_code);
			    strcpy(p_belt2->goods_code, "");

				p_belt3->scan_id  = p_belt2->scan_id;
			    p_supply->scan_id = p_belt2->scan_id;
			    p_belt2->scan_id  = 0;
			}
			// 在第二段 上报 商品码
			p_belt2->scan_id = 0;
            // 统一报码
			//goods_info_push(p_supply, p_belt3->goods_code, p_belt3->scan_id, 3);
		}
        // 上升沿获取体积数据 (改到下降沿获取，原因：适应体积相机多次报码)
		#if 0
		if((m_feeder_info.volume_key == 1) && (p_belt2->vol.length > 0))
		{
			p_supply->vol = p_belt2->vol;
			SPDLOG_INFO("id = {},the belt 2 volume copy to supply volume length:{},width:{},height:{}",p_supply->id,p_belt2->vol.length,p_belt2->vol.width,p_belt2->vol.height);
			memset(&p_belt2->vol,0,sizeof(p_belt2->vol));
		}
		#endif 
		// teset
		uCount = 0;
		// p_belt3->belt_start  =0 ;
	}
	else if ((p_belt3->sensor_up == false) && (p_belt3->goods_over == true) && (p_belt3->sensor_cnt == 0))
	{
        p_supply->weight_meter = false;
		p_belt3->uCountEnd = uCount;
		// p_belt3->goods_over = false; 在第4段下降沿 清除
		p_belt3->goods = true;
		// 位置获取
		p_belt3->belt_end = p_belt3->belt_pos_safe;
		// 传感器计数 货物计数 可以和 goods优化
		p_belt3->sensor_cnt = p_belt3->sensor_cnt + 1;

		p_belt2->goods = false;
		p_belt2->goods_over = false;
        // key 触发体积测量，在下降沿获取（和获取体积数据逻辑 有关）
		if((m_feeder_info.volume_key == 1) && (p_belt2->vol.length > 0))
		{
			p_supply->vol = p_belt2->vol;
			SPDLOG_INFO("id = {},the belt 2 volume copy to supply volume length:{},width:{},height:{}",p_supply->id,p_belt2->vol.length,p_belt2->vol.width,p_belt2->vol.height);
			memset(&p_belt2->vol,0,sizeof(p_belt2->vol));
		}

		SPDLOG_INFO("WARNING :id = {},betl 3 the goods Pass time {}", p_supply->id,uCount);

#if 0
		if(p_belt2->sensor_cnt > 0)
		{
			p_belt2->sensor_cnt = p_belt2->sensor_cnt -1;
		}
		else
		p_belt2->sensor_cnt = 0;
#endif
        p_belt2->sensor_cnt = 0;
		// 下降沿停止触发
		// p_supply->vsion_scan = false;
		// SPDLOG_INFO("====== the belt 3 dn, stop the scan ");
		SPDLOG_INFO("###### id = {},the belt 3 dn dn dn",p_supply->id);
		SPDLOG_INFO("id ={}, belt3 goods = {},count = {}",p_supply->id, p_belt3->goods, p_belt3->sensor_cnt);
	}

	// 4.扫码控制 如果条件触发scan标志失败，需要节拍性检查，直到再次触发成功
	if (p_supply->vsion_scan_failure == true)
	{
		if ((p_supply->vsion_scan == false) && ((p_belt4->goods == false) || (strlen(p_belt4->goods_code) > 0))) //(((p_belt4->goods_over) == true) && (strlen(p_belt4->goods_code) == 0))
		{
			p_supply->vsion_scan = true;
			p_supply->vsion_scan_failure = false;

			SPDLOG_INFO("warning :id = {},the 1th scanning goods, start the scan ",p_supply->id);
		}
	}


	// SPDLOG_INFO("p_belt3->goods_over={},p_belt3->task_msg.container,{}",p_belt3->goods_over,p_belt3->task_msg.container);
	//  5.计算位置和长度
	// if((p_belt3->belt_dis >= (p_belt3->belt_len/2.0 + p_belt3->goods_len/2.0 - 100.0)) && (p_belt3->goods == true) && (p_belt4->goods == true))
	//if ((p_belt3->goods_over == true) && p_belt3->task_msg.container == 0)
	if ((p_belt3->goods_over == true) )
	{
		// 如果传感器触发，且无货，更新计算货物长度值
		if ((p_belt3->sensor_up) == true && (p_belt3->goods == false))
		{
			p_belt3->uCountEnd = uCount;
			p_belt3->belt_end = p_belt3->belt_pos_safe;
		}

		// #### 1. 计算位置和商品长度
		p_belt3->goods_len = (1.0 * (p_belt3->belt_end - p_belt3->belt_start)) / (p_belt3->code_count * p_belt3->gear_ratio) * (PI * p_belt3->diameter);

		p_belt3->goods_len = fabs(p_belt3->goods_len);
		// 实时皮带在收到货物起的运行距离
		p_belt3->belt_dis = (1.0 * ((int32_t)p_belt3->belt_pos_safe - p_belt3->belt_start)) / (p_belt3->code_count * p_belt3->gear_ratio) * (PI * p_belt3->diameter);
		p_belt3->belt_dis = fabs(p_belt3->belt_dis);

		//SPDLOG_INFO(" belt3 goods,diff:{}, len= {},dis = {},belt_end :{},belt_start:{},belt_pos:{}",\
		p_belt3->belt_end - p_belt3->belt_start,p_belt3->goods_len,p_belt3->belt_dis,p_belt3->belt_end,\
		p_belt3->belt_start,(int32_t)p_belt3->belt_pos_safe);

		// 按照时间 速度计算长度
		p_belt3->goods_len_count = (0.01 * p_belt3->uCountEnd) * (BELT3_SPEED / 60.0 * 0.1 * 1.0 / p_belt3->gear_ratio * (PI * p_belt3->diameter));
		p_belt3->belt_dis_count = (0.01 * uCount) * (BELT3_SPEED / 60.0 * 0.1 * 1.0 / p_belt3->gear_ratio * (PI * p_belt3->diameter));
		// SPDLOG_INFO("test : belt 3 goods goods_len_count {},belt_dis_count {}",p_belt3->goods_len_count,p_belt3->belt_dis_count);
        
		bool belt_stop = (p_belt3->belt_dis_count >= (p_belt3->belt_len / 2.0 + p_belt3->goods_len_count / 2.0 + m_feeder_info.belt3_dis));

		// 伺服位置不准，增加数据是否可用判断
		bool code_isused = true;

		if (p_belt3->belt_spd > 0)
		{
			//SPDLOG_INFO("id = {},the belt3 dis_count:{},len_count:{}", p_supply->id,p_belt3->belt_dis_count, p_belt3->goods_len_count);
			SPDLOG_INFO("id = {},the betl3 goods,diff:{}, len= {},dis = {},belt_end :{},belt_start:{},belt_pos:{}",
						p_supply->id,p_belt3->belt_end - p_belt3->belt_start, p_belt3->goods_len, p_belt3->belt_dis,
						p_belt3->belt_end, p_belt3->belt_start, (int32_t)p_belt3->belt_pos_safe);
		}

		if (p_belt3->belt_dis > 3000 || p_belt3->belt_len > 3000)
		{
			code_isused = false;

			SPDLOG_INFO("###### id = {},the servo 3 code is not is used ",p_supply->id);
		}
		else
		{
			code_isused = true;
		}

		// 检测异常
		if ((p_belt3->belt_len > 2000) && (p_belt3->goods_len_count > 2000))
		{
			// 编码器异常
			p_supply->warning = SERVER_CODE_FAULT;
			SPDLOG_INFO("###### id = {},the code and count are error ",p_supply->id);
		}
		// 超长包检测
		// test 用
		m_package_over_length = true;
		 m_package_length_max = m_feeder_info.goods_len_max;

		if (m_package_over_length == true)
		{
			if ((p_belt3->goods_len > m_package_length_max) || (p_belt3->goods_len_count > m_package_length_max))
			{
				// 超长包
				p_supply->warning = LENGTH_OVER_LIMIT;
				SPDLOG_INFO("###### id = {},the goods length over the max {},{},{}",p_supply->id, p_belt3->belt_len, p_belt3->goods_len_count, m_package_length_max);
			}
		}

		// #### 2.皮带停止
		if (((code_isused == true) && (p_belt3->belt_dis >= (p_belt3->belt_len / 2.0 + p_belt3->goods_len / 2.0 + m_feeder_info.belt3_dis))) || (belt_stop))
		//if (code_isused && (p_belt4->belt_dis > (p_belt4->belt_len + m_feeder_info.belt4_dis_stop)))
		{
			// uCount = 1000;
			if((p_belt4->goods == true) || (p_belt3->task_msg.container == 0))
			{
				if(p_belt3->belt_spd > 0)
				{
                    SPDLOG_INFO("====== stop for belt4 has goods or container 0 ");
				}
				p_belt3->belt_spd = 0;

			}
			
		}

		// if((p_belt4->sensor_up == true))
	}
	else
	{
		// timeout = 0;
	}

	// 货物在皮带上的滞留时间
	if (p_belt3->goods_over == true && p_belt3->belt_spd == 0)
	{
		p_belt3->goods_time++;

        // 停止时间监控
		if(p_belt3->goods_time == 500)
		{
			if((p_belt3->weight == 0) && (m_feeder_info.weight_enable == 1) )
			{
				//p_supply->sound_ctrl = 2;
				p_supply->warning = 13;
				
				SPDLOG_INFO("warning:id = {},the time over 5 s,and not recv weight ",p_supply->id);
			}
			if(strlen(p_belt3->goods_code) == 0)
			{
				//有货无码 
				p_supply->warning = 13;
				SPDLOG_INFO("warning:id = {},the time over 5s,and not recv goodecode ",p_supply->id);
			}
		}

		if((m_feeder_info.auto_scan_static == 1) && (strlen(p_belt3->goods_code) == 0) )
		{
			if(p_belt3->goods_time == 10)
			{
				p_supply->vsion_scan = true;
				SPDLOG_INFO("id = {},static scan ,the belt speed 0,start the scan ",p_supply->id);
			}
			else if(p_belt3->goods_time == 30)
			{
				p_supply->vsion_scan = false;
				SPDLOG_INFO("id = {},static scan ,the belt speed 0,stop the scan ",p_supply->id);
			}
		}

		// 皮带停止后获取扫码枪的码值
		if (p_belt3->goods_time > 10000)
		{
			p_belt3->goods_time = 10000;
		}
	}
	else
	{
		p_belt3->goods_time = 0;
	}

	// 6.面扫停止控制 （在这里修改停止触发条件，可以压缩报码时间）,add，非静态条件
	if ((m_feeder_info.auto_scan_static == 0) && (p_belt3->goods_time > 2 || (strlen(p_belt3->goods_code) > 0)))// || (p_belt4->belt_dis > (p_belt4->belt_len + 60) ) 
	{
		// 五面扫停止scan
		if (1) //(p_supply->vsion_scan == true)
		{

			if (p_supply->vsion_scan == true)
				SPDLOG_INFO("id = {},the belt speed 0,the scan true, stop the scan ",p_supply->id);

			p_supply->vsion_scan = false;
		}
		else
		{
			p_belt3->goods_time = 0;
		}
	}
	return 0;
}
int16_t plc_agent::supply_belt3_unormal2_ctrl(supply_package *p_supply)
{
	belt *p_belt4 = &p_supply->st_belt[3];
	belt *p_belt3 = &p_supply->st_belt[2];
	belt *p_belt2 = &p_supply->st_belt[1];
	belt *p_belt1 = &p_supply->st_belt[0];

	if (p_belt4->goods == true)
	{
		p_belt3->belt_spd = 0;
	}
	else
	{
		// todo
		p_belt3->belt_spd = BELT3_SPEED;
	}
	// SPDLOG_INFO(" supply_belt2_ctrl speed :{} ", p_belt2->belt_spd );
	return 0;

}

// 2段-第1段 扫码段 ：请求到格口信息且供包段无货，启动传输；无格口信息无速度间隔300ms扫码；
int16_t plc_agent::supply_belt3_unormal_ctrl(supply_package *p_supply)
{
	uint8_t id = p_supply->id;
	// SPDLOG_INFO(" supply_belt3_ctrl" );
	belt *p_belt4 = &p_supply->st_belt[3];
	belt *p_belt3 = &p_supply->st_belt[2];
	belt *p_belt2 = &p_supply->st_belt[1];
	belt *p_belt1 = &p_supply->st_belt[0];

	if (m_feeder_info.double_feeder_scan == 2)
	{
		if (p_belt4->goods == true)
		{
			p_belt3->belt_spd = 0;
		}
		else
		{
			// todo
			p_belt3->belt_spd = BELT3_SPEED;
		}
		// SPDLOG_INFO(" supply_belt2_ctrl speed :{} ", p_belt2->belt_spd );
		return 0;
	}

	// test
	uint16_t &uCount = p_belt3->ucount;

	if (p_belt3->belt_spd > 0)
	{
		uCount++;
	}
	// 1.接收到分播任务
	if (true == m_scheduler_msg.scheduler_manager_get_sort_task_flag(id))
	{
		m_scheduler_msg.scheduler_manager_set_sort_task_flag(id);
		// 判断分播任务的有效性
		if ((strlen((char *)m_scheduler_msg.m_sort_task_recv[id].task_id) != 0) && (strcmp((const char *)m_scheduler_msg.m_sort_task_recv[id].task_id, (const char *)p_belt3->task_msg.task_id) == 0))
		{
			// 获取有效的 分播地址
			p_belt3->task_msg.container = m_scheduler_msg.m_sort_task_recv[id].container;
			memset(&m_scheduler_msg.m_sort_task_recv[id], 0, sizeof(m_scheduler_msg.m_sort_task_recv[id]));
			SPDLOG_INFO("###### belt3 recv task info: id={},{},{},{}", id, p_belt3->task_msg.task_id, p_belt3->task_msg.container,p_belt3->task_msg.gd_codes);
		}
		else
		{
			// 错误
			SPDLOG_INFO("###### ERR TASK: belt3 feeder id {}, task id {}", id, p_belt3->task_msg.task_id);
			SPDLOG_INFO("###### ERR TASK: thing feeder id {},task id {}", id, m_scheduler_msg.m_sort_task_recv[id].task_id);
		}
	}

	// 2.启动皮带传动条件
	if (((p_belt4->goods == false) && (p_belt3->task_msg.container != 0)) && (p_belt3->goods == 0))
	{
		p_belt3->goods = true;
		p_belt3->sensor_cnt = 1;
		p_belt3->goods_over = true;
		p_belt3->belt_spd = BELT3_SPEED;

		// SPDLOG_INFO(" supply_belt3_ctrl speed :{},betl3_goods:{},betl4_goods:{} ", p_belt3->belt_spd ,p_belt3->goods,p_belt4->goods);
	}
	else if ((p_belt3->task_msg.container == 0) || ((p_belt4->goods == true) && (p_belt3->task_msg.container != 0)))
	{
		p_belt3->belt_spd = 0;
	}

	// 3.1 启动扫描 (0 == strcmp(p_belt3->goods_code,"noread"))
	if ((strlen(p_belt3->goods_code) == 0) || p_supply->st_key.recover == true)
	{
		// SPDLOG_INFO("###### start the scan {},{}",p_belt3->goods,p_supply->vsion_scan);
		if ((p_belt3->goods == 0) && (p_supply->vsion_scan == 0))
		{
			p_supply->vsion_scan = true;

			SPDLOG_INFO("###### start the scan ,feeder id {}", id);
		}
	}


	    // 来自手持扫码设备 todo
		if ((strlen(p_belt3->goods_code_buff[0]) > 0) && ( p_belt3->sensor_cnt == 0))
		{
#if 1
            p_belt3->sensor_cnt = 1;
			//  注意，与3段手持扫码有区别
			p_belt3->scan_id = p_belt3->scan_id;
			strcpy(p_belt3->goods_code, p_belt3->goods_code_buff[0]);
			strcpy(p_belt3->goods_code_buff[0], p_belt3->goods_code_buff[1]);
			strcpy(p_belt3->goods_code_buff[1], p_belt3->goods_code_buff[2]);
			strcpy(p_belt3->goods_code_buff[2], p_belt3->goods_code_buff[3]);
			strcpy(p_belt3->goods_code_buff[3], p_belt3->goods_code_buff[4]);
			strcpy(p_belt3->goods_code_buff[4], "");
#else
			strcpy(p_belt2->goods_code, p_belt1->goods_code_buff[0]);
			strcpy(p_belt1->goods_code_buff[0], "");
			strcpy(p_belt1->goods_code, "");
#endif

			SPDLOG_INFO("id:{} belt3: goods code \n1:{}\n2:{}\n3:{}\n4:{}",id, p_belt1->goods_code, p_belt2->goods_code, p_belt3->goods_code, p_belt4->goods_code);
			// 在第二段 上报 商品码
			p_belt2->scan_id = 0;
			goods_info_push(p_supply, p_belt3->goods_code, p_belt3->scan_id, 3,0);

		}

	// 3.2 结束扫描
	if (strlen(p_belt3->goods_code) > 0)
	{
		if (p_supply->vsion_scan == true)
			SPDLOG_INFO("###### stop the scan ,feeder id {}", id);
		p_supply->vsion_scan = false;

		if (0 == strcmp(p_belt3->goods_code, "noread"))
		{
			p_belt3->goods_time++;

			if (p_belt3->goods_time > 100)
			{
				p_belt3->goods_time = 0;
				strcpy(p_belt3->goods_code, "");
			}
		}
	}

// 4.扫码控制 如果条件触发scan标志失败，需要节拍性检查，直到再次触发成功

// SPDLOG_INFO("p_belt3->goods_over={},p_belt3->task_msg.container,{}",p_belt3->goods_over,p_belt3->task_msg.container);
//  5.计算位置和长度

// 6.面扫停止控制
#if 0
	if(p_belt3->goods_time > 20)
	{
		// 五面扫停止scan
		if(1) //(p_supply->vsion_scan == true)
		{
			
			if(p_supply->vsion_scan == true)
			SPDLOG_INFO("the belt speed 0,the scan true, stop the scan ");

			//p_supply->vsion_scan = false;

			
		}
		else
		{
			p_belt3->goods_time = 0;
		}
	}
#endif
	return 0;
}

// 第4段供包机：1段供包机+粗分逻辑（san = 1 belt = 1）
int16_t plc_agent::supply_belt_ctrl(supply_package *p_supply)
{
	uint8_t id = p_supply->id;
	// SPDLOG_INFO(" supply_belt3_ctrl" );
	belt *p_belt4 = &p_supply->st_belt[3];

	// 1. 格口等待队列为空，则从条码队列获取条码，若有条码则pub请求格口任务，等待任务队里加+1
	// 2. 获取得到的格口任务，存入任务队列，任务队列+1，等待任务度列-1
	// 3. work空闲，则从任务队里get任务，get任务后，任务队列-1，work执行，get不到任务，皮带静止
	// 4. work 根据任务启动皮带传输及方向，并监控传感器，上升沿push 任务到下一级皮带，下降沿work执行结束
	std::string goods_code;
	uint32_t task_id;
    goods_taskid_msg goods_msg;
	auto start = std::chrono::high_resolution_clock::now();  
	auto end = std::chrono::high_resolution_clock::now();  

	// 1. 任务请求,请求中的任务最多一个 
	if(p_supply->m_req_task_num == 0)
	{
		if (!p_supply->m_goods_msg_queue.empty())
		{
			p_supply->m_goods_msg_queue.pop(goods_msg);
			goods_code = goods_msg.str;
			task_id    = goods_msg.task_id;
			strcpy(p_belt4->goods_code, goods_code.c_str());
            goods_info_push(p_supply, p_belt4->goods_code, p_belt4->scan_id, 3,task_id);
			p_supply->m_req_task_num = 1;

		}
		p_supply->start[3] = 0;
	}
	else
	{
			if(p_supply->start[3] == 0)
			{
				p_supply->start[3] = 1;
				p_supply->start_time[3]= start;
			}

			auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - p_supply->start_time[3]);

			if(std::chrono::duration_cast<std::chrono::milliseconds>(duration).count() > 3000)
			{
				if(m_feeder_info.auto_feeder == 1)
				{
					//p_supply->action_msg.run_dir = 2;
					SPDLOG_INFO("id = {}, req task but not recv task, out time, task id:{}", id, p_supply->m_sort_req_task.task_id);
				}
				else
				{
					// 信号一直触发状态，传输超时
					p_supply->warning = BASIC_NOT_RECV_TASK_TIM_OUT;
					SPDLOG_INFO("id = {}, req task but not recv task, out time, task id:{}", id, p_supply->m_sort_req_task.task_id);
				}
			}

	}
    // 2.接收到分播任务,存入任务队列
	if (true == m_scheduler_msg.scheduler_manager_get_sort_task_flag(id))
	{
		m_scheduler_msg.scheduler_manager_set_sort_task_flag(id);
		// 判断分播任务的有效性
		if ((strlen((char *)m_scheduler_msg.m_sort_task_recv[id].task_id) != 0) && (strcmp((const char *)m_scheduler_msg.m_sort_task_recv[id].task_id, (const char *)p_supply->m_sort_req_task.task_id) == 0))
		{
			// 获取有效的 分播地址
			//p_belt4->task_msg.container = m_scheduler_msg.m_sort_task_recv[id].container;
			//p_belt4->task_msg.vehicle_belt_speed = m_scheduler_msg.m_sort_task_recv[id].vehicle_belt_speed;
			SPDLOG_INFO("###### id = {}, recv task info:{},{},cnt_num={},{},{},{},{},{}",
			p_supply->id, 
			m_scheduler_msg.m_sort_task_recv[id].gd_codes,
			m_scheduler_msg.m_sort_task_recv[id].task_id,
			m_scheduler_msg.m_sort_task_recv[id].containers_count,
			m_scheduler_msg.m_sort_task_recv[id].containers[0],
			m_scheduler_msg.m_sort_task_recv[id].containers[1],
			m_scheduler_msg.m_sort_task_recv[id].containers[2],
			m_scheduler_msg.m_sort_task_recv[id].containers[3],
			m_scheduler_msg.m_sort_task_recv[id].vehicle_belt_speed);

			if( m_scheduler_msg.m_sort_task_recv[id].containers_count == 1)
			{
				m_scheduler_msg.m_sort_task_recv[id].containers[1] = 0;
			}
			p_supply->m_sort_task_queue.push(m_scheduler_msg.m_sort_task_recv[id]);
			memset(&m_scheduler_msg.m_sort_task_recv[id], 0, sizeof(m_scheduler_msg.m_sort_task_recv[id]));
			memset(&p_supply->m_sort_req_task, 0, sizeof(m_scheduler_msg.m_sort_task_recv[id]));

			p_supply->m_req_task_num = 0;

		}
		else
		{
			p_supply->warning = TASK_ID_NOT_SAME;
			SPDLOG_INFO("warning : id ={} ERR TASK id  ",p_supply->id);
			// 错误
			SPDLOG_INFO("###### id ={}, ERR TASK:  req task id {}",p_supply->id, p_supply->m_sort_req_task.task_id);
			SPDLOG_INFO("###### id ={},ERR TASK:   rec task id {}", p_supply->id,m_scheduler_msg.m_sort_task_recv[id].task_id);
		}
	}

	// 3. work 任务为空时，从任务队列get任务，===从任务队列get 任务时，必须是空闲即皮带停止态。
	if((p_belt4->task_msg.containers[0] == 0) && (p_supply->action_msg.run_dir == 0x03))
	{
		// get 任务
		if (!p_supply->m_sort_task_queue.empty())
		{
			// get task info 
			memset(&p_belt4->task_msg.containers[0],0,sizeof(p_belt4->task_msg.containers));
			p_supply->m_sort_task_queue.pop(p_belt4->task_msg);

			SPDLOG_INFO("###### id = {},get task form queue, info:{},{},{},{},{},{},{},{}",
			p_supply->id, 
			p_belt4->task_msg.gd_codes,
			p_belt4->task_msg.task_id,
			p_belt4->task_msg.containers_count,
			p_belt4->task_msg.containers[0],
			p_belt4->task_msg.containers[1],
			p_belt4->task_msg.containers[2],
			p_belt4->task_msg.containers[3],
			p_belt4->task_msg.vehicle_belt_speed);
		}
		else
		{
			// 任务队列空，且work空闲时
			// 下发停止指令
		}

	}
	// 结合get的任务+当前设备状态，进行快照生成,根据快照进行action生成
	else
	{
		// 快照没有生成，进行快照
		if (p_supply->action_msg.run_dir == 0x03)
		{
			// 1.判断格口是否存在不可用状态/满箱或封箱状态，如有，格口剔除，
			// 2.筛选后的格口数大于2，则进一步筛选，优先筛选两个墙且不属于兜底的格口，4个格口属于同一墙，默认选前两个格口下发;
			// 3.最终筛选结果不超过2 
			// 4.筛选结果中，判断是镜像任务还是非镜像任务，如果是进入镜像分播逻辑，如果是非镜像任务。。。
			// 5.根据可选的左右供包机状态，选取一个格口任务进行下发，并锁定粗分方向
			
		    // 1.格口筛选，筛选1次
			if (p_belt4->task_msg.containers_count > 0)
			{
				int containers[16] = {0};
				int containers1[16] = {0};

				int n_containers[16] = {0};
				int n_containers1[16] = {0};

				int j = 0;
				int m = 0;
				int k = 0;
				int n = 0;
				// 封箱后满箱剔除
				for (auto i = 0; i < p_belt4->task_msg.containers_count; i++)
				{
					int cnt_id = p_belt4->task_msg.containers[i];

					if ((cnt_id >= 1000) && (cnt_id < 3000))
					{
						if (state_NORMAL == m_dev_slot.slot_manager_get_slot_state(cnt_id))
						{
							if (container_seal_state_IDLE == m_dev_slot.slot_manager_get_seal_state(cnt_id))
							{
								containers[j++] = cnt_id;
								SPDLOG_INFO("id {}, add 0 feeder containers {},num {}", p_supply->id, cnt_id, j);
								continue;
							}
						}

						n_containers[k++] = cnt_id;
						SPDLOG_INFO("id {}, add 0 feeder not use containers {},num {}", p_supply->id, cnt_id, k);
					}
					else if ((cnt_id >= 3000) && (cnt_id < 5000))
					{
						if (state_NORMAL == m_dev_slot1.slot_manager_get_slot_state(cnt_id))
						{
							if (container_seal_state_IDLE == m_dev_slot1.slot_manager_get_seal_state(cnt_id))
							{
								containers1[m++] = cnt_id;
								SPDLOG_INFO("id {}, add 1 feeder containers {},num {}", p_supply->id, cnt_id, m);
								continue;
							}
						}

						n_containers1[n++] = cnt_id;

						SPDLOG_INFO("id {}, add 1 feeder not use containers {},num {}", p_supply->id, cnt_id, n);
					}
				}
				// 无可用格口，随机下发，调度决策
				int select_containers[16] = {0};
				if ((m + j) == 0)
				{
					if ((k > 0) && (n > 0))
					{
						select_containers[0] = n_containers[0];
						select_containers[1] = n_containers1[0];
					}
					else if ((k > 0) && (n == 0))
					{
						select_containers[0] = n_containers[0];
						select_containers[1] = n_containers1[0];
					}
					else if ((k == 0) && (n > 0))
					{
						select_containers[0] = n_containers1[0];
						select_containers[1] = n_containers[0];
					}

				}
				// 用可用格口,从可用格口中最多选择2个格口
				else if ((m + j) > 0)
				{

					if ((j != 0) && (m != 0))
					{
						select_containers[0] = containers[0];
						select_containers[1] = containers1[0];
					}
					else if ((j == 0) && (m != 0))
					{
						select_containers[0] = containers1[0];
					}
					else if ((j != 0) && (m == 0))
					{
						select_containers[0] = containers[0];
					}

					//SPDLOG_INFO("id {},  the use containers num {} , select cnt0 = {},select cnt1 = {}", p_supply->id, m + j,select_containers[0] ,select_containers[1]);
				}

				SPDLOG_INFO("id {},  the useable containers num {} , select cnt0 = {},select cnt1 = {}", p_supply->id, m + j,select_containers[0] ,select_containers[1]);

				// 下发两个格口
				memset(&p_belt4->task_msg.containers[0], 0, sizeof(p_belt4->task_msg.containers));

				p_belt4->task_msg.containers[0] = select_containers[0];
				p_belt4->task_msg.containers[1] = select_containers[1];

				if(p_belt4->task_msg.containers[0] == 0)
				{
					SPDLOG_INFO("id {}, p_belt4->task_msg.containers[0] == 0  is err", p_supply->id);
				}

			    p_belt4->task_msg.containers_count = 0;
			}
            // 2. 从筛选结果里进行粗分，并锁定格口。
			if (((p_belt4->task_msg.containers[0] > 1000) && (p_belt4->task_msg.containers[0] < 3000)) && ((p_belt4->task_msg.containers[1] > 3000) && (p_belt4->task_msg.containers[1] < 5000)))
			{
				// 都允许供包

				if (p_supply->sort_info[0].ready_state && p_supply->sort_info[1].ready_state)
				{
					// 首先向任务少的一侧供包
					if (p_supply->sort_info[1].task_num >= p_supply->sort_info[0].task_num)
					{
						// 确定分播方向
						p_supply->action_msg.run_dir =  0;
						//p_supply->action_msg.run_dir = p_supply->sort_info[1].task_num > p_supply->sort_info[0].task_num;
						p_belt4->task_msg.containers[0] = p_belt4->task_msg.containers[p_supply->action_msg.run_dir];

						SPDLOG_INFO("mirror task to sort {}: sort 0 task num {},sort 1 task num {}",p_supply->action_msg.run_dir,p_supply->sort_info[0].task_num,p_supply->sort_info[1].task_num);
					}
					else
					{
						p_supply->action_msg.run_dir =  1;
						// 确定分播方向
						//p_supply->action_msg.run_dir = p_supply->sort_info[1].task_num > p_supply->sort_info[0].task_num;
						p_belt4->task_msg.containers[0] = p_belt4->task_msg.containers[p_supply->action_msg.run_dir];

						SPDLOG_INFO("mirror task to sort {}: sort 0 task num {},sort 1 task num {}",p_supply->action_msg.run_dir,p_supply->sort_info[0].task_num,p_supply->sort_info[1].task_num);
					}
				}
				else if(p_supply->sort_info[0].ready_state || p_supply->sort_info[1].ready_state)
				{
					if(p_supply->sort_info[0].ready_state)
					{
						p_supply->action_msg.run_dir =  0;
					}
					else
					{
						p_supply->action_msg.run_dir =  1;
					}
					p_belt4->task_msg.containers[0] = p_belt4->task_msg.containers[p_supply->action_msg.run_dir];

					SPDLOG_INFO("mirror task to sort {}: sort 0 task num {},sort 1 task num {}",p_supply->action_msg.run_dir,p_supply->sort_info[0].task_num,p_supply->sort_info[1].task_num);

					
					// 下发stop指令
				}
				else
				{
					// 等待
					p_supply->action_msg.run_dir =  3;
				}
			}
			// 非镜像任务
			else if(p_belt4->task_msg.containers[0] > 0)
			{
				if(p_belt4->task_msg.containers[0] > 3000)
				{
					if(p_supply->sort_info[1].ready_state)
					{
						p_supply->action_msg.run_dir =  1;
					}
					
				}
				else
				{
					if (p_supply->sort_info[0].ready_state)
					{
						p_supply->action_msg.run_dir = 0;
					}
				}

				if(p_supply->action_msg.run_dir < 2)
				SPDLOG_INFO("not mirror task to sort {}: sort 0 task num {},sort 1 task num {}",p_supply->action_msg.run_dir,p_supply->sort_info[0].task_num,p_supply->sort_info[1].task_num);
			}
		}
	}
 
	// 4. action生成及控制
	if (p_supply->action_msg.run_dir < 2)
	{
        //auto start = std::chrono::high_resolution_clock::now();  
		//auto end = std::chrono::high_resolution_clock::now();  
		
		// 传输状态监测
		p_belt4->sensor_up = p_supply->sort_info[p_supply->action_msg.run_dir].sensor_state;

		//p_belt4->sensor_up = p_belt4->sensor_state[p_supply->action_msg.run_dir];

		//SPDLOG_INFO("sensor up ====id {} sensor {},dir = {},",p_supply->id, p_belt4->sensor_up,p_supply->action_msg.run_dir);

		if ((((p_belt4->sensor_up == true) && (p_belt4->sensor_flag == false) && (p_belt4->task_msg.containers[0] !=0))))
		{
			SPDLOG_INFO("###### id = {}, the belt4 up up up", p_supply->id);
			p_belt4->goods_over = true;
			p_belt4->sensor_flag = true;

			// todo: push 任务信息，p_belt4->task_msg <task:"";goods:"">,采用tcp协议，
		    if(m_supply_fd[p_supply->action_msg.run_dir] > 0)
			{
				uint32_t id;
	
                // 报文格式 字符串<dev_id,code,cnt,task_id>
				std::string send_msg = "<" + 
				#if 1
				to_string(p_supply->id) + "," +
			    std::string((char *)p_belt4->task_msg.gd_codes) + "," + 
				to_string(p_belt4->task_msg.containers[0]) + "," + 
				std::string((char *)p_belt4->task_msg.task_id) + 
				">";
				#endif 

				if(send(m_supply_fd[p_supply->action_msg.run_dir],send_msg.c_str(),send_msg.length(),0) < 0)
				{
					// 发送失败
					SPDLOG_INFO("tcp send to feeder msg err,dir {},id {},",p_supply->id,p_supply->action_msg.run_dir);
				}

				SPDLOG_INFO("###### tcp send msg ok,{} ",send_msg);

				memset(&p_belt4->task_msg, 0, sizeof(p_belt4->task_msg));

			}
			else
			{
				//
				SPDLOG_INFO("wait feeder tcp client connect...id {},fd {} ",p_supply->action_msg.run_dir,m_supply_fd[p_supply->action_msg.run_dir]);


			}
		}
		// 信号
		else if ((p_belt4->sensor_up == true && p_belt4->sensor_flag == true) )
		{
			// 触发计数
			//auto start = std::chrono::high_resolution_clock::now();  
			if(p_supply->start[2] == 0)
			{
				p_supply->start[2] = 1;
				p_supply->start_time[2]= start;
			} 

			auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - p_supply->start_time[2]);

			if(std::chrono::duration_cast<std::chrono::milliseconds>(duration).count() > 1000)
			{
				// 信号一直触发状态，传输超时
				p_supply->warning = BASIC_SENSOER_UP_TIM_OUT;

				SPDLOG_INFO("id = {}, the out time ,the sensor up  out time not change",p_supply->id);
			}
		}
		else if ((p_belt4->sensor_up == false) && (p_belt4->sensor_flag == true))
		{
			// 超时检测 定义一个map，每次启动时插入一个key，stop时删除，

			if(p_supply->start[1] == 0)
			{
				p_supply->start[1] = 1;
				p_supply->start_time[1]= start;
			}

			SPDLOG_INFO("###### id = {},the belt 4 dn dn dn", p_supply->id);
			  
			if (p_supply->sort_info[p_supply->action_msg.run_dir].ready_state == false)
			{
				p_belt4->sensor_flag = false;
				p_supply->sort_info[p_supply->action_msg.run_dir].ready_state = false;
				SPDLOG_INFO("###### id = {},the recv ready state run dir = {},ready = {}", p_supply->id,p_supply->action_msg.run_dir,0);

				memset(&p_belt4->task_msg, 0, sizeof(p_belt4->task_msg));

				p_supply->start[1] = 0;
				// stop 指令下发
				p_supply->action_msg.run_dir = 3;
			}
			else
			{
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - p_supply->start_time[1]);
                // 等待传输禁止信号，200超时，故障
				if (std::chrono::duration_cast<std::chrono::milliseconds>(duration).count() > 300)
				{
					p_belt4->sensor_flag = false;
					p_supply->sort_info[p_supply->action_msg.run_dir].ready_state = false;
					p_supply->start[1] = 0;
                    // 获取供包机状态超时
					p_supply->warning = BASIC_SENSOER_COM_TIM_OUT;

					p_supply->action_msg.run_dir = 3;
					SPDLOG_INFO("id = {}, the out time ,the ready false state not come",p_supply->id);
				}
				
			}
		}
		else if ((p_belt4->sensor_up == false) && (p_belt4->sensor_flag == false))
		{
			// 长时间未检测到商品信号，异常
			if (p_supply->start[0] == 0)
			{
				p_supply->start[0] = 1;
				p_supply->start_time[0]= start;
			}

			auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - p_supply->start_time[0]);

			if (std::chrono::duration_cast<std::chrono::milliseconds>(duration).count() > 5000)
			{
				// 信号一直触发状态，传输超时
				p_supply->warning = BASIC_SENSOER_LOW_TIM_OUT;
				p_supply->action_msg.run_dir = 3;
				SPDLOG_INFO("id = {}, the out time ,the sensor up  not come",p_supply->id);
			}
		}
	}
	else if(p_supply->action_msg.run_dir == 2)
	{
		// 长时间未检测到商品信号，异常
		if (p_supply->start[4] == 0)
		{
			p_supply->start[4] = 1;
			p_supply->start_time[4] = start;
		}
		p_supply->m_req_task_num = 0;

		auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - p_supply->start_time[4]);

		if (std::chrono::duration_cast<std::chrono::milliseconds>(duration).count() > 3000)
		{
			// 信号一直触发状态，传输超时
			p_supply->action_msg.run_dir = 3;
			SPDLOG_INFO("id = {}, it is time for change ,run dir 2 -> 3", p_supply->id);
		}
	}
	else 
	{
		p_supply->action_msg.run_dir = 3;
		p_supply->start[0] = 0;
		p_supply->start[1] = 0;
		p_supply->start[2] = 0;
		p_supply->start[3] = 0;
		p_supply->start[4] = 0;
		// stop

		// 
	}
}

// 第4段供包机控制：1 段供包机
int16_t plc_agent::supply_belt4_unormal2_ctrl(supply_package *p_supply)
{
	uint8_t id = p_supply->id;
	// SPDLOG_INFO(" supply_belt3_ctrl" );
	belt *p_belt4 = &p_supply->st_belt[3];
	belt *p_belt3 = &p_supply->st_belt[2];
	belt *p_belt2 = &p_supply->st_belt[1];
	belt *p_belt1 = &p_supply->st_belt[0];

	// test
	uint16_t &uCount = p_belt4->ucount;

	if (p_belt4->belt_spd > 0)
	{
		uCount++;
	}
	// 皮带传动条件
	// 给皮带速度
	p_belt4->belt_spd = BELT4_SPEED ;

    //p_belt4->belt_spd = BELT4_SPEED ;
	// 1.接收到分播任务
	if (true == m_scheduler_msg.scheduler_manager_get_sort_task_flag(id))
	{
		m_scheduler_msg.scheduler_manager_set_sort_task_flag(id);
		// 判断分播任务的有效性
		if ((strlen((char *)m_scheduler_msg.m_sort_task_recv[id].task_id) != 0) && (strcmp((const char *)m_scheduler_msg.m_sort_task_recv[id].task_id, (const char *)p_belt4->task_msg.task_id) == 0))
		{
			// 获取有效的 分播地址
			p_belt4->task_msg.container = m_scheduler_msg.m_sort_task_recv[id].container;
			p_belt4->task_msg.vehicle_belt_speed = m_scheduler_msg.m_sort_task_recv[id].vehicle_belt_speed;
			memset(&m_scheduler_msg.m_sort_task_recv[id], 0, sizeof(m_scheduler_msg.m_sort_task_recv[id]));
			SPDLOG_INFO("###### id = {},belt4 recv task info:{},{},{},{}",p_supply->id, p_belt4->task_msg.gd_codes, p_belt4->task_msg.task_id, p_belt4->task_msg.container,p_belt4->task_msg.vehicle_belt_speed);
			// 拷贝、发布分播
		    //p_belt4->task_msg = p_belt3->task_msg;
		    p_belt4->pub_enable = 1;
		    p_belt4->task_msg.dev_id = p_supply->id;
			// 格口任务push
			if ((p_supply->sort_ready == 0))
			{
				// 格口任务push
				sort_info_push(p_supply);
			}
			else if(p_supply->sort_ready == 1)
			{
				p_supply->warning = SUPPLY_CMD_FAULT;
				p_supply->sort_ready = 0;
				m_scheduler_msg.supply_cmd_flag[p_supply->id] = 0;
				SPDLOG_INFO("id {} ,warning : recv supply cmd befor push sort info,err ", p_supply->id);
			}
		}
		else
		{
			p_supply->warning = TASK_ID_NOT_SAME;
			SPDLOG_INFO("warning : id ={} ERR TASK id  ",p_supply->id);
			// 错误
			SPDLOG_INFO("###### id ={}, ERR TASK: belt4  task id {}",p_supply->id, p_belt4->task_msg.task_id);
			SPDLOG_INFO("###### id ={},ERR TASK: thing task id {}", p_supply->id,m_scheduler_msg.m_sort_task_recv[id].task_id);
		}
	}

	// 2.皮带接货控制


	//if (((p_belt4->sensor_up == true) && (p_belt4->sensor_flag == false)) && (p_belt4->goods_over == false) && (p_belt3->goods) && (p_belt3->task_msg.container != 0))
	//if ((((p_belt4->sensor_up == true) && (p_belt4->sensor_flag == false)) && p_belt4->goods_over == false) && (p_belt3->belt_spd > 0) )
	
	if(strlen(p_belt3->goods_code) > 0  && (p_belt4->goods_over == false))
	{
		SPDLOG_INFO("====== id = {}, the belt4 up up up",p_supply->id);
		// 获取伺服编码器位置 todo
		// test 上升沿，默认 container = 0
		p_belt4->goods_over  = true;
		p_belt4->goods       = true;
		//p_belt4->sensor_flag = true;
		p_belt4->belt_start = p_belt4->belt_pos_safe;
		// 触发多面扫
		if (strlen(p_belt3->goods_code) == 0)
		{
			if(1)
			{
				// 触发多面扫 todo
				p_supply->vsion_scan = true;
				SPDLOG_INFO("====== id = {},the belt4 up ,start the scan ",p_supply->id);
			}
			//SPDLOG_INFO("====== id = {}, the belt4 up up up",p_supply->id);
		}

		// 码值跟踪 20221211  增加自动扫是否启动的判断
		else if ((strlen(p_belt3->goods_code) > 0))
		{
			// scanid
			p_supply->vsion_scan  = false;
			p_belt4->scan_id = p_belt3->scan_id;
			p_belt3->scan_id = 0;

			{
				std::lock_guard<std::mutex> lck(scan_lock);
				//strcpy(p_belt4->goods_code, p_belt3->goods_code);
				strcpy(p_supply->goods_code, p_belt3->goods_code);
			    strcpy(p_belt3->goods_code, "");
			}

			SPDLOG_INFO("id = {},belt4: goods code \n1:{}\n2:{}\n3:{}\n4:{}", p_supply->id, p_belt1->goods_code, p_belt2->goods_code, p_belt3->goods_code, p_belt4->goods_code);
			
			SPDLOG_INFO("id = {},the belt 3 handle code to supply code {}", p_supply->id, p_supply->goods_code);
			// SPDLOG_INFO("belt3: goods code \n1:{}\n2:{}\n3:{}\n4:{}",p_belt1->goods_code,p_belt2->goods_code,p_belt3->goods_code,p_belt4->goods_code );
            // 与自动扫统一push
            //goods_info_push(p_supply, p_belt4->goods_code, p_belt4->scan_id, 3);
		}

		// teset
		uCount = 0;
		// p_belt3->belt_start  =0 ;
	}
	// 第2件商品压横跨第3和第4皮带
	else if((strlen(p_belt3->goods_code) > 0 ) && (p_belt4->goods_over == true))
	{
		p_belt4->belt_start2 = p_belt4->belt_pos_safe;
		SPDLOG_INFO("id {},belt 4 have goods ,and the belt4 and belt 3 have goods too", p_supply->id);
	}

	// 3 超长包检测

	// 4 手持扫码处理
	if (p_belt4->goods_over == true )
	{
		p_belt4->goods_time++;
		// 皮带停止后获取扫码枪的码
	}
	else
	{
		p_belt4->goods_time = 0;
	}

	if(strlen(p_belt3->goods_code) > 0)
	{
		// 如果相机自动报码，收到码值停止触发
		if(p_supply->vsion_scan == true)
		{
			SPDLOG_INFO("====== id = {},get code, and stop the scan ",p_supply->id);
		}
		p_supply->vsion_scan  = false;
	}

    // 5 传输及位置控制
	if (((p_belt4->goods_over == true || p_belt4->goods == true)))
	{
		// 给皮带速度
		if (1)
		{
			// 接收到供包指令后，清除缓存给 调度的 信息，以免下一辆车快速跟车导致请求同一个分播任务
			//if ((((code_isused) && (p_belt4->belt_dis > (p_belt4->belt_len / 2.0 + p_belt4->goods_len / 2.0 + m_feeder_info.belt4_dis))) || (belt_stop && m_feeder_info.isuse_fuse) || code_isused && (p_belt4->belt_dis > (p_belt4->belt_len + m_feeder_info.belt4_dis_stop))))
			if(1)
			{
				// if(p_belt4->belt_dis > (p_belt4->belt_len/2.0))
				if ((p_supply->sort_ready == false))
				{
					p_belt4->belt_spd = 0;

					if (p_belt4->belt_spd == 0)
					{
						static uint16_t log_count[2] ={0,0};

						if((log_count[p_supply->id] % 500) == 0)
						SPDLOG_INFO("warning :stop the belt 4,for no spully cmd,id {}", p_supply->id);
						log_count[p_supply->id]++;
					}
				}

				if (p_belt4->belt_spd > 0)
					SPDLOG_INFO("id{},stop the belt4 p_belt4->belt_dis = {},p_belt4->goods_len{},dis_count:{},len_count:{}", p_supply->id, p_belt4->belt_dis, p_belt4->goods_len, p_belt4->belt_dis_count, p_belt4->goods_len_count);
			

				if (p_supply->vsion_scan == true)
				SPDLOG_INFO("id = {},the goods go the pos ,the scan true, stop the scan ",p_supply->id);
				// 停止触发，适合下降沿报码的相机
			    p_supply->vsion_scan = false;

			}
		}
		// 日志
		if (p_belt4->belt_spd > 0)
		{
			//SPDLOG_INFO("id {},the belt4 dis_count:{},len_count:{}", p_supply->id, p_belt4->belt_dis_count, p_belt4->goods_len_count);
			SPDLOG_INFO("id {},the betl4 goods,diff:{}, len= {},dis = {},belt_end :{},belt_start:{},belt_pos:{}",
						p_supply->id,
						p_belt4->belt_end - p_belt4->belt_start, p_belt4->goods_len, p_belt4->belt_dis,
						p_belt4->belt_end, p_belt4->belt_start, (int32_t)p_belt4->belt_pos_safe);
		}
		// 6 供包控制
		
		bool flag = strcmp((char *)(p_belt4->task_msg.task_id),(char *)(m_scheduler_msg.sorting_action_record[p_supply->id].task_id));
		if((p_supply->sort_ready == true) && (flag != 0))
		{
			SPDLOG_INFO("====== id :{},recv supply cmd,but the record task_id  id not belt 4 task_id,err ",p_supply->id);
			p_supply->sort_ready = 0;
			p_supply->warning = SUPPLY_CMD_FAULT;
			m_scheduler_msg.supply_cmd_flag[p_supply->id] = 0;
		}

		if ((p_supply->sort_ready == true) && (flag == 0))
		//if ((p_supply->sort_ready == true) )
		{
			if(m_feeder_info.belt_count > 0)
			{
				// 原来是static 现在
				static uint32_t count[2] = {0};
				p_supply->vsion_scan = false;
				memset(&m_scheduler_msg.m_sort_action_recv[p_supply->id], 0, sizeof(m_scheduler_msg.m_sort_action_recv[0]));
				count[p_supply->id]++;
				p_belt4->belt_spd = BELT4_SPEED + 1000;
				// if ((code_isused && (p_belt4->belt_dis > (p_belt4->belt_len + p_belt4->goods_len + 30.0))) || count[p_supply->id] > 150 || (code_isused && (p_belt4->belt_dis > (p_belt4->belt_len * 2))))
				if (count[p_supply->id] > 30)
				{
					uint32_t &supply_success = p_belt4->supply_success;

					supply_success++;
					p_belt4->goods = 0;
					p_belt4->goods_over = 0;
					p_supply->sort_ready = 0;
					p_belt4->belt_end = 0;
					p_belt4->belt_start = p_belt4->belt_start2;
					p_belt4->belt_start2 = 0;
					p_belt4->belt_dis = 0;
					p_belt4->goods_len = 0;
					p_belt4->sensor_flag = 0;
					p_belt4->goods_time = 0;

					// 本次 供包指令请求-应答 标志 失效
					int id = p_supply->id;
					m_scheduler_msg.supply_cmd_flag[id] = false;

					strcpy(p_belt4->goods_code, "");
					memset(&p_belt4->task_msg, 0, sizeof(p_belt4->task_msg));
					// if(p_belt4->sensor_cnt > 0)
					// p_belt4->sensor_cnt = p_belt4->sensor_cnt  - 1;
					p_belt4->sensor_cnt = 0;
					SPDLOG_INFO("id {}, betl4 goods = {},p_belt4->sensor_cnt = {},timout ={}", p_supply->id, p_belt4->goods, p_belt4->sensor_cnt, count[p_supply->id]);
					count[p_supply->id] = 0;
					SPDLOG_INFO("====== supply success, count:{} id {}=====", supply_success, p_supply->id);
				}
			}
			else if ((m_feeder_info.belt_count == 0))
			{
				// 原来是static 现在
				static uint32_t count[2] = {0};
				// 这里复用为点灯io
				p_supply->vsion_scan = true;
				memset(&m_scheduler_msg.m_sort_action_recv[p_supply->id], 0, sizeof(m_scheduler_msg.m_sort_action_recv[0]));
				count[p_supply->id]++;
				p_belt4->belt_spd = BELT4_SPEED + 1000;
				// if ((code_isused && (p_belt4->belt_dis > (p_belt4->belt_len + p_belt4->goods_len + 30.0))) || count[p_supply->id] > 150 || (code_isused && (p_belt4->belt_dis > (p_belt4->belt_len * 2))))
				if ((p_belt4->sensor_up == true) && (p_belt4->sensor_flag ==  0))
				{
					p_belt4->sensor_flag = true;
					SPDLOG_INFO("====== handle feeder up up up up ");
				}
				else if ((p_belt4->sensor_up == false) && (p_belt4->sensor_flag == true))
				{
					if (count[p_supply->id] > 30)
					{ 
						uint32_t &supply_success = p_belt4->supply_success;

                        SPDLOG_INFO("====== handle feeder dn dn dn dn ");
						supply_success++;
						p_belt4->goods = 0;
						p_belt4->goods_over = 0;
						p_supply->sort_ready = 0;
						p_belt4->belt_end = 0;
						p_belt4->belt_start = p_belt4->belt_start2;
						p_belt4->belt_start2 = 0;
						p_belt4->belt_dis = 0;
						p_belt4->goods_len = 0;
						p_belt4->sensor_flag = 0;
						p_belt4->goods_time = 0;
                        // 熄灯
						p_supply->vsion_scan = false;

						// 本次 供包指令请求-应答 标志 失效
						int id = p_supply->id;
						m_scheduler_msg.supply_cmd_flag[id] = false;

						strcpy(p_belt4->goods_code, "");
						memset(&p_belt4->task_msg, 0, sizeof(p_belt4->task_msg));
						// if(p_belt4->sensor_cnt > 0)
						// p_belt4->sensor_cnt = p_belt4->sensor_cnt  - 1;
						p_belt4->sensor_cnt = 0;
						SPDLOG_INFO("id {}, betl4 goods = {},p_belt4->sensor_cnt = {},timout ={}", p_supply->id, p_belt4->goods, p_belt4->sensor_cnt, count[p_supply->id]);
						count[p_supply->id] = 0;
						SPDLOG_INFO("====== supply success, count:{} id {}=====", supply_success, p_supply->id);
					}
				}
			}
		}
	}

	if ((m_feeder_info.belt_count == 0) && (p_supply->sort_ready == false) )
	{
		if(p_belt4->sensor_up == true)
		{
			p_supply->warning = BELT4_SESOR_UP_NOT_EXP;

		}

	}
	return 0;
}
// 第4段供包机控制：标准控制
int16_t plc_agent::supply_belt4_unormal_ctrl(supply_package *p_supply)
{
	uint8_t id = p_supply->id;
	// SPDLOG_INFO(" supply_belt3_ctrl" );
	belt *p_belt4 = &p_supply->st_belt[3];
	belt *p_belt3 = &p_supply->st_belt[2];
	belt *p_belt2 = &p_supply->st_belt[1];
	belt *p_belt1 = &p_supply->st_belt[0];

	// test
	uint16_t &uCount = p_belt4->ucount;

	if (p_belt4->belt_spd > 0)
	{
		uCount++;
	}
	// 皮带传动条件
	if (p_belt4->goods == false)
	{
		// 给皮带速度
		p_belt4->belt_spd = BELT4_SPEED ;
	}

    //p_belt4->belt_spd = BELT4_SPEED ;
	// 1.接收到分播任务
	if (true == m_scheduler_msg.scheduler_manager_get_sort_task_flag(id))
	{
		m_scheduler_msg.scheduler_manager_set_sort_task_flag(id);
		// 判断分播任务的有效性
		if ((strlen((char *)m_scheduler_msg.m_sort_task_recv[id].task_id) != 0) && (strcmp((const char *)m_scheduler_msg.m_sort_task_recv[id].task_id, (const char *)p_belt4->task_msg.task_id) == 0))
		{
			// 获取有效的 分播地址
			p_belt4->task_msg.container = m_scheduler_msg.m_sort_task_recv[id].container;
			
			p_belt4->task_msg.vehicle_belt_speed = m_scheduler_msg.m_sort_task_recv[id].vehicle_belt_speed;
			memset(&m_scheduler_msg.m_sort_task_recv[id], 0, sizeof(m_scheduler_msg.m_sort_task_recv[id]));
			SPDLOG_INFO("###### id = {},belt4 recv task info:{},{},{},{}",p_supply->id, p_belt4->task_msg.gd_codes, p_belt4->task_msg.task_id, p_belt4->task_msg.container,p_belt4->task_msg.vehicle_belt_speed);
			// 拷贝、发布分播
		    //p_belt4->task_msg = p_belt3->task_msg;
		    p_belt4->pub_enable = 1;
		    p_belt4->task_msg.dev_id = p_supply->id;
			// 格口任务push

			if ((p_supply->sort_ready == 0))
			{
				// 格口任务push
				sort_info_push(p_supply);
			}
			else if(p_supply->sort_ready == 1)
			{
				p_supply->warning = SUPPLY_CMD_FAULT;
				p_supply->sort_ready = 0;
				m_scheduler_msg.supply_cmd_flag[p_supply->id] = 0;
				SPDLOG_INFO("id {} ,warning : recv supply cmd befor push sort info,err ", p_supply->id);
			}
		}
		else
		{
			p_supply->warning = TASK_ID_NOT_SAME;
			SPDLOG_INFO("warning : id ={} ERR TASK id  ",p_supply->id);
			// 错误
			SPDLOG_INFO("###### id ={}, ERR TASK: belt4  task id {}",p_supply->id, p_belt4->task_msg.task_id);
			SPDLOG_INFO("###### id ={},ERR TASK: thing task id {}", p_supply->id,m_scheduler_msg.m_sort_task_recv[id].task_id);
		}
	}

	// 2.皮带接货控制
	//if (((p_belt4->sensor_up == true) && (p_belt4->sensor_flag == false)) && (p_belt4->goods_over == false) && (p_belt3->goods) && (p_belt3->task_msg.container != 0))
	if ((((p_belt4->sensor_up == true) && (p_belt4->sensor_flag == false)) && p_belt4->goods_over == false) && (p_belt3->belt_spd > 0) )
	{
		SPDLOG_INFO("====== id = {}, the belt4 up up up",p_supply->id);
		// 获取伺服编码器位置 todo
		// test 上升沿，默认 container = 0
		p_belt4->goods_over  = true;
		p_belt4->sensor_flag = true;
		p_belt4->belt_start = p_belt4->belt_pos_safe;
		// 触发多面扫
		if (strlen(p_belt3->goods_code) == 0)
		{
			if(1)
			{
				// 触发多面扫 todo
				p_supply->vsion_scan = true;
				SPDLOG_INFO("====== id = {},the belt4 up ,start the scan ",p_supply->id);
			}
			//SPDLOG_INFO("====== id = {}, the belt4 up up up",p_supply->id);
		}

		// 码值跟踪 20221211  增加自动扫是否启动的判断
		else if ((strlen(p_belt3->goods_code) > 0))
		{
			// scanid
			p_supply->vsion_scan  = false;
			p_belt4->scan_id = p_belt3->scan_id;
			p_belt3->scan_id = 0;

			{
				std::lock_guard<std::mutex> lck(scan_lock);
				//strcpy(p_belt4->goods_code, p_belt3->goods_code);
				strcpy(p_supply->goods_code, p_belt3->goods_code);
			    strcpy(p_belt3->goods_code, "");
			}

			SPDLOG_INFO("id = {},belt4: goods code \n1:{}\n2:{}\n3:{}\n4:{}", p_supply->id, p_belt1->goods_code, p_belt2->goods_code, p_belt3->goods_code, p_belt4->goods_code);
			
			SPDLOG_INFO("id = {},the belt 3 handle code to supply code {}", p_supply->id, p_supply->goods_code);
			// SPDLOG_INFO("belt3: goods code \n1:{}\n2:{}\n3:{}\n4:{}",p_belt1->goods_code,p_belt2->goods_code,p_belt3->goods_code,p_belt4->goods_code );
            // 与自动扫统一push
            //goods_info_push(p_supply, p_belt4->goods_code, p_belt4->scan_id, 3);
		}

		// teset
		uCount = 0;
		// p_belt3->belt_start  =0 ;
	}
	// 第2件商品压横跨第3和第4皮带
	else if ((p_belt4->sensor_up == true && p_belt4->sensor_flag == false) && (p_belt4->goods_over == true))
	{
		p_belt4->belt_start2 = p_belt4->belt_pos_safe;
		SPDLOG_INFO("id {},belt 4 have goods ,and the belt4 and belt 3 have goods too", p_supply->id);
	}
	else if ((p_belt4->sensor_up == false) && (p_belt4->sensor_flag == true))
	{

		p_belt4->uCountEnd = uCount;
		// p_belt3->goods_over = false; 在第4段下降沿 清除
		p_belt4->goods = true;
		// 位置获取
		p_belt4->belt_end = p_belt4->belt_pos_safe;
		// 传感器计数 货物计数 可以和 goods优化
		p_belt4->sensor_cnt = 1;
		p_belt3->goods = false;
		p_belt3->sensor_cnt = 0;
		p_belt4->sensor_flag = false;

		SPDLOG_INFO("WARNING :id = {},betl 4 the goods Pass time {}", p_supply->id,uCount);

		// 下降沿停止触发
		// p_supply->vsion_scan = false;
		// SPDLOG_INFO("====== the belt 3 dn, stop the scan ");
		SPDLOG_INFO("====== id = {},the belt 4 dn dn dn",p_supply->id);
		SPDLOG_INFO("id ={}, belt4 goods = {},count = {}",p_supply->id, p_belt4->goods, p_belt4->sensor_cnt);
	}

	// SPDLOG_INFO("p_belt3->goods_over={},p_belt3->task_msg.container,{}",p_belt3->goods_over,p_belt3->task_msg.container);
	//  5.计算位置和长度
	// if((p_belt3->belt_dis >= (p_belt3->belt_len/2.0 + p_belt3->goods_len/2.0 - 100.0)) && (p_belt3->goods == true) && (p_belt4->goods == true))
	
	// 3 超长包检测
	m_package_over_length = true;
	m_package_length_max = m_feeder_info.goods_len_max;

	if (m_package_over_length == true)
	{
		if ((p_belt3->goods_len > m_package_length_max) || (p_belt3->goods_len_count > m_package_length_max))
		{
			// 超长包
			p_supply->warning = LENGTH_OVER_LIMIT;
			SPDLOG_INFO("###### id = {},the goods length over the max {},{},{}",p_supply->id, p_belt3->belt_len, p_belt3->goods_len_count, m_package_length_max);
		}
	}

	// 4 手持扫码处理
	if (p_belt4->goods_over == true )
	{
		p_belt4->goods_time++;
		// 皮带停止后获取扫码枪的码
	}
	else
	{
		p_belt4->goods_time = 0;
	}

	if(strlen(p_belt3->goods_code) > 0)
	{
		// 如果相机自动报码，收到码值停止触发
		if(p_supply->vsion_scan == true)
		{
			SPDLOG_INFO("====== id = {},get code, and stop the scan ",p_supply->id);
		}
		p_supply->vsion_scan  = false;
	}

    // 5 传输及位置控制
	if (((p_belt4->goods_over == true || p_belt4->goods == true)))
	{
		// 如果传感器触发，且无货，更新计算货物长度值
		if ((p_belt4->sensor_up) == true && (p_belt4->goods == false))
		{
			p_belt4->uCountEnd = uCount;
			p_belt4->belt_end = p_belt4->belt_pos_safe;
		}
		// 实时计算货物长度
		p_belt4->goods_len = (1.0 * (p_belt4->belt_end - p_belt4->belt_start)) / (p_belt4->code_count * p_belt4->gear_ratio) * (PI * p_belt4->diameter);

		p_belt4->goods_len = fabs(p_belt4->goods_len);
		// 实时皮带在收到货物起的运行距离
		p_belt4->belt_dis = (1.0 * ((int32_t)p_belt4->belt_pos_safe - p_belt4->belt_start)) / (p_belt4->code_count * p_belt4->gear_ratio) * (PI * p_belt4->diameter);

		p_belt4->belt_dis = fabs(p_belt4->belt_dis);
		//SPDLOG_INFO(" betl4 goods,diff:{}, len= {},dis = {},belt_end :{},belt_start:{},belt_pos:{}",\
		 p_belt4->belt_end - p_belt4->belt_start,p_belt4->goods_len,p_belt4->belt_dis,p_belt4->belt_end,p_belt4->belt_start,(int32_t)p_belt4->belt_pos_safe);

		if (p_belt4->goods_len > p_belt4->belt_len)
		{
			// p_belt4->goods_len = p_belt4->belt_len;
		}

		// 按照时间 速度计算长度
		p_belt4->goods_len_count = (0.01 * p_belt4->uCountEnd) * (BELT4_SPEED / 60.0 * 0.1 * 1.0 / p_belt4->gear_ratio * (PI * p_belt4->diameter));
		p_belt4->belt_dis_count = (0.01 * uCount) * (BELT4_SPEED / 60.0 * 0.1 * 1.0 / p_belt4->gear_ratio * (PI * p_belt4->diameter));
		// SPDLOG_INFO("test : belt 4 goods len {},dis {}",p_belt4->goods_len_count,p_belt4->belt_dis_count);

		bool belt_stop = (p_belt4->belt_dis_count >= (p_belt4->belt_len / 2.0 + p_belt4->goods_len_count / 2.0 + m_feeder_info.belt4_dis));

		// 伺服位置不准，增加数据是否可用判断
		bool code_isused = true;

		if (p_belt4->belt_dis > 3000 || p_belt4->belt_len > 3000)
		{
			code_isused = false;

			SPDLOG_INFO("======id {}, the servo 4 code is not is used ", p_supply->id);
		}
		else
		{
			code_isused = true;
		}
		// 检测异常
		if ((p_belt4->belt_len > 2000) && (p_belt4->goods_len_count > 2000))
		{
			p_supply->warning = SERVER_CODE_FAULT;
			SPDLOG_INFO("======id {}, the code and count are error ", p_supply->id);
		}

		// 给皮带速度
		if (1)
		{
			// 接收到供包指令后，清除缓存给 调度的 信息，以免下一辆车快速跟车导致请求同一个分播任务

			if ((((code_isused) && (p_belt4->belt_dis > (p_belt4->belt_len / 2.0 + p_belt4->goods_len / 2.0 + m_feeder_info.belt4_dis))) || (belt_stop && m_feeder_info.isuse_fuse) || code_isused && (p_belt4->belt_dis > (p_belt4->belt_len + m_feeder_info.belt4_dis_stop))))
			{
				// if(p_belt4->belt_dis > (p_belt4->belt_len/2.0))
				if ((p_supply->sort_ready == false))
				{
					p_belt4->belt_spd = 0;

					if (p_belt4->belt_spd == 0)
					{
						static uint16_t log_count[2] ={0,0};

						if((log_count[p_supply->id] % 500) == 0)
						SPDLOG_INFO("warning :stop the belt 4,for no spully cmd,id {}", p_supply->id);
						log_count[p_supply->id]++;
					}
				}

				if (p_belt4->belt_spd > 0)
					SPDLOG_INFO("id{},stop the belt4 p_belt4->belt_dis = {},p_belt4->goods_len{},dis_count:{},len_count:{}", p_supply->id, p_belt4->belt_dis, p_belt4->goods_len, p_belt4->belt_dis_count, p_belt4->goods_len_count);
			

				if (p_supply->vsion_scan == true)
				SPDLOG_INFO("id = {},the goods go the pos ,the scan true, stop the scan ",p_supply->id);
				// 停止触发，适合下降沿报码的相机
			    p_supply->vsion_scan = false;

			}
		}
		// 日志
		if (p_belt4->belt_spd > 0)
		{
			//SPDLOG_INFO("id {},the belt4 dis_count:{},len_count:{}", p_supply->id, p_belt4->belt_dis_count, p_belt4->goods_len_count);
			SPDLOG_INFO("id {},the betl4 goods,diff:{}, len= {},dis = {},belt_end :{},belt_start:{},belt_pos:{}",
						p_supply->id,
						p_belt4->belt_end - p_belt4->belt_start, p_belt4->goods_len, p_belt4->belt_dis,
						p_belt4->belt_end, p_belt4->belt_start, (int32_t)p_belt4->belt_pos_safe);
		}
		// 6 供包控制
		
		bool flag = strcmp((char *)(p_belt4->task_msg.task_id),(char *)(m_scheduler_msg.sorting_action_record[p_supply->id].task_id));
		if((p_supply->sort_ready == true) && (flag != 0))
		{
			SPDLOG_INFO("====== id :{},recv supply cmd,but the record task_id  id not belt 4 task_id,err ",p_supply->id);
			p_supply->sort_ready = 0;
			p_supply->warning = SUPPLY_CMD_FAULT;
			m_scheduler_msg.supply_cmd_flag[p_supply->id] = 0;
		}

		if ((p_supply->sort_ready == true) && (flag == 0))
		//if ((p_supply->sort_ready == true) )
		{
			// 原来是static 现在
			static uint32_t count[2] = {0};
            p_supply->vsion_scan = false;
			memset(&m_scheduler_msg.m_sort_action_recv[p_supply->id], 0, sizeof(m_scheduler_msg.m_sort_action_recv[0]));
			count[p_supply->id]++;
			p_belt4->belt_spd = BELT4_SPEED + 1000;
			if ((code_isused && (p_belt4->belt_dis > (p_belt4->belt_len + p_belt4->goods_len + 30.0))) || count[p_supply->id] > 150 || (code_isused && (p_belt4->belt_dis > (p_belt4->belt_len * 2))))
			{
				uint32_t &supply_success = p_belt4->supply_success;

				supply_success++;
				p_belt4->goods = 0;
				p_belt4->goods_over = 0;
				p_supply->sort_ready = 0;
				p_belt4->belt_end = 0;
				p_belt4->belt_start = p_belt4->belt_start2;
				p_belt4->belt_start2 = 0;
				p_belt4->belt_dis = 0;
				p_belt4->goods_len = 0;
				p_belt4->sensor_flag = 0;
				p_belt4->goods_time = 0;
				
				// 本次 供包指令请求-应答 标志 失效
				int id = p_supply->id;
				m_scheduler_msg.supply_cmd_flag[id] = false;

				strcpy(p_belt4->goods_code, "");
				memset(&p_belt4->task_msg, 0, sizeof(p_belt4->task_msg));
				// if(p_belt4->sensor_cnt > 0)
				// p_belt4->sensor_cnt = p_belt4->sensor_cnt  - 1;
				p_belt4->sensor_cnt = 0;
				SPDLOG_INFO("id {}, betl4 goods = {},p_belt4->sensor_cnt = {},timout ={}", p_supply->id, p_belt4->goods, p_belt4->sensor_cnt, count[p_supply->id]);
				count[p_supply->id] = 0;
				SPDLOG_INFO("====== supply success, count:{} id {}=====", supply_success, p_supply->id);
			}
		}
	}
	return 0;
}
// 第4段供包机控制:1超长件处理;2 第3段皮带商品压第4段皮带
int16_t plc_agent::supply_belt4_ctrl(supply_package *p_supply)
{
	// SPDLOG_INFO(" supply_belt4_ctrl ");
	belt *p_belt4 = &p_supply->st_belt[3];
	belt *p_belt3 = &p_supply->st_belt[2];
	belt *p_belt2 = &p_supply->st_belt[1];
	belt *p_belt1 = &p_supply->st_belt[0];

	uint16_t &uCount = p_belt4->ucount;

	if (p_belt4->belt_spd > 0)
	{
		uCount++;
	}
	// 皮带传动条件
	if (p_belt4->goods == false)
	{
		// 给皮带速度
		p_belt4->belt_spd = BELT4_SPEED;
		// SPDLOG_INFO(" supply_belt4_ctrl speed :{} ", p_belt4->belt_spd );
	}
	// 异常判定
	static uint16_t sensor_count[2] = {0};
	if (((p_belt4->sensor_up == true) && (p_belt4->sensor_flag == false)) && (p_belt4->goods_over == false) && (p_belt3->goods == 0) && (p_belt3->belt_spd > 0))
	{
		sensor_count[p_supply->id]++;
		if (sensor_count[p_supply->id] > 10)
		{
			p_supply->warning = BELT4_SESOR_UP_NOT_EXP;
			SPDLOG_INFO("warning belt4 sensor is up at no goods belt3 ");
		}
	}
	else
	{
		sensor_count[p_supply->id] = 0;
	}
	// 1.接包控制
	if (((p_belt4->sensor_up == true) && (p_belt4->sensor_flag == false)) && (p_belt4->goods_over == false) && (p_belt3->goods) && (p_belt3->task_msg.container != 0))
	{
		SPDLOG_INFO("======id {}, belt 4 sensor up up up up ", p_supply->id);
		uCount = 0;
		// 获取伺服编码器位置 todo
		int32_t belt4_pos = 0;
		// m_belt4_pos.pop(&belt4_pos);
		p_belt4->belt_start = p_belt4->belt_pos_safe;

		p_belt4->goods_over = true;
		p_belt4->sensor_flag = true;
#if 1
		// 五面扫停止scan
		if (p_supply->vsion_scan == true)
		{
			p_supply->vsion_scan = false;
			SPDLOG_INFO("the belt4 sensor up,stop the scan ");
		}
#endif	
		// 拷贝、发布分播
		p_belt4->task_msg = p_belt3->task_msg;
		p_belt4->pub_enable = 1;
		p_belt4->task_msg.dev_id = p_supply->id;
        SPDLOG_INFO("id {} ,the belt3 task_msg copy to belt4 task_msg",p_supply->id);
        // 此时接收到供包指令 是错误的，
        if((p_supply->sort_ready == 0))
		{
			sort_info_push(p_supply);
		}
		else
		{
			p_supply->warning = SUPPLY_CMD_FAULT;
			p_supply->sort_ready = 0;
			m_scheduler_msg.supply_cmd_flag[p_supply->id] = 0;
			SPDLOG_INFO("id {} ,warning : recv supply cmd befor push sort info,err  ",p_supply->id);
		}
		
		 // 上升沿时数据信息交付，下降沿有风险，sensor可能一直处于上升沿，3段数据无法清除，导致异常。
		if (1)
		{
			// 如果是两端皮带供包段扫码，则不在这里处理码值跟踪 20230412
			p_belt4->scan_id = p_belt3->scan_id;
			p_belt3->scan_id = 0;
			strcpy(p_belt4->goods_code, p_belt3->goods_code);
			// 上升沿
		    strcpy(p_belt3->goods_code, "");
			
			SPDLOG_INFO("id {},belt3 code to belt4: goods code \n1:{}\n2:{}\n3:{}\n4:{}",p_supply->id, p_belt1->goods_code, p_belt2->goods_code, p_belt3->goods_code, p_belt4->goods_code);
		}

		//memset(&p_belt3->task_msg,0,sizeof(p_belt3->task_msg));

		SPDLOG_INFO("id {}, p_belt4->belt_start  = {},p_belt4->belt_pos = {}", p_supply->id, p_belt4->belt_start, p_belt4->belt_pos_safe);
	}
	// 第2件商品压横跨第3和第4皮带
	else if ((p_belt4->sensor_up == true && p_belt4->sensor_flag == false) && (p_belt4->goods_over == true))
	{
		p_belt4->belt_start2 = p_belt4->belt_pos_safe;
		SPDLOG_INFO("id {},belt 4 have goods ,and the belt4 and belt 3 have goods too", p_supply->id);
	}
	// 下降沿
	else if ((p_belt4->sensor_up == false) && (p_belt4->sensor_flag == true) )
	{
		if (p_belt4->sensor_dn_cnt < 10)
		{
			p_belt4->sensor_dn_cnt++;
		}
		else
		{
            p_belt4->sensor_dn_cnt = 0;

			p_belt4->uCountEnd = uCount;
			p_belt4->sensor_flag = false;
			p_belt4->goods = true;

			p_belt4->sensor_cnt = p_belt4->sensor_cnt + 1;
			// 获取编码器 todo

			p_belt4->belt_end = p_belt4->belt_pos_safe;
			SPDLOG_INFO("id {}, p_belt4->belt_end = {},p_belt4->belt_pos_safe = {}", p_supply->id, p_belt4->belt_end, p_belt4->belt_pos_safe);
			// 上包完成，维护上一个皮带的传感器计数
			if (1)
			{
				p_belt3->sensor_cnt = 0;
				p_belt3->goods = false;
				// 清除一次，防止第三段有触发传感器
				p_belt3->goods_over = 0;
				memset(&p_belt3->task_msg, 0, sizeof(p_belt3->task_msg));
				// p_belt3->sensor_cnt = p_belt3->sensor_cnt -1; ljl 20220623
			}

			SPDLOG_INFO("id {}, betl4 goods = {},count = {}", p_supply->id, p_belt4->goods, p_belt4->sensor_cnt);
			SPDLOG_INFO("id {},WARNING :betl 4 the goods Pass time {}", p_supply->id, uCount);
			SPDLOG_INFO("======id {}, belt 4 sensor dn dn dn", p_supply->id);
		}
	}
	// 传输过程判断
	else if ((p_belt4->sensor_up == true) && (p_belt4->sensor_flag == true) )
	{
		p_belt4->sensor_dn_cnt = 0;
	}

	if (p_belt3->sensor_cnt == 0)
	{
		p_belt3->goods = false;
	}

	if (((p_belt4->goods_over == true || p_belt4->goods == true)))
	{
		// 如果传感器触发，且无货，更新计算货物长度值
		if ((p_belt4->sensor_up) == true && (p_belt4->goods == false))
		{
			p_belt4->uCountEnd = uCount;
			p_belt4->belt_end = p_belt4->belt_pos_safe;
		}
		// 实时计算货物长度
		p_belt4->goods_len = (1.0 * (p_belt4->belt_end - p_belt4->belt_start)) / (p_belt4->code_count * p_belt4->gear_ratio) * (PI * p_belt4->diameter);

		p_belt4->goods_len = fabs(p_belt4->goods_len);
		// 实时皮带在收到货物起的运行距离
		p_belt4->belt_dis = (1.0 * ((int32_t)p_belt4->belt_pos_safe - p_belt4->belt_start)) / (p_belt4->code_count * p_belt4->gear_ratio) * (PI * p_belt4->diameter);

		p_belt4->belt_dis = fabs(p_belt4->belt_dis);
		//SPDLOG_INFO(" betl4 goods,diff:{}, len= {},dis = {},belt_end :{},belt_start:{},belt_pos:{}",\
		 p_belt4->belt_end - p_belt4->belt_start,p_belt4->goods_len,p_belt4->belt_dis,p_belt4->belt_end,p_belt4->belt_start,(int32_t)p_belt4->belt_pos_safe);

		if (p_belt4->goods_len > p_belt4->belt_len)
		{
			// p_belt4->goods_len = p_belt4->belt_len;
		}

		// 按照时间 速度计算长度
		p_belt4->goods_len_count = (0.01 * p_belt4->uCountEnd) * (BELT4_SPEED / 60.0 * 0.1 * 1.0 / p_belt4->gear_ratio * (PI * p_belt4->diameter));
		p_belt4->belt_dis_count = (0.01 * uCount) * (BELT4_SPEED / 60.0 * 0.1 * 1.0 / p_belt4->gear_ratio * (PI * p_belt4->diameter));
		// SPDLOG_INFO("test : belt 4 goods len {},dis {}",p_belt4->goods_len_count,p_belt4->belt_dis_count);

		bool belt_stop = (p_belt4->belt_dis_count >= (p_belt4->belt_len / 2.0 + p_belt4->goods_len_count / 2.0 + m_feeder_info.belt4_dis));
		if (p_belt4->belt_spd > 0)
		{
			//SPDLOG_INFO("id {},the belt4 dis_count:{},len_count:{}", p_supply->id, p_belt4->belt_dis_count, p_belt4->goods_len_count);
			SPDLOG_INFO("id {},the betl4 goods,diff:{}, len= {},dis = {},belt_end :{},belt_start:{},belt_pos:{}",
						p_supply->id,
						p_belt4->belt_end - p_belt4->belt_start, p_belt4->goods_len, p_belt4->belt_dis,
						p_belt4->belt_end, p_belt4->belt_start, (int32_t)p_belt4->belt_pos_safe);
		}
		// 伺服位置不准，增加数据是否可用判断
		bool code_isused = true;

		if (p_belt4->belt_dis > 3000 || p_belt4->belt_len > 3000)
		{
			code_isused = false;

			SPDLOG_INFO("======id {}, the servo 4 code is not is used ", p_supply->id);
		}
		else
		{
			code_isused = true;
		}
		// 检测异常
		if ((p_belt4->belt_len > 2000) && (p_belt4->goods_len_count > 2000))
		{
			p_supply->warning = SERVER_CODE_FAULT;
			SPDLOG_INFO("======id {}, the code and count are error ", p_supply->id);
		}

        // 6 供包指令合理性判断
		
		bool flag = strcmp((char *)(p_belt4->task_msg.task_id),(char *)(m_scheduler_msg.sorting_action_record[p_supply->id].task_id));
		if((p_supply->sort_ready == true) && (flag != 0))
		{
			SPDLOG_INFO("====== recv supply cmd,but the record task_id  id not belt 4 task_id,err ");
			p_supply->sort_ready = 0;
			p_supply->warning = SUPPLY_CMD_FAULT;
		}
		// 给皮带速度
		if ((p_supply->sort_ready == false))
		{
			// 接收到供包指令后，清除缓存给 调度的 信息，以免下一辆车快速跟车导致请求同一个分播任务

			if ((((code_isused) && (p_belt4->belt_dis > (p_belt4->belt_len / 2.0 + p_belt4->goods_len / 2.0 + m_feeder_info.belt4_dis))) || (belt_stop && m_feeder_info.isuse_fuse) || code_isused && (p_belt4->belt_dis > (p_belt4->belt_len + m_feeder_info.belt4_dis_stop))))
			{
				if (p_belt4->belt_spd > 0)
					SPDLOG_INFO("id{},stop the belt4 p_belt4->belt_dis = {},p_belt4->goods_len{},dis_count:{},len_count:{}", p_supply->id, p_belt4->belt_dis, p_belt4->goods_len, p_belt4->belt_dis_count, p_belt4->goods_len_count);

				// if(p_belt4->belt_dis > (p_belt4->belt_len/2.0))
				{
					if (p_belt4->belt_spd > 0)
						SPDLOG_INFO("warning : id {}  stop the belt 4,for no spully cmd,", p_supply->id);
					p_belt4->belt_spd = 0;
				}
			}
		}
		else if (p_supply->sort_ready == true)
		{
			// 原来是static 现在
			static uint32_t count[2] = {0};

			memset(&m_scheduler_msg.m_sort_action_recv[p_supply->id], 0, sizeof(m_scheduler_msg.m_sort_action_recv[0]));
			count[p_supply->id]++;
			p_belt4->belt_spd = BELT4_SPEED + 1000;
			if ((code_isused && (p_belt4->belt_dis > (p_belt4->belt_len + p_belt4->goods_len + 30.0))) || count[p_supply->id] > 150 || (code_isused && (p_belt4->belt_dis > (p_belt4->belt_len * 2))))
			{
				uint32_t &supply_success = p_belt4->supply_success;

				supply_success++;
				if(p_belt4->goods == 0)
				{
					// 供包成功，但没有belt down 下降沿，货物还在传感器间，传输受阻。
                    SPDLOG_INFO("id {},supply success,and the belt4 has goods record is 0,belt 4 dn signal not recv",p_supply->id);
				    //memset(&p_belt3->task_msg, 0, sizeof(p_belt3->task_msg));
					p_supply->warning = SUPPLY_NO_DOWN;
					SPDLOG_INFO("warning: id {},id {},supply success,and set belt3 task_msg 0",p_supply->id);
				}
				p_belt4->goods = 0;
				p_belt4->goods_over = 0;
				p_supply->sort_ready = 0;
				p_belt4->belt_end = 0;
				p_belt4->belt_start = p_belt4->belt_start2;
				p_belt4->belt_start2 = 0;
				p_belt4->belt_dis = 0;
				p_belt4->goods_len = 0;
				p_belt4->sensor_flag = 0;
				// 本次 供包指令请求-应答 标志 失效
				int id = p_supply->id;
				m_scheduler_msg.supply_cmd_flag[id] = false;

				strcpy(p_belt4->goods_code, "");
				memset(&p_belt4->task_msg, 0, sizeof(p_belt4->task_msg));
				// if(p_belt4->sensor_cnt > 0)
				// p_belt4->sensor_cnt = p_belt4->sensor_cnt  - 1;
				p_belt4->sensor_cnt = 0;
				SPDLOG_INFO("id {}, betl4 goods = {},p_belt4->sensor_cnt = {},timout ={}", p_supply->id, p_belt4->goods, p_belt4->sensor_cnt, count[p_supply->id]);
				count[p_supply->id] = 0;
				SPDLOG_INFO("====== supply success, count:{} id {}=====", supply_success, p_supply->id);
			}
		}
	}

	return 0;
}
// 供包台伺服初始化
int plc_agent::supply_servo_can_init(uint8_t uCanid)
{
	int iCanSocketFd0 = 0;
	int iCanSocketFd1 = 0;
	int iRet = 0;

	int iSendTimes = 8;

	int iRecvSuccessTimes = 0;
	int i = 0;
	int inum = 0;
	char *pAddr = NULL;

	fd_set stReadFds = {0};
	struct timeval stTimeOut = {0, 0};
	struct can_frame stSendCanFrame = {0};
	struct can_frame stRecvCanFrame = {0};

	// CAN1 SEND TO CAN1
	stSendCanFrame.can_id = 0x600 + uCanid;
	stSendCanFrame.can_dlc = 8;

	typedef struct can_data
	{
		char data[8];
	} can_device_t;

	can_device_t stSetCanFrame[8] = {

		{
			0x2f,
			0x60,
			0x60,
			0x00,
			0x03,
			0x00,
			0x00,
			0x00,
		},
#ifdef THREE_BELT
		{
			0x23,
			0x83,
			0x60,
			0x00,
			0x2C,
			0x01,
			0x00,
			0x00,
		},
#else
		{
			0x23,
			0x83,
			0x60,
			0x00,
			0x84,
			0x03,
			0x00,
			0x00,
		},
#endif
		{
			0x23,
			0x84,
			0x60,
			0x00,
			0x2C,
			0x01,
			0x00,
			0x00,
		},

		#if 1
		{
			0x01,
			0x00,
			0x00,
			0x00,
			0x00,
			0x00,
			0x00,
			0x00,
		},
		#endif
		{
			0x2B,
			0x40,
			0x60,
			0x00,
			0x06,
			0x00,
			0x00,
			0x00,
		},
		{
			0x2B,
			0x40,
			0x60,
			0x00,
			0x07,
			0x00,
			0x00,
			0x00,
		},
		{
			0x2B,
			0x40,
			0x60,
			0x00,
			0x0F,
			0x00,
			0x00,
			0x00,
		},
        // 读状态字
		{
			0x40,
			0x41,
			0x60,
			0x00,
			0x00,
			0x00,
			0x00,
			0x00,
		}, // 目标速度
	};
	int j = 0;

	
	memcpy(&stSetCanFrame[1].data[4],&m_feeder_info.belt_acc,sizeof(m_feeder_info.belt_acc));
	memcpy(&stSetCanFrame[2].data[4],&m_feeder_info.belt_dcc,sizeof(m_feeder_info.belt_dcc));
	m_can_recv_state = 0;
	for (i = 0; i < iSendTimes; i++)
	{
		FD_ZERO(&stReadFds);
		FD_SET(m_CanSocketFd0, &stReadFds);
		stTimeOut.tv_usec = 50000;

		stSetCanFrame[3].data[1] = uCanid;
		memcpy(&stSendCanFrame.data, &(stSetCanFrame[i].data[0]), 8); // 要发送的数据
		if(i == 3)
		{
			stSendCanFrame.can_id = 0x0000;
			stSendCanFrame.can_dlc = 8;
		}
		else
		{
			stSendCanFrame.can_id = 0x600 + uCanid;
		}
		iRet = write(m_CanSocketFd0, &stSendCanFrame, sizeof(stSendCanFrame)); // 发送初始化指令

		if (iRet < sizeof(stSendCanFrame))
		{
			SPDLOG_INFO("can write  init err");
			usleep(50000);
			i--;
			break;
		}
		// wait for state change
        for(auto m = 0; m < 3; m++)
		{
			if (m_can_recv_state == 1)
			{
				break;
			}
			usleep(50000);
		}
        // 检查配置sdo ack 接收状态 是否正确
		if (m_can_recv_state == 0)
		{
			if(i !=3)
			i--;

			if(iSendTimes == 7)
			{
				SPDLOG_INFO(" servo state ,enable failure");
			}
		}
        m_can_recv_state = 0;
		SPDLOG_INFO(" init servo num {}",i);
	}

	SPDLOG_INFO(" init_can for servo pass ");
	return iCanSocketFd0;
}
// can io 启动
int plc_agent::supply_canio_start(uint8_t uCanid)
{
	int iRet = 0;
	// CAN1 SEND TO CAN1
	struct can_frame stSendCanFrame = {0};

	stSendCanFrame.can_id = 0x00;
	stSendCanFrame.can_dlc = 2;

	typedef struct can_data
	{
		char data[8];
	} can_device_t;

	char stStartCanFrame[] = {0x01, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
	// add. 增加canid 设置 by 20230307
	stStartCanFrame[1] = uCanid;
	memcpy(&stSendCanFrame.data[0], &stStartCanFrame[0], 8);

	iRet = write(m_CanSocketFd0, &stSendCanFrame, sizeof(stSendCanFrame)); // 下发速度指令
	SPDLOG_INFO(" init_can for canio start,canid {} ", uCanid);
	return iRet;
}
// can io 设置
int plc_agent::supply_canio_set(uint8_t uCanid, bool uData, uint8_t pin)
{
	int iRet = 0;
	// CAN1 SEND TO CAN1
	struct can_frame stSendCanFrame = {0};

	stSendCanFrame.can_id = 0x600 + uCanid;
	stSendCanFrame.can_dlc = 8;

	typedef struct can_data
	{
		char data[8];
	} can_device_t;

	char stSetCanFrame[] = {0x23, 0x02, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00};

	// 使用 sdo 设置有效位
	if (pin <= 8)
	{
		stSetCanFrame[6] = 1 << (pin - 1);
		if (uData == 1)
		{
			stSetCanFrame[4] = 1 << (pin - 1);
		}
		else
		{
			// 不处理
		}
	}
	else if (pin > 8 && pin <= 16)
	{
		stSetCanFrame[7] = 1 << (pin - 1 - 8);
		if (uData == 1)
		{
			stSetCanFrame[5] = 1 << (pin - 1 - 8);
		}
		else
		{
			// 不处理
		}
	}

	memcpy(&stSendCanFrame.data, &stSetCanFrame[0], 8);
	iRet = write(m_CanSocketFd0, &stSendCanFrame, sizeof(stSendCanFrame));

	//SPDLOG_INFO("scan set cmd: canid={},Data={},pin: {},data:{},{},{},{},{},{},{},{}",uCanid,uData,pin,stSendCanFrame.data[0],\
	stSendCanFrame.data[1],\
	stSendCanFrame.data[2],\
	stSendCanFrame.data[3],\
	stSendCanFrame.data[4],\
	stSendCanFrame.data[5],\
	stSendCanFrame.data[6],\
	stSendCanFrame.data[7]);
	return iRet;
}
// can io 设置
int plc_agent::supply_vsion_set(supply_package *p_supply)
{
	static uint16_t uLast_set[2] = {0xff, 0xff};
	// 增加重复设置,可靠性触发
	static uint32_t uCount[2] = {0, 0};

	// 这里需要考虑其他设置 io 的状态，有耦合
	
	if (uCount[p_supply->id] % 3 == 0)
	{
		// scan
		supply_canio_set(5 + 5 * p_supply->id, p_supply->vsion_scan, 11);

	}	
	else if(uCount[p_supply->id] % 3 == 2)
	{
		// weight
		if(m_feeder_info.weight_enable == 1)
		supply_canio_set(5 + 5 * p_supply->id, p_supply->weight_meter, 14);
	}
	// SPDLOG_INFO(" vsion scan,id={}, cmd = {},pin = {}",p_supply->id,p_supply->vsion_scan,11);

	uCount[p_supply->id]++;
	return 0;
}
// 速度控制
int plc_agent::supply_servo_speed_set(int iCanSocketFd0, uint8_t uCanid, int32_t uSpeed)
{
	int iRet = 0;
	// CAN1 SEND TO CAN1
	struct can_frame stSendCanFrame = {0};
    // sdo
	//stSendCanFrame.can_id = 0x600 + uCanid;
	// rpdo
	stSendCanFrame.can_id = 0x400 + uCanid;

	stSendCanFrame.can_dlc = 8;
	typedef struct can_data
	{
		char data[8];
	} can_device_t;

	char stSetCanFrame[] =
		{
			0x23,
			0xff,
			0x60,
			0x00,
			0x00,
			0x00,
			0x00,
			0x00,
		}; // 目标速度
    // sdo
	//memcpy(&stSetCanFrame[4], &uSpeed, 4);
	// pdo
	memcpy(&stSetCanFrame[0], &uSpeed, 4);
	memcpy(&stSendCanFrame.data[0], &stSetCanFrame[0], 8);

	iRet = write(m_CanSocketFd0, &stSendCanFrame, sizeof(stSendCanFrame)); // 下发速度指令

	// SPDLOG_INFO(" servo speed set fd:{}, id:{},speed :{} ",p_supply->iCanSocketFd0,uCanid,uSpeed);
	return iRet;
}
// 速度速度控制
int plc_agent::supply_belt_speed_set(supply_package *p_supply)
{

	belt *p_belt4 = &p_supply->st_belt[3];
	belt *p_belt3 = &p_supply->st_belt[2];
	belt *p_belt2 = &p_supply->st_belt[1];
	belt *p_belt1 = &p_supply->st_belt[0];

	int16_t speed1;
	int16_t speed2;
	int16_t speed3;
	int16_t speed4;

	// 增加重复设置,可靠性触发
	static uint16_t uCount[2] = {0, 0};

    // 降低总线负载率；冗余 can 容错率，重发机制
	uCount[p_supply->id]++;
	if (uCount[p_supply->id] % 3 != 0)
	{
		//return 0;
	}

	if (p_supply->id == 1)
	{
		// 第2个供包机 速度反向
		speed1 = m_feeder_info.second_feeder_dir * p_belt1->belt_spd;
		speed2 = m_feeder_info.second_feeder_dir *p_belt2->belt_spd;
		speed3 = m_feeder_info.second_feeder_dir *p_belt3->belt_spd;
		speed4 = m_feeder_info.second_feeder_dir *p_belt4->belt_spd;
	}
	else if (p_supply->id == 0)
	{
		speed1 = m_feeder_info.first_feeder_dir * p_belt1->belt_spd;
		speed2 = m_feeder_info.first_feeder_dir * p_belt2->belt_spd;
		speed3 = m_feeder_info.first_feeder_dir * p_belt3->belt_spd;
		speed4 = m_feeder_info.first_feeder_dir * p_belt4->belt_spd;
	}
	else
	{
		SPDLOG_INFO(" supply_belt_speed_set err,id = {} ", p_supply->id);
		p_supply->id = 0;
	}

	// 皮带速度控制逻辑
	switch (m_feeder_info.belt_count)
	{
	case BELT4:
	{
		if (m_feeder_info.feeder_belt_virtual == 0)
		{
			supply_servo_speed_set(p_supply->iCanSocketFd0, 1 + 5 * p_supply->id, speed1);
		}
		else
		{
			// 外接皮带1
			if ((m_feeder_info.first_feeder_dir * speed1) > 0)
			{
				// io 控制虚拟第一段皮带 canio 15控制
				supply_canio_set(5 + p_supply->id * 5, 1, 15);
				//supply_canio_set(5 + p_supply->id * 5, 0, 15);
			}
			else
			{
				// io 控制虚拟第一段皮带 canio 15控制
				supply_canio_set(5 + p_supply->id * 5, 0, 15);

				//supply_canio_set(5 + p_supply->id * 5, 1, 15);
			}
		}

		supply_servo_speed_set(p_supply->iCanSocketFd0, 2 + 5 * p_supply->id, speed2);
		supply_servo_speed_set(p_supply->iCanSocketFd0, 3 + 5 * p_supply->id, speed3);
		supply_servo_speed_set(p_supply->iCanSocketFd0, 4 + 5 * p_supply->id, speed4);
		break;
	}
	case BELT3:
	{
		supply_servo_speed_set(p_supply->iCanSocketFd0, 2 + 5 * p_supply->id, speed2);
		supply_servo_speed_set(p_supply->iCanSocketFd0, 3 + 5 * p_supply->id, speed3);
		supply_servo_speed_set(p_supply->iCanSocketFd0, 4 + 5 * p_supply->id, speed4);

        // 外接皮带2，看做三段皮带，把外接皮带与第2节皮带看做一体控制
		if (m_feeder_info.feeder_belt_virtual ==  1)
		{
			if ((m_feeder_info.first_feeder_dir * speed2) > 0)
			{
				// io 控制虚拟第一段皮带 canio 15控制
				supply_canio_set(5 + p_supply->id * 5, 1, 15);
				//supply_canio_set(5 + p_supply->id * 5, 0, 15);
			}
			else
			{
				// io 控制虚拟第一段皮带 canio 15控制
				supply_canio_set(5 + p_supply->id * 5, 0, 15);

				//supply_canio_set(5 + p_supply->id * 5, 1, 15);
			}

		}
		break;
	}
	case BELT2:
	{
		supply_servo_speed_set(p_supply->iCanSocketFd0, 3 + 5 * p_supply->id, speed3);
		supply_servo_speed_set(p_supply->iCanSocketFd0, 4 + 5 * p_supply->id, speed4);
		break;
	}
	case BELT1:
	{
		supply_servo_speed_set(p_supply->iCanSocketFd0, 4 + 5 * p_supply->id, speed4);
		break;
	}
	case 0:
	{
		break;
	}
	default:
		break;
	}

	p_belt1->speed_old = p_belt1->belt_spd;
	p_belt2->speed_old = p_belt2->belt_spd;
	p_belt3->speed_old = p_belt3->belt_spd;
	p_belt4->speed_old = p_belt4->belt_spd;

	return 0;
}
//
int plc_agent::supply_servo_position_get(uint8_t uCanid)
{
	int iCanSocketFd0 = 0;
	int iCanSocketFd1 = 0;
	int iRet = 0;

	uint32_t position = 0;

	fd_set stReadFds = {0};
	struct timeval stTimeOut = {0, 0};
	struct can_frame stSendCanFrame = {0};
	static struct can_frame stRecvCanFrame = {0};

	// CAN1 SEND TO CAN1
	stSendCanFrame.can_id = 0x600 + uCanid;
	stSendCanFrame.can_dlc = 8;
	stTimeOut.tv_usec = 50000;

	typedef struct can_data
	{
		char data[8];
	} can_device_t;

	char stSetCanFrame[] =
		{0x40, 0x63, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00}; // 获取目标位置

	FD_ZERO(&stReadFds);
	FD_SET(m_CanSocketFd0, &stReadFds);
	memcpy(&stSendCanFrame.data, &stSetCanFrame[0], 8);

	iRet = write(m_CanSocketFd0, &stSendCanFrame, sizeof(stSendCanFrame));

	return iRet;
}

uint32_t plc_agent::supply_belt_position_cmd(supply_package *p_supply)
{

	belt *p_belt4 = &p_supply->st_belt[3];
	belt *p_belt3 = &p_supply->st_belt[2];
	belt *p_belt2 = &p_supply->st_belt[1];
	belt *p_belt1 = &p_supply->st_belt[0];
#if 0
	// 
	if(((p_belt2->goods == true) && (p_belt2->belt_spd > 100)) || ((p_belt3->goods == true) && (p_belt3->belt_spd > 100)))
	{
		supply_servo_position_get(3);
	}
	
	if(((p_belt4->goods == true) && (p_belt4->belt_spd > 100)) || ((p_belt3->goods == true) && (p_belt3->belt_spd > 100)))
	//if(p_belt3->goods == true || p_belt4->goods == true)
	{
         supply_servo_position_get(4);
		 //SPDLOG_INFO(" postion get cmd send ");
	}

#else
	static int select[2] = {1, 1};

	if (select[p_supply->id] == 1)
	{
		select[p_supply->id] = 2;
		//supply_servo_position_get(3 + 5 * p_supply->id);
	}
	else if (select[p_supply->id] == 2)
	{
		select[p_supply->id] = 1;
		//supply_servo_position_get(4 + 5 * p_supply->id);
	}
	// else if(select == )

#endif
}
int plc_agent::can_to_net(can_frame *can_frame_msg, char *net_msg)
{
	if(can_frame_msg == nullptr || net_msg == nullptr)
		return -1;
    // tcp: 0x08 udp : 0x28
	net_msg[0] = 0x28;
	net_msg[1] = (can_frame_msg->can_id >> 24) & 0xff;
	net_msg[2] = (can_frame_msg->can_id >> 16) & 0xff;
	net_msg[3] = (can_frame_msg->can_id >> 8) & 0xff;
	net_msg[4] = can_frame_msg->can_id & 0xff;
	net_msg[5] = can_frame_msg->data[0];
	net_msg[6] = can_frame_msg->data[1];
	net_msg[7] = can_frame_msg->data[2];
	net_msg[8] = can_frame_msg->data[3];
	net_msg[9] = can_frame_msg->data[4];
	net_msg[10] = can_frame_msg->data[5];
	net_msg[11] = can_frame_msg->data[6];
	net_msg[12] = can_frame_msg->data[7];
		
	return 0;
}
bool plc_agent::servo_ctrl(supply_package *p_supply)
{
    can_frame & f = p_supply->action_msg.send_ctrl_frame;
	can_frame & q_f = p_supply->action_msg.send_query_frame;
	para_t & para = m_feeder_info.para;

	//SPDLOG_INFO("server cmd {}",p_supply->action_msg.run_dir);

	if(p_supply->action_msg.run_dir == 0)
	{
		if (p_supply->action_msg.run_dir_config != 1)
		{
			servo_dev[p_supply->id].forward_turn_time_ctrl(para.belt_turn_speed, para.belt_turn_time, para.belt_turn_delay_time, f);
		}
	    else 
		{
			servo_dev[p_supply->id].reserve_turn_time_ctrl(para.belt_turn_speed, para.belt_turn_time, para.belt_turn_delay_time, f);
		}
	}
	else if(p_supply->action_msg.run_dir == 1)
	{
		if (p_supply->action_msg.run_dir_config != 1)
		{
			servo_dev[p_supply->id].reserve_turn_time_ctrl(para.belt_turn_speed, para.belt_turn_time, para.belt_turn_delay_time, f);
		}
		else
		{
			servo_dev[p_supply->id].forward_turn_time_ctrl(para.belt_turn_speed, para.belt_turn_time, para.belt_turn_delay_time, f);
		}
	}
	else if (p_supply->action_msg.run_dir == 3)
	{
		servo_dev[p_supply->id].forward_turn_time_ctrl(0, 0, 0, f);

	}

    // 控制帧
    if((p_supply->action_msg.old_run_dir != p_supply->action_msg.run_dir) || ((p_supply->action_msg.run_time_ctrl % 20) == 0))
	{
		m_can_send_msg_queue.push(f);

		if(p_supply->action_msg.old_run_dir != p_supply->action_msg.run_dir)
		{
			SPDLOG_INFO("change data, server cmd id = {},dir = {}",p_supply->id,p_supply->action_msg.run_dir);
		}
		
	}
    
	p_supply->action_msg.old_run_dir = p_supply->action_msg.run_dir;

    // 查询帧
    if((p_supply->action_msg.run_time_ctrl % 500 == 0))
	{
		servo_dev[p_supply->id].query(q_f);
	    m_can_send_msg_queue.push(q_f);
	}
	p_supply->action_msg.run_time_ctrl++;


    return true;
}
#if 1
bool plc_agent::servo_ctrl2(supply_package *p_supply)
{
	can_frame & f = p_supply->action_msg.send_ctrl_frame;
	can_frame & q_f = p_supply->action_msg.send_query_frame;
	para_t & para = m_feeder_info.para;

	int iRet = 0;
	static uint16_t ctr_msg[3] = {0};
	// CAN1 SEND TO CAN1
	struct can_frame stSendCanFrame = {0};
	static uint16_t last_ctr_msg = 0;

	stSendCanFrame.can_id = 0x600 + 0x0A;
	stSendCanFrame.can_dlc = 8;

	typedef struct can_data
	{
		char data[8];
	} can_device_t;

	char stSetCanFrame[] = {0x23, 0x02, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00};

	// 使用 sdo 设置有效位


	if(p_supply->id == 0)
	{
		static uint16_t count = 0;
		if(p_supply->action_msg.run_dir == 0)
		{
			ctr_msg[p_supply->id] = 1;
		}
		else if(p_supply->action_msg.run_dir == 1)
		{
			ctr_msg[p_supply->id] = 2;
		}
		else if (p_supply->action_msg.run_dir == 2)
		{
			ctr_msg[p_supply->id] = 3;
		}
		else if (p_supply->action_msg.run_dir == 3)
		{
			ctr_msg[p_supply->id] = 0;
		}
		else
		{
			ctr_msg[p_supply->id] = 0;
		}
        //strcpy(m_supply[0].goods_code, str_list[0].c_str());
		if (strlen(p_supply->goods_code) > 0)
		{
			count++;
			if(count >  10)
			{
				strcpy(p_supply->goods_code,"");
			}
			ctr_msg[p_supply->id] = ctr_msg[p_supply->id] + 0x10;
		}
		else 
		{
			count = 0;
			ctr_msg[p_supply->id] = ctr_msg[p_supply->id] + 0x00;
		}

		stSetCanFrame[6] =0x13;
		stSetCanFrame[4] = ctr_msg[p_supply->id];
	}
	else if (p_supply->id == 1)
	{
		static uint16_t count = 0;
		if (p_supply->action_msg.run_dir == 0)
		{
			ctr_msg[p_supply->id]  = 4;
		}
		else if (p_supply->action_msg.run_dir == 1)
		{
			ctr_msg[p_supply->id]  = 8;
		}
		else if (p_supply->action_msg.run_dir == 2)
		{
			ctr_msg[p_supply->id]  = 12;
		}
		else if (p_supply->action_msg.run_dir == 3)
		{
			ctr_msg[p_supply->id]  = 0;
		}
		else
		{
			ctr_msg[p_supply->id]  = 0;
		}

		if (strlen(p_supply->goods_code) > 0)
		{
			count++;
			if(count >  10)
			{
				count = 0;
				strcpy(p_supply->goods_code,"");
			}
			ctr_msg[p_supply->id]  = ctr_msg[p_supply->id]  + 0x20;
		} 
		else
		{
			count = 0;
			ctr_msg[p_supply->id]  = ctr_msg[p_supply->id]  + 0x00;
		}

        // 两个设备ioset 一起设置
		ctr_msg[2] = ctr_msg[0] + ctr_msg[1];

		//ctr_msg[2] = 0x04;

		stSetCanFrame[6] = 0xFF;
		//stSetCanFrame[3] = 0xFF;
		//stSetCanFrame[4] =0x3f; //ctr_msg[2];
		stSetCanFrame[4] = ctr_msg[2];
        //stSetCanFrame[5] = ctr_msg[2];
		//stSetCanFrame[7] = 0x3f;
		//stSetCanFrame[5] = 0x3f;
		//stSetCanFrame[5] =0x0f; //ctr_msg[2];

		if(0)
		{
			// RPDO1
			stSendCanFrame.can_dlc = 2;
			stSendCanFrame.can_id = 0x200 + 0x0A;
			stSetCanFrame[0] = ctr_msg[2];
			stSetCanFrame[6] = 0;
			stSetCanFrame[4] = 0;

		}

		memcpy(&stSendCanFrame.data, &stSetCanFrame[0], 8);
		f = stSendCanFrame;

        if((last_ctr_msg != ctr_msg[2]) || ((p_supply->action_msg.run_time_ctrl % 5) == 0))
		{
			m_can_send_msg_queue.push(f);
			if(last_ctr_msg != ctr_msg[2])
			{
				SPDLOG_INFO("last_ctr_msg = {},ctr_msg[2] = {}",last_ctr_msg,ctr_msg[2]);
			}

		}
		

		last_ctr_msg = ctr_msg[2];
	}

    // 控制帧
    if((p_supply->action_msg.old_run_dir != p_supply->action_msg.run_dir) || ((p_supply->action_msg.run_time_ctrl % 20) == 0))
	{
		//m_can_send_msg_queue.push(f);

		if(p_supply->action_msg.old_run_dir != p_supply->action_msg.run_dir)
		{
			SPDLOG_INFO("change data, server cmd id = {},dir = {}",p_supply->id,p_supply->action_msg.run_dir);
		}
		
	}

    p_supply->action_msg.run_time_ctrl++;
	p_supply->action_msg.old_run_dir = p_supply->action_msg.run_dir;


return 0 ;
}
#endif

void sockaddr_to_ip(struct sockaddr *addr, char *ip)
{  

        struct sockaddr_in *ipv4 = (struct sockaddr_in *)addr;  

        ip = inet_ntoa(ipv4->sin_addr);  

}  
// can recv 线程
void plc_agent::package_supply_udp_send_msg(void)
{
	 can_frame cf;
	 can_frame cf1;
	/**
	 * 从配置文件读取参数
	 */
	m_feeder_info.para.belt_turn_speed = 100;
	m_feeder_info.para.belt_turn_time = 100;
	m_feeder_info.para.belt_turn_delay_time = 0;
	m_feeder_info.para.belt_acc = 100;
	//para.work_mode = WORK_NORMAL;

	servo_dev[0].set_id(7);
	servo_dev[1].set_id(8);

    // 配置帧
	for (auto &s : servo_dev)
	{
		s.set_acc(127, cf);
		SPDLOG_INFO( "servo.. set acc, id:{}", cf.can_id);
		for (int i = 0; i < cf.can_dlc; i++)
			SPDLOG_INFO("data[{}]:{:x}", i, cf.data[i]);
		m_can_send_msg_queue.push(cf);
	}

    // 发送canio 启动帧
    char stStartCanFrame[] = {0x01, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

	cf.can_id = 0x00;
	cf.can_dlc = 2;
	
	memcpy(&cf.data[0], &stStartCanFrame[0], 8);
	// canio 启动帧1
	m_can_send_msg_queue.push(cf);


    //  配置
	char stStartCanFrame2[] = {0x2F, 0x01, 0x20, 0x01, 0x00, 0x00, 0x00, 0x00};
	char stStartCanFrame3[] = {0x2F, 0x01, 0x20, 0x02, 0x00, 0x00, 0x00, 0x00};
    cf1.can_id = 0x060A;
	cf1.can_dlc = 8;
	memcpy(&cf1.data[0], &stStartCanFrame2[0], 8);
	// canio 配置1
	m_can_send_msg_queue.push(cf1);

	memcpy(&cf1.data[0], &stStartCanFrame3[0], 8);
	// canio 配置2
	m_can_send_msg_queue.push(cf1);

	// canio 启动帧2
	stStartCanFrame[1] = 0x0A;
	memcpy(&cf.data[0], &stStartCanFrame[0], 8);
	m_can_send_msg_queue.push(cf);
	

	char net_msg[13] = {0};
	can_frame can_frame_msg;

	struct sockaddr addr;
	char ip[32];
	std::string client_ip;
	struct sockaddr_in *p_addr = (struct sockaddr_in *)&client_udp_addr;
	unsigned short port = ntohs(p_addr->sin_port);
	struct sockaddr_in client_addr_config;

	sockaddr_to_ip(&client_udp_addr, ip);
	client_ip = ip;


	std::this_thread::sleep_for(std::chrono::milliseconds(8000));

	for (;;)
	{

		m_udp_server_socket.udp_socket_client_cfg(m_feeder_info.ip_info.client_udp_ip,m_feeder_info.ip_info.client_udp_port,client_addr_config);
		
		//if ((port == m_feeder_info.ip_info.client_udp_port) && (m_udp_server_fd > 0))
		if (m_udp_server_fd > 0)
		{
			if (!m_can_send_msg_queue.empty())
			{
				//SPDLOG_INFO("send udp client ip :{}, port {}",m_feeder_info.ip_info.client_udp_ip,m_feeder_info.ip_info.client_udp_port);
				m_can_send_msg_queue.timedPop(can_frame_msg, 1);
                // 增加数据格式转换
				can_to_net(&can_frame_msg, net_msg);
				sendto(m_udp_server_fd, net_msg, sizeof(net_msg), 0, (struct sockaddr *)&client_addr_config, sizeof(client_addr_config));
				for(auto &au:net_msg)
				{
					//SPDLOG_INFO("send udp client data :{:02x}",au);
				}
				#if 0
				SPDLOG_INFO("send udp client data :{:02x},{:02x},{:02x},{:02x},{:02x},{:02x},{:02x},{:02x},{:02x},{:02x},{:02x},{:02x},{:02x}",
				net_msg[0],
				net_msg[1],
				net_msg[2],
				net_msg[3],
				net_msg[4],
				net_msg[5],
				net_msg[6],
				net_msg[7],
				net_msg[8],
				net_msg[9],
				net_msg[10],
				net_msg[11],
				net_msg[12]
	
				);
                #endif 
				std::this_thread::sleep_for(std::chrono::milliseconds(5));
			}
			else
			{
				std::this_thread::sleep_for(std::chrono::milliseconds(50));
			}
		}
		else if(((port ) && (m_udp_server_fd > 0)))
		{
			//SPDLOG_INFO("send udp client data,the port err recv port {},config {} ",port,m_feeder_info.ip_info.client_udp_port);
		}
		
	}
}
// can recv 线程
#define PRE_UP_DATE 500
void plc_agent::package_supply_udp_recv_msg(void)
{
	msg_queue msg;

	static struct can_frame stRecvCanFrame = {0};
	fd_set stReadFds = {0};
	struct timeval stTimeOut = {0, 0};
	struct can_frame stSendCanFrame = {0};
	// supply_package st_supply;

	static uint32_t uSenSorFilerUp[4] = {0};
	static uint32_t uSenSorFilerDown[4] = {0};
	static uint32_t uKeyFilerDown[4] = {0};
	static uint32_t uEmcFilerDown = 0;
	static uint32_t uDateNum = 0;

	int iCanSocketFd0 = 0;
	int iCanSocketFd1 = 0;
	int iRet = -1;
	int iselect = 0;

	std::unordered_map<uint16_t, std::string> key_signal = {
		{0x0001,"sensor1"},{0x0002,"sensor2"},{0x0004,"sensor3"},{0x0008,"sensor4"},
		{0x0010,"cancel0"},{0x0020,"cancel1"},{0x0040,"reset0"},{0x0080,"reset1"},
		{0x0100,"req1"},{0x0200,"req2"},{0x0400,"req3"},{0x0800,"req4"},
		{0x100,"reserv1"},{0x2000,"reserv2"},{0x4000,"reserv3"},{0x8000,"reserv4"},
		
	};
	// CAN1 SEND TO CAN1
	stSendCanFrame.can_id = 0x600 + 0;
	stSendCanFrame.can_dlc = 8;
	stTimeOut.tv_usec = 50000;

	uint16_t uLastData = 0;
	uint16_t uLastData2 = 0;

	typedef struct can_data
	{
		char data[8];
	} can_device_t;

	int position[4] = {0};

	SPDLOG_INFO(" can recv thread run ");

	while(1)
	{

		if (m_can_data.empty())
		{
			std::this_thread::sleep_for(std::chrono::milliseconds(5));
			continue;
		}

		m_can_data.pop(stRecvCanFrame);


		//servo_dev[0].data_parse( stRecvCanFrame);
		//servo_dev[1].data_parse( stRecvCanFrame);

		if (stRecvCanFrame.can_id == 0x185)
		{
			supply_package *p_supply = &m_supply[0];

			belt *p_belt4 = &p_supply->st_belt[3];
			belt *p_belt3 = &p_supply->st_belt[2];
			belt *p_belt2 = &p_supply->st_belt[1];
			belt *p_belt1 = &p_supply->st_belt[0];

			uint16_t uData = 0;
			uint16_t uData_orign = 0;


			p_supply->dev_heartbeat = 1;

			memcpy(&uData, &stRecvCanFrame.data[0], 2);
			// 更新 传感器、 IO 状态 TODO
            uData_orign = uData;
			// sensor1 up
			if ((uData & 0x01) == 1)
			{
				uSenSorFilerUp[0]++;

				if (uSenSorFilerUp[0] > 3)
					m_supply[0].sort_info[0].sensor_state = 1;
			}
			else
			{
				uSenSorFilerUp[0] = 0;
			}
			// sensor2 up
			if (((uData >> 1) & 0x01))
			{
				uSenSorFilerUp[1]++;

				if (uSenSorFilerUp[1] > 2)
				{
					m_supply[1].sort_info[0].sensor_state = 1;
					SPDLOG_INFO("m_supply[1].sort_info[0].sensor_state = {}",m_supply[1].sort_info[0].sensor_state);


				}
					
			}
			else
			{
				uSenSorFilerUp[1] = 0;
			}
			// sensor3 up
			if (((uData >> 2) & 0x01))
			{
				uSenSorFilerUp[2]++;

				if (uSenSorFilerUp[2] > 2)
				{
					m_supply[1].sort_info[1].sensor_state = 1;
				}
			}
			else
			{
				uSenSorFilerUp[2] = 0;
			}

			// sensor 4
			if ((((uData >> 3)) & 0x01) == 1)
			{
				uSenSorFilerUp[3]++;

				if (uSenSorFilerUp[3] > 2)
					m_supply[0].sort_info[1].sensor_state = 1;
			}
			else
			{
				uSenSorFilerUp[3] = 0;
			}

			// sensor1 down
			if ((uData & 0x01) == 0)
			{
				uSenSorFilerDown[0]++;

				if (uSenSorFilerDown[0] > 3)
					m_supply[0].sort_info[0].sensor_state = 0;
			}
			else
			{
				uSenSorFilerDown[0] = 0;
			}
			// sensor2 down
			if (((uData >> 1) & 0x01) == 0)
			{
				uSenSorFilerDown[1]++;

				if (uSenSorFilerDown[1] > 2)
					m_supply[1].sort_info[0].sensor_state = 0;
			}
			else
			{
				uSenSorFilerDown[1] = 0;
			}
			// sensor3 down
			if (((uData >> 2) & 0x01) == 0)
			{
				uSenSorFilerDown[2]++;

				if (uSenSorFilerDown[2] > 2)
					m_supply[1].sort_info[1].sensor_state = 0;
			}
			else
			{
				uSenSorFilerDown[2] = 0;
			}
			if ((((uData >> 3)) & 0x01) == 0)
			{
				uSenSorFilerDown[3]++;

				if (uSenSorFilerDown[3] > 3)
					m_supply[0].sort_info[1].sensor_state = 0;
			}
			else
			{
				uSenSorFilerDown[3] = 0;
			}

			// p_belt2->sensor_up = (uData& 0x01);
			// p_belt3->sensor_up = ((uData >> 1) & 0x01);
			// p_belt4->sensor_up = ((uData >> 2) & 0x01);
			//  急停有效 0 ，无效为1

            m_supply[0].st_key.reverse = ((uData >> 4) & 0x01)  + ((uData >> 13) & 0x01) ;

			m_supply[0].sort_info[0].ready_state = ((uData >> 8) & 0x01);
			m_supply[1].sort_info[0].ready_state = ((uData >> 9) & 0x01);
			m_supply[1].sort_info[1].ready_state = ((uData >> 10) & 0x01);
			m_supply[0].sort_info[1].ready_state = ((uData >> 11) & 0x01);

			// 区分自动供件和人为供件
			if(m_feeder_info.auto_feeder == 0)
			{
				m_supply[1].st_key.reverse = ((uData >> 5) & 0x01);
			}
			else
			{
				m_supply[1].st_key.reverse = ((uData >> 4) & 0x01) +  ((uData >> 12) & 0x01) ;
			}

			if(m_supply[0].st_key.reverse > 0)
			{
				m_supply[0].st_key.reverse = 1;
				SPDLOG_INFO("feeder 0 cancel ");
			}

			if(m_supply[1].st_key.reverse > 0)
			{
				m_supply[1].st_key.reverse = 1;
				SPDLOG_INFO("feeder 1 cancel ");
			}


			m_supply[0].st_key.recover = ((uData >> 6) & 0x01);
			m_supply[1].st_key.recover = ((uData >> 7) & 0x01);


			// log 变化打印
			if (uLastData == uData_orign)
			{
				uDateNum++;
			}
			else
			{
				uDateNum = 0;
			}

			if ((uLastData != uData_orign) || ((uDateNum % PRE_UP_DATE) == (PRE_UP_DATE - 1)))
			// if ((uLastData != uData) )
			{
				// 上报按键状态
				key_info_push(p_supply);

				if (uLastData != uData_orign)
					SPDLOG_INFO(" can recv can id hex:0x:{:0x},data:{:0x},uLastData {:0x},uData_orign,{:0x} ", stRecvCanFrame.can_id, uData,uLastData,uData_orign);
			}

			for (int i = 0; i < 2; i++)
			{
				static uint8_t flag[2] = {0};
				if (m_supply[i].st_key.reverse == 1 && (flag[i] == 0))
				{
					flag[i] = 1;
					// 回退，强制cmd 是 stop
					// p_supply->st_cmd.cmd = STOP;
					m_supply[i].m_state = e_wkstate_STOP;
					m_supply[i].st_key.reverse = true;
					//supply_canio_set(5, 1, 13 + i );
					SPDLOG_INFO("the key cancel key id {}",i);

					// 速度下发0
				}
				else if (m_supply[i].st_key.reverse == 0 && (flag[i] == 1)) // if(p_supply->st_key.stop || p_supply->st_key.start || p_supply->st_key.recover)
				{
					flag[i] = 0;
					// p_supply->st_key.reverse = false;
					m_supply[i].m_state = e_wkstate_AUTO_RUNNING;
					//supply_canio_set(5, 0, 13 + i);
				}
			}

			uLastData = uData_orign;
		}
		else if (stRecvCanFrame.can_id == 0x18A)
		{
			supply_package *p_supply = &m_supply[0];

			belt *p_belt4 = &p_supply->st_belt[3];
			belt *p_belt3 = &p_supply->st_belt[2];
			belt *p_belt2 = &p_supply->st_belt[1];
			belt *p_belt1 = &p_supply->st_belt[0];

			uint16_t uData = 0;
			uint16_t uData_orign = 0;

			//p_supply->dev_heartbeat = 1;
			memcpy(&uData, &stRecvCanFrame.data[0], 2);
			// 更新 传感器、 IO 状态 TODO
            uData_orign = uData;
			if (uLastData2 != uData_orign)
				SPDLOG_INFO(" can recv can id hex:0x:{:0x},data:{:0x},uLastData2 {:0x},uData_orign,{:0x} ", stRecvCanFrame.can_id, uData,uLastData2,uData_orign);

			uLastData2 = uData_orign;

		}

		else if (stRecvCanFrame.can_id == 0x008)
		{
			supply_package *p_supply = &m_supply[0];

			belt *p_belt4 = &p_supply->st_belt[3];
			belt *p_belt3 = &p_supply->st_belt[2];
			belt *p_belt2 = &p_supply->st_belt[1];
			belt *p_belt1 = &p_supply->st_belt[0];
            // todo
			if (stRecvCanFrame.data[2] == 0x07 || stRecvCanFrame.data[2] == 0x08)
			{
				servo_dev[stRecvCanFrame.data[2]].data_parse(stRecvCanFrame);
			}
			else
			{
				//m_can_recv_msg_queue[i].push(stRecvCanFrame);
			}

		}

		bzero(&stRecvCanFrame, sizeof(stRecvCanFrame));
		// std::this_thread::sleep_for(10);
	}
}

uart_attr_t stUartAttr =
	{
		.cDateBit = DATA_BIT_8,
		.cParity = PARITY_NONE,
		.cStopBit = STOP_BIT_1,
		.iBaud = 9600,
};
// uart recv 线程
#define UART_PATH_DEF / dev / ttysWK0
void plc_agent::package_supply_uart_recv(void)
{
	string UartDev1 = m_feeder_info.scan_dev1_addr;
	string UartDev2 = m_feeder_info.scan_dev2_addr;

	char *pUartDev =  (char*)"/dev/ttysWK0";
	char *pUartDev1 = (char*)"/dev/ttysWK1";

	pUartDev = (char *)UartDev1.c_str();
	pUartDev1 = (char *)UartDev2.c_str();

	SPDLOG_INFO("uart dev 1 {},dev 2{}",UartDev1,UartDev2);

	char cSendBuf[2] = {0xAA, 0x55};
	char cRecvBuf[256] = {0x00, 0x00};
	string cRecvData = "hello";
	int iFd = 0;
	int iFd1 = 0;
	int imaxfd = 0; /* 定义最大句柄 */
	int iRet = 0;
	int iRecv = 0;
	int iRecvSuccessTimes = 0;

	fd_set stReadFds = {0};
	struct timeval stTimeOut = {0, 0};
	// todo 以后做成配置文件 或写在脚本里
	system("echo jd@ugv | sudo -S chmod 777 /dev/ttysWK0");
	system("echo jd@ugv | sudo -S chmod 777 /dev/ttysWK1");

	stUartAttr.pUartName = pUartDev;
	iFd = init_uart(&stUartAttr);

	stUartAttr.pUartName = pUartDev1;
	iFd1 = init_uart(&stUartAttr);

	if (iFd < 0)
	{
		SPDLOG_INFO("init uart failed!");
		return;
	}
	SPDLOG_INFO("uart recv thread run");

	if (iFd > imaxfd)
	{
		imaxfd = iFd;
	}
	if (iFd1 > imaxfd)
	{
		imaxfd = iFd1;
	}

	while (1)
	{
		FD_ZERO(&stReadFds);
		// 增加监控
		FD_SET(iFd, &stReadFds);
		FD_SET(iFd1, &stReadFds);
		// stTimeOut.tv_usec = 500 00;
		// send_uart(iFd, cSendBuf, sizeof(cSendBuf));
#if 0
		// 更新vector

#endif
		iRet = select(imaxfd + 1, &stReadFds, NULL, NULL, &stTimeOut);

		if (iRet > 0)
		{
			// uart1
			if (FD_ISSET(iFd, &stReadFds))
			{
				supply_package *p_supply = &m_supply[0];
				belt *p_belt4 = &p_supply->st_belt[3];
				belt *p_belt3 = &p_supply->st_belt[2];
				belt *p_belt2 = &p_supply->st_belt[1];
				belt *p_belt1 = &p_supply->st_belt[0];

                // 扫码赋值定位
				if(m_feeder_info.belt_count == BELT3)
				{
					p_belt1 = p_belt2;
					SPDLOG_INFO("belt num 3,the uart code p_belt1 -> p_belt2");

				}
				else if(m_feeder_info.belt_count <= BELT2)
				{
					p_belt1 = p_belt3;
					SPDLOG_INFO("belt num 2,the uart code p_belt1 -> p_belt3");
				}
		
				iRecv = recv_uart(iFd, cRecvBuf, sizeof(cRecvBuf));
				#if 0
				std::string str = cRecvBuf;
                auto pos = str.find("\n")

				if(pos != std::string::npos)
				{
					cRecvBuf[pos - 1] = '\0';
				}
                #endif

				if(m_feeder_info.auto_feeder == 1)
				{
					goods_taskid_msg goods_msg;

					goods_msg.task_id = 0;
					strcpy(m_supply[0].goods_code, cRecvBuf);

					goods_msg.str = cRecvBuf;
					m_supply[0].m_goods_msg_queue.push(goods_msg);
					SPDLOG_INFO("auto feeder0 recv string data: {}",m_supply[0].goods_code);
				}
				else
				{

					if ((iRecv < sizeof(p_belt1->goods_code)) && (iRecv != 0))
					{
						std::chrono::high_resolution_clock::time_point start_time;
						static std::chrono::high_resolution_clock::time_point last_time;

						start_time = std::chrono::high_resolution_clock::now();
						// SPDLOG_INFO("start_time {}",start_time);
						std::chrono::milliseconds interval = std::chrono::duration_cast<std::chrono::milliseconds>(start_time - last_time);

						SPDLOG_INFO("距离上次的时间间隔,blet count = {},interval={}", m_feeder_info.belt_count, interval.count());

						// test
						uint32_t count = 0;

						if ((m_feeder_info.belt_count >= BELT2))
						{
							count = 3000;
						}
						else
						{
							count = 0;
						}
						if ((interval.count() + count) > 1600)
						{
							{
								std::lock_guard<std::mutex> lck(scan_lock);

								strcpy(p_belt1->goods_code, cRecvBuf);
								// 手动扫码成功，认为有货
								p_belt1->goods = 1;
							}
							p_belt1->sensor_cnt++;
							SPDLOG_INFO("id : {},the uart data len:{},code:{},num ={}", p_supply->id, iRecv, p_belt1->goods_code, p_belt1->sensor_cnt);
							SPDLOG_INFO("id :{},belt1: goods code \n1:{}\n2:{}\n3:{}\n4:{}",
										p_supply->id, p_belt1->goods_code, p_belt2->goods_code, p_belt3->goods_code, p_belt4->goods_code);
						}
						else
						{
							SPDLOG_INFO("短时间内接收到2个sku码,舍弃1个,interval={}", interval.count());
							SPDLOG_INFO("id : {},the uart data len:{},code:{},num ={}", p_supply->id, iRecv, p_belt1->goods_code, p_belt1->sensor_cnt);
						}
						last_time = start_time;
					}
				}

				bzero(cRecvBuf, sizeof(cRecvBuf));
			}
            // uart2
			if (FD_ISSET(iFd1, &stReadFds))
			{
				supply_package *p_supply = &m_supply[1];

				belt *p_belt4 = &p_supply->st_belt[3];
				belt *p_belt3 = &p_supply->st_belt[2];
				belt *p_belt2 = &p_supply->st_belt[1];
				belt *p_belt1 = &p_supply->st_belt[0];

				// 扫码赋值定位
				if(m_feeder_info.belt_count == BELT3)
				{
					p_belt1 = p_belt2;
					SPDLOG_INFO("belt num 3,the uart code p_belt1 -> p_belt2");

				}
				else if(m_feeder_info.belt_count <= BELT2)
				{
					p_belt1 = p_belt3;
					SPDLOG_INFO("belt num 2,the uart code p_belt1 -> p_belt3");
				}
				else if(m_feeder_info.belt_count == BELT1)
				{
					p_belt1 =  p_belt3;
					SPDLOG_INFO("belt num 1,the uart code p_belt1 -> p_belt3");
				}

                p_belt1->scan_id = 2;

				iRecv = recv_uart(iFd1, cRecvBuf, sizeof(cRecvBuf));

				if(m_feeder_info.auto_feeder == 1)
				{
					goods_taskid_msg goods_msg;

					goods_msg.task_id = 0;
					strcpy(m_supply[1].goods_code, cRecvBuf);

					goods_msg.str = cRecvBuf;
					m_supply[1].m_goods_msg_queue.push(goods_msg);
					SPDLOG_INFO("auto feeder1 recv string data: {}",m_supply[1].goods_code);
				}
				else
				{
					if ((iRecv < sizeof(p_belt1->goods_code)) && (iRecv != 0))
					{
						{
							std::lock_guard<std::mutex> lck(scan_lock);
							strcpy(p_belt1->goods_code, cRecvBuf);
							// 手动扫码成功，认为有货
							p_belt1->goods = 1;
						}
						p_belt1->sensor_cnt++;
						SPDLOG_INFO("id : {},the uart data len:{},num ={},code:{}", p_supply->id, iRecv, p_belt1->sensor_cnt, p_belt1->goods_code);
						SPDLOG_INFO("id :{},belt1: goods code \n1:{}\n2:{}\n3:{}\n4:{}",
									p_supply->id, p_belt1->goods_code, p_belt2->goods_code, p_belt3->goods_code, p_belt4->goods_code);
					}
				}

				bzero(cRecvBuf, sizeof(cRecvBuf));
			}
		}

		std::this_thread::sleep_for(std::chrono::milliseconds(50));
	}
}

std::string plc_agent::replenish_num_with_zero(int num, int sum_length)
{
	std::string str_temp = std::to_string(num);
	std::string str_result = std::string(sum_length - str_temp.length(), '0') + str_temp;

	return str_result;
}

std::string plc_agent::creat_task_id()
{
	std::string id_temp;
	// 单线程使用，调用者共享 递增
	static uint32_t s_count = 0;
	int a_count = 0;

	//struct tm *tp;
	//time_t t = time(NULL);
	//tp = localtime(&t);

	// 获取当前时间点  
    auto now = std::chrono::system_clock::now();  
   
    // 转换为time_t，以便使用ctime库  
    std::time_t now_time_t = std::chrono::system_clock::to_time_t(now);  
    // 转换为tm结构体  
    std::tm* tp = std::localtime(&now_time_t); 

	//id_temp = GOODS_TASK_ID_HEAD;

	//id_temp += replenish_num_with_zero(tp->tm_year + 1900, 4);
	//id_temp += replenish_num_with_zero(tp->tm_mon + 1, 2);
	//id_temp += replenish_num_with_zero(tp->tm_mday, 2);

	// 转换成12小时制

	s_count++;
	if (tp->tm_hour > 13)
	{
		// 生成偶数
		a_count = (s_count % 10) * 2 ;
		// 取消转换12小时制，
		tp->tm_hour = tp->tm_hour - 12;
	}
	else
	{
		// 生成奇数
		a_count = (s_count % 10) * 2  + 1;
	}

	if (tp->tm_hour < 1)
	{
		tp->tm_hour = 13;
	}

	if(tp->tm_hour < 10)
	{
		if(tp->tm_hour > 0)
		id_temp += replenish_num_with_zero(tp->tm_hour, 1);
	} 
	else
	{
		id_temp += replenish_num_with_zero(tp->tm_hour, 2);
	}
	
	id_temp += replenish_num_with_zero(tp->tm_min, 2);
	id_temp += replenish_num_with_zero(tp->tm_sec, 2);

	//auto now = std::chrono::system_clock::now();
	uint64_t dis_millseconds = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count() - std::chrono::duration_cast<std::chrono::seconds>(now.time_since_epoch()).count() * 1000; // 增加毫秒
	id_temp += replenish_num_with_zero((int)dis_millseconds, 3);

	//id_temp += replenish_num_with_zero(creat_rand_num(0, 9), 1);

	id_temp += replenish_num_with_zero(a_count % 10, 1);

	return id_temp;
}
// 商品码发布
void plc_agent::goods_info_push(supply_package *p_supply, char *pCode, uint8_t uid, uint8_t upos,uint32_t task_id)
{
	std::string taskid;
	sorting_task_msg goods_info_temp;
	msg_queue goods_msg;

	goods_info_temp.containers_count = 2;
   
	goods_info_temp.sequence++;
	goods_info_temp.container = 0;
	goods_info_temp.dev_id = p_supply->id;

	strcpy(goods_info_temp.gd_codes, pCode);

	// 2.重量
	goods_info_temp.weight = p_supply->st_belt[2].weight;
    // 3.体积
	goods_info_temp.has_vol = 1;
	goods_info_temp.vol = p_supply->st_belt[2].vol;
	// goods_info_temp.codes[] = 0x00;
	if(task_id ==0)
	{
		taskid = creat_task_id();
	}
	else
	{
		taskid = std::to_string(task_id);
	}
	//taskid = creat_task_id();
	strcpy((char *)goods_info_temp.task_id, (const char *)taskid.c_str());
    
	//if(m_feeder_info.double_feeder_scan == 2)
	if(1)
	{
		// 赋值第4段
	    //p_supply->st_belt[3].task_msg = goods_info_temp;
		p_supply->m_sort_req_task = goods_info_temp;
	}
	else
	{
		// 赋值第3段
	    p_supply->st_belt[2].task_msg = goods_info_temp;
	}

	// 消息发布队列
	goods_msg.type = PLC_GOODS_INS_PUB;
	// 供包机id
	goods_msg.dev_id = p_supply->id;
	goods_info_temp.dev_id = p_supply->id;
	p_supply->m_sort_req_task = goods_info_temp;
	m_scheduler_msg.scheduler_manager_set_req_task_queue( p_supply->id,(char*)p_supply->m_sort_req_task.task_id);
	memcpy(goods_msg.msg_data, &goods_info_temp, sizeof(goods_info_temp));
	m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(goods_msg);

	SPDLOG_INFO("######id :{}, req task info :\n###### tsk:{} \n###### cnt:{} \n###### gds:{} \n###### wht:{} \n###### vol:{}x{}x{}",\
	 p_supply->id, \
	 goods_info_temp.task_id, \
	 goods_info_temp.container,\
	 goods_info_temp.gd_codes,\
	 goods_info_temp.weight,\
	 goods_info_temp.vol.length,goods_info_temp.vol.width,goods_info_temp.vol.height);
	return;
}
// 播种信息发布
void plc_agent::sort_info_push(supply_package *p_supply)
{

	std::string taskid;
	sorting_task_msg goods_info_temp;
	msg_queue goods_msg;

	sorting_task_msg *pMsg = &p_supply->st_belt[3].task_msg;
	pMsg->dev_id = p_supply->id;

	if (p_supply->st_belt[3].pub_enable == 0)
	{
		return;
	}

	// if(pMsg->id <= 2 || pMsg->id > 1)
	{
		std::lock_guard<std::mutex> lck(m_scheduler_msg.m_sort_action_lock);
		memcpy(&(m_scheduler_msg.m_sort_action_recv[pMsg->dev_id]), pMsg, sizeof(sorting_task_msg));
	}

	SPDLOG_INFO("====== belt 4 push sort info:{}, {},{},{}", pMsg->dev_id, pMsg->gd_codes, pMsg->task_id, pMsg->container);
	// SPDLOG_INFO("belt 4 SORT: task id {}",pMsg->task_id);
	// SPDLOG_INFO("belt 4 SORT: container id {}",pMsg->container);

	return;
}

// 按键信息发布
void plc_agent::key_info_push(supply_package *p_supply)
{
#define FEEDER_TASK_CANCELED 2

	key_event button_event;
	msg_queue button_msg;
	event_exception plc_dev_excep;
	uint8_t key_poll = 0;
	supply_package *pst_supply = p_supply;
	key *pst_key = &p_supply->st_key;

	uint8_t &key_poll_old = pst_key->key_poll_old;
	uint32_t &key_id_old = pst_key->key_id_old;
	key_evt_type &evt_type_old = pst_key->evt_type_old;

	static uint32_t pre_poll_num[2] ={0}; 

	belt *p_belt4 = &pst_supply->st_belt[3];
	belt *p_belt3 = &pst_supply->st_belt[2];
	belt *p_belt2 = &pst_supply->st_belt[1];
	belt *p_belt1 = &pst_supply->st_belt[0];
	// 按键id
	button_event.dev_id = p_supply->id;
	if (pst_key->stop)
	{
		key_poll = 1;
		button_event.key_id = key_id_KEY_STOP;
		button_event.evt_type = key_evt_type_KEY_PRESSED;
	}
	else if (pst_key->emergency)
	{
		key_poll = 1;
		button_event.key_id = key_id_KEY_EMERG;
		button_event.evt_type = key_evt_type_KEY_DOWN;
		
	}
	else if (pst_key->start)
	{
		key_poll = 1;
		button_event.key_id = key_id_KEY_START;
		button_event.evt_type = key_evt_type_KEY_PRESSED;
	}
	else if (pst_key->recover)
	{
		key_poll = 1;
		button_event.key_id = key_id_KEY_RESET;
		button_event.evt_type = key_evt_type_KEY_PRESSED;
	}
	else if (pst_key->pause)
	{
		key_poll = 1;
		button_event.key_id = key_id_KEY_PAUSE;
		button_event.evt_type = key_evt_type_KEY_PRESSED;
	}
	else if (pst_key->reverse)
	{
		key_poll = 1;
		button_event.key_id = key_id_KEY_RESERVE;
		button_event.evt_type = key_evt_type_KEY_PRESSED;
	}
	else
	{
		if ((pst_key->emergency == 0) && (evt_type_old == key_evt_type_KEY_DOWN))
		{
			button_event.key_id = key_id_KEY_EMERG;
			button_event.evt_type = key_evt_type_KEY_UP;

			SPDLOG_INFO("WANRING :the emc key up ");
		}
		else
		{
			button_event.key_id = key_id_old;
			button_event.evt_type = key_evt_type_KEY_UP;
		}
		key_poll = 0;
		// button_event.key_id = key_id_KEY_RESERVE;
		// button_event.evt_type = key_evt_type_KEY_PRESSED;
	}
    
	pre_poll_num[p_supply->id]++;
	if ((key_poll_old != key_poll) || ((pre_poll_num[p_supply->id] % 1) == 0))
	{
		// 上包key info
		button_msg.type = PLC_BUTTON_STATE_PUB;
		if(key_poll_old != key_poll)
		{
			SPDLOG_INFO("WARNING:id :{},KEY PRESS,push key info,  key_id[0:reserve,1:start,2:stop,5:emc] :{},type {}",p_supply->id, button_event.key_id, button_event.evt_type);

		}
				
		memcpy(button_msg.msg_data, &button_event, sizeof(button_event));
		m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(button_msg);

		if (button_event.key_id == key_id_KEY_RESERVE)
		{
#if 0
			msg_queue excep_msg;
			plc_dev_excep.which_evt_except = event_exception_evt_tag;
            plc_dev_excep.evt_except.evt.code = FEEDER_TASK_CANCELED;
            plc_dev_excep.evt_except.evt.src = exception_src_FEEDER;
            plc_dev_excep.evt_except.evt.dev = 0;
			// 异常信息
            excep_msg.type = PLC_EXCEP_PUB;
			memcpy(excep_msg.msg_data, &plc_dev_excep, sizeof(plc_dev_excep));
			m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(excep_msg);
#endif
			// 2.第3段若发布商品码，则供包台取消次商品码对应的任务，若无发布清除码值，停止发布

			// 3.请求任务取消，等待应答
		}
	}
	key_id_old = button_event.key_id;
	evt_type_old = button_event.evt_type;
	key_poll_old = key_poll;

	return;
}

void plc_agent::feeder_state_info_push(supply_package *p_supply)
{
	event_exception feeder_exception;
	feeder_dev_state_total feeder_state;
	// scanner_state feeder_state;
	msg_queue feeder_state_msg;

	belt *p_belt4 = &p_supply->st_belt[3];
	belt *p_belt3 = &p_supply->st_belt[2];
	belt *p_belt2 = &p_supply->st_belt[1];
	belt *p_belt1 = &p_supply->st_belt[0];

	// test
	// feeder_state.feeder_id = 0x02;
	memset(&feeder_state, 0, sizeof(feeder_dev_state_total));
	feeder_state.dev_id = p_supply->id;
#if 1
	feeder_state.has_belt_motor = true;
	feeder_state.has_charger = false;
	feeder_state.has_auto_scanner = true;
	feeder_state.auto_scanner.state = feeder_dev_state_DEV_STATE_NORMAL;
	feeder_state.auto_scanner.wk_state = feeder_dev_work_state_DEV_WORK_STATE_SHIELD;

	feeder_state.has_manual_scanner = false;
	feeder_state.manual_scanner.state = feeder_dev_state_DEV_STATE_NORMAL;
	feeder_state.manual_scanner.wk_state = feeder_dev_work_state_DEV_WORK_STATE_SHIELD;
	feeder_state.belt_motor.motor_count = 4;
	feeder_state.belt_motor.motor[0].dev_id = feeder_belt_motor_id_BELT_MOTOR_1;
	feeder_state.belt_motor.motor[0].state = feeder_dev_state_DEV_STATE_UNKNOWN;
	feeder_state.belt_motor.motor[0].wk_state = feeder_dev_work_state_DEV_WORK_STATE_SHIELD;
	feeder_state.belt_motor.motor[0].state_code = p_belt1->servo_state;
	feeder_state.belt_motor.motor[0].speed = p_belt1->belt_spd;

	feeder_state.belt_motor.motor[1].dev_id = feeder_belt_motor_id_BELT_MOTOR_2;
	feeder_state.belt_motor.motor[1].state = feeder_dev_state_DEV_STATE_NORMAL;
	feeder_state.belt_motor.motor[1].wk_state = feeder_dev_work_state_DEV_WORK_STATE_SHIELD;
	feeder_state.belt_motor.motor[1].state_code = p_belt2->servo_state;
	feeder_state.belt_motor.motor[2].speed = p_belt2->belt_spd;

	feeder_state.belt_motor.motor[2].dev_id = feeder_belt_motor_id_BELT_MOTOR_3;
	feeder_state.belt_motor.motor[2].state = feeder_dev_state_DEV_STATE_NORMAL;
	feeder_state.belt_motor.motor[2].wk_state = feeder_dev_work_state_DEV_WORK_STATE_SHIELD;
	feeder_state.belt_motor.motor[2].state_code = p_belt3->servo_state;
	feeder_state.belt_motor.motor[2].speed = p_belt3->belt_spd;

	feeder_state.belt_motor.motor[3].dev_id = feeder_belt_motor_id_BELT_MOTOR_4;
	feeder_state.belt_motor.motor[3].state = feeder_dev_state_DEV_STATE_NORMAL;
	feeder_state.belt_motor.motor[3].wk_state = feeder_dev_work_state_DEV_WORK_STATE_SHIELD;
	feeder_state.belt_motor.motor[3].state_code = p_belt4->servo_state;
	feeder_state.belt_motor.motor[3].speed = p_belt4->belt_spd;

	// 货物传感器检测
	feeder_state.has_belt_sensor = true;
	feeder_state.belt_sensor.sensor_count = 4;
	feeder_state.belt_sensor.sensor[3].dev_id = feeder_belt_sensor_id_SENSOR_4;
	feeder_state.belt_sensor.sensor[3].state = (p_belt4->sensor_up == true ? feeder_sensor_trigger_state_ON : feeder_sensor_trigger_state_OFF);

	feeder_state.belt_sensor.sensor[2].dev_id = feeder_belt_sensor_id_SENSOR_3;
	feeder_state.belt_sensor.sensor[2].state = (p_belt3->sensor_up == true ? feeder_sensor_trigger_state_ON : feeder_sensor_trigger_state_OFF);

	feeder_state.belt_sensor.sensor[1].dev_id = feeder_belt_sensor_id_SENSOR_2;
	feeder_state.belt_sensor.sensor[1].state = (feeder_sensor_trigger_state)feeder_dev_state_DEV_STATE_UNKNOWN;
	feeder_state.belt_sensor.sensor[0].dev_id = feeder_belt_sensor_id_SENSOR_1;
	feeder_state.belt_sensor.sensor[0].state = (feeder_sensor_trigger_state)feeder_dev_state_DEV_STATE_UNKNOWN;
	// 扫码设备状态
	if (m_auto_scan_state == 1)
	{
		feeder_state.auto_scanner.state = feeder_dev_state_DEV_STATE_UNKNOWN;
	}
	else if (m_auto_scan_state == 2)
	{
		feeder_state.auto_scanner.state = feeder_dev_state_DEV_STATE_NORMAL;
	}
	else if (m_auto_scan_state == 3)
	{
		feeder_state.auto_scanner.state = feeder_dev_state_DEV_STATE_ERR;
	}

	feeder_state.auto_scanner.wk_state = feeder_dev_work_state_DEV_WORK_STATE_POWER_UP;
	feeder_state.auto_scanner.feeder_id = 2;

	feeder_state.feeder_id = p_supply->id;

#endif

	if ((p_belt4->belt_state == true) && (p_supply->warning == 0))
	{
		if (p_supply->st_cmd.cmd == START)
		{
			feeder_state.state = fsm_state_RUNNING;
		}
		else
		{
			feeder_state.state = fsm_state_IDLE;
		}
	}
	else if (p_supply->warning != 0 || p_belt1->servo_state > 0 || p_belt2->servo_state > 0 || p_belt3->servo_state > 0 || p_belt4->servo_state > 0) 
	{
		if((p_belt1->servo_state != 2) && (p_belt2->servo_state != 2)&& (p_belt3->servo_state != 2)&& (p_belt4->servo_state != 2))
		{
			feeder_state.state = fsm_state_ERR;
			if(p_supply->warning == 0 )
			{
				feeder_state.err_code = 0xff;
			}
			else
			{
				feeder_state.err_code = p_supply->warning ;
			}
			
			//p_supply->fault = true;
		}
	}
	else if ((p_belt4->belt_state == false) && (p_supply->warning != 0))
	{
		feeder_state.state = fsm_state_INIT;
	}
    // 优化：增加 第4段sensor up信号或 有货标志，
	feeder_state.has_goods = (((p_belt4->sensor_up) && (p_belt3->belt_spd > 0))  || p_belt4->goods ) + ( p_belt3->goods || strlen(p_belt3->goods_code) > 0 );
	
	//feeder_state.has_goods = p_belt4->goods ;
	memcpy(feeder_state_msg.msg_data, &feeder_state, sizeof(feeder_state));

	static uint16_t count[2] = {0};
    static uint16_t has_goods_old[2] = {0};
	if (count[p_supply->id]++ > 10 || (has_goods_old[p_supply->id] != feeder_state.has_goods))
	{
		count[p_supply->id] = 0;
		// push back queue
		feeder_state_msg.type = PLC_FEEDER_STATE_PUB;
		m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(feeder_state_msg);

		// SPDLOG_INFO("push feeder state info,  id {},sizeof {}",feeder_state.dev_id, sizeof(feeder_state) );
		// SPDLOG_INFO("push feeder state info,  id {},sizeof {}",feeder_state.feeder_id, sizeof(feeder_state) );
	}
 
    has_goods_old[p_supply->id] = feeder_state.has_goods;

    //=======异常上报
	msg_queue excep_msg;

	static uint8_t excep_state_last[2] = {1,1};

	//if((p_supply->warning  >= 0) && (count[p_supply->id] == 10))
	if(true)
	{
		static uint32_t warning_last[2] = {0};
		#if 0
		if(p_supply->warning == 0xff)
		{
			feeder_exception.evt_except.except.level = exception_level_WARNNING;
			feeder_exception.which_evt_except = event_exception_evt_tag;//event_exception_evt_tag;

		}
		#endif
		if(p_supply->warning > 0)
		{
			
			warning_last[p_supply->id] = p_supply->warning;
			feeder_exception.evt_except.except.level = exception_level_WARNNING;
			feeder_exception.which_evt_except = event_exception_except_tag;//event_exception_evt_tag;

		}
		// 物控字典 7500 + warning
		feeder_exception.evt_except.except.code =7500 +  p_supply->warning;
		feeder_exception.evt_except.except.sub_code = 0;
		
		feeder_exception.evt_except.except.src = exception_src_FEEDER;
		feeder_exception.evt_except.except.dev = p_supply->id;
		feeder_exception.evt_except.except.state = exception_state_STATE_OCCURED;
        // 置恢复，即异常
		if(p_supply->warning == 0)
		{
			feeder_exception.evt_except.except.state = exception_state_STATE_RESET;
			feeder_exception.evt_except.except.code =7500 +  warning_last[p_supply->id];
		    feeder_exception.evt_except.except.sub_code = 0;
			
			feeder_exception.evt_except.except.level = exception_level_WARNNING;
			feeder_exception.which_evt_except = event_exception_except_tag;//event_exception_evt_tag;

			warning_last[p_supply->id] = 0;
		}
		// 异常上报 状态变化上报
		if(excep_state_last[p_supply->id] != feeder_exception.evt_except.except.state)
		{
			// 异常信息
			excep_msg.type = PLC_EXCEP_PUB;
			memcpy(excep_msg.msg_data, &feeder_exception, sizeof(feeder_exception));
			m_scheduler_msg.scheduler_manager_scheduler_msg_queue_push(excep_msg);
		}

		excep_state_last[p_supply->id] = feeder_exception.evt_except.except.state;
	}

	return;
}
