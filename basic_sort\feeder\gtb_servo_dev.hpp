﻿#ifndef _GTB_SERVO_DEV_H_
#define _GTB_SERVO_DEV_H_

//#include "../dev/modbus.hpp"
#include "linux/can.h"
#include <cstdint>

#define CAN_ID 8

#define START_FRAME 0XFA
#define QUERY_FRAME 0XFC
#define RECV_START_FRAME 0XEC
#define SET_PARA_FRAME 0XA0
#define QUERY_PARA_FRAME 0XA1

#define FORWARD_TURN_TIME_MODE 0b00000000
#define FORWARD_TURN_POS_MODE  0b01000000
#define RESERVE_TURN_TIME_MODE 0b10000000
#define RESERVE_TURN_POS_MODE  0b11000000

#define SET_DEV_ADDRESS 1
#define SET_ACC_PARA    2
#define SET_BAUDRATE    3

#define SENSOR_TRIGGER      1
#define SENSOR_NO_TRIGGER   0

class gtb_servo_dev
{
private:
    int dev_id;
    int state = 0;
    int error_code = 0;
    int position = 0;
    int sensor_state = SENSOR_NO_TRIGGER;
    int address = 0;
    int acc = 0;
    int baud = 0;

    void servo_ctrl(int mode, int speed, int para_1, int para_2, can_frame &f);
    void para_set(int type, int para, can_frame &f);
    void para_query(int type, can_frame &f);

    uint16_t calc_crc16_modbus(uint8_t *data, int length);

public:

    void forward_turn_position_ctrl(int speed, int pos, int delay_time, can_frame &f);
    void reserve_turn_position_ctrl(int speed, int pos, int delay_time, can_frame &f);
    void forward_turn_time_ctrl(int speed, int time, int delay_time, can_frame &f);
    void reserve_turn_time_ctrl(int speed, int time, int delay_time, can_frame &f);
    void set_acc(int acc, can_frame &f);
    void query_acc(can_frame &f);

    void query(can_frame &f);

    int data_parse(can_frame f);
    int get_id();
    void set_id(int id);
    int get_state();
    int get_position();
    int get_error_code();
    int get_sensor_state();
    int get_address();
    int get_acc();
    int get_baudrate();
    void clear_sensor_state();
};



#endif

