#include <thread>         // std::thread, std::thread::id, std::this_thread::get_id
#include <chrono>         // std::chrono::seconds
#include <iostream>

#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>
#include "spdlog/async.h"
#include "spdlog/sinks/basic_file_sink.h"
#include "spdlog/sinks/rotating_file_sink.h"

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>
#include <spdlog/spdlog.h>

#include "share/global_def.h"
#include "share/callstack/callstack_wrapper.hpp"

#include "containers.hpp"
#include "controller.hpp"
#include "diagnose/diagnose.hpp"
#include "setting/setting.hpp"

using namespace std;

std::string GetHomePath(){
	std::string home_path;
	char *home;
    home = getenv("HOME");
    home_path = home;
	return home_path;
}

int init_log(void)
{
	std::string str_home_path = GetHomePath();
	std::string str_log_path = str_home_path+"/auto_sort/logs/container_interface/container";
    auto container_logger = spdlog::rotating_logger_mt<spdlog::async_factory>("container_interface", str_log_path, 1024 * 1024 * 128, 20);

	auto stdout_sink = std::make_shared<spdlog::sinks::stdout_sink_mt>();
    
	container_logger->set_level(spdlog::level::trace);
	container_logger->sinks().push_back(stdout_sink); 		//增加从stdout输出
	spdlog::set_default_logger(container_logger);

	spdlog::set_pattern("[%m-%d %H:%M:%S %e] [%s:%#] [%l] %v");

	SPDLOG_INFO("initialize loging ok");
	return 0;
}

int init_setting()
{
	std::string str_home_path = GetHomePath();
	std::string str_thing_path = str_home_path + DATA_FILE_PATH + CONTAINER_FILE_NAME;
	if (setting::get_instance()->load_setting(str_thing_path.c_str()) == 0)
		exit(1);
	return 0;
}

int main(int argc, char *argv[])
{
    init_log();

    zmq::context_t context{1};

	init_setting();
	SPDLOG_INFO("container version is.{} -----------", setting::get_instance()->get_setting().version);
	diagnose_init();
	diagnose_run();

    controller::get_instance()->init(context);
	
	SPDLOG_INFO("socket init done");
	
	controller::get_instance()->run();

	while(1)
	{
		this_thread::sleep_for(chrono::seconds(1));
	}
	
    return 0;
}