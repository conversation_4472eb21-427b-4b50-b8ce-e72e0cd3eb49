﻿

/**@file  udp_socket.cpp
* @brief       基于udp的socket操作软件二次封装
* @details     NULL
* <AUTHOR>
* @date        2021-07-01
* @version     v1.2.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.2.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/06/11  <td>1.0.1    <td>lizhy     <td>初始版本，支持udp服务器建立              </tr>
* <tr><td>2021/06/22  <td>1.1.0    <td>lizhy     <td>
* -# 对socket API进行二次封装，使用class的成员函数实现socket API功能
* <tr><td>2021/07/01  <td>1.2.0    <td>lizhy     <td>
* -# 添加quick ack功能设计 
* </table>
*
**********************************************************************************
*/


#include "../plc_agent_debug.h"

#include "udp_socket.hpp"


#include <sys/types.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/udp.h>
#include <netdb.h>

#include <fcntl.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <errno.h>
#include <iostream>
#include <stdexcept>




/**@brief  udp_socket class构造函数
* @param[in]  NULL
* @return     NULL
*/
udp_socket::udp_socket() 
:m_udp_server_sockfd(-1)
,m_default_processor(100)
{
	return ;
}


/**@brief  udp_socket class析构函数，调用时关闭udp服务器的文件描述符
* @param[in]  NULL
* @return     NULL
*/
udp_socket::~udp_socket() 
{
    if (m_udp_server_sockfd >= 0) 
	{
        close(m_udp_server_sockfd);  
    }
}


/**@brief     udp 通信中 Server 构造
* @param[in]  maxWaiter --- 支持的最大客户端数量，默认16
* @return     函数执行结果
* - false     server创建失败
* - true      server创建成功
*/
bool udp_socket::udp_socket_init(int maxWaiter) 
{
    m_udp_server_sockfd = socket(PF_INET, SOCK_DGRAM, 0);
	
#ifdef udp_SOCKET_DEBUG
	OUTPUT_LOG;
#endif
	
    if (m_udp_server_sockfd < 0) 
	{
#ifdef udp_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: socket creat fail\n\n"<< std::endl;
#endif
        return false ;
    }
	
    m_default_processor = maxWaiter;

	return true;
	
}


/**@brief     udp 通信中 Server 构造
* @param[in]  NULL
* @return     函数执行结果
* - false     server创建失败
* - true      server创建成功
*/
bool udp_socket::udp_socket_init(void) 
{
    m_udp_server_sockfd = socket(PF_INET, SOCK_DGRAM, 0);
	
#ifdef udp_SOCKET_DEBUG
	OUTPUT_LOG;
	std::cout << "log>>: socket creat \n\n"<< std::endl;
#endif

    if (m_udp_server_sockfd < 0) 
	{
#ifdef udp_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: socket creat fail\n\n"<< std::endl;
#endif
        return false ;
    }
	
	return true;	
}




/**@brief     udp 通信中 Server 地址及端口绑定
* @param[in]  NULL
* @return     函数执行结果
* - false     server绑定失败
* - true      server绑定成功
*/
bool udp_socket::udp_socket_bind(void) 
{
#ifdef udp_SOCKET_DEBUG
	OUTPUT_LOG;
#endif
	if (bind(m_udp_server_sockfd, (struct sockaddr *)(&m_serv_addr), sizeof(m_serv_addr)) < 0) 
	{
#ifdef udp_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: socket bink fail\n\n"<< std::endl;
#endif

        return false;
    }
	
    return true;
}



/**@brief     向udp Server分配IP地址及端口号
* @param[in]  const std::string &ip --- 服务器IP地址
* @param[in]  int port --- 服务器端口号
* @return     函数执行结果
* - false     server设置失败
* - true      server设置成功
*/
bool udp_socket::udp_socket_server_cfg(const std::string &ip, int port) 
{
	//获取IP地址
	auto IP = ip.data();
    bzero(&m_serv_addr, sizeof(m_serv_addr));
    if (inet_pton(AF_INET, IP, &m_serv_addr.sin_addr.s_addr) < 0) 
	{
#ifdef udp_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: udp ip address converse fail\n\n"<< std::endl;
#endif

		return false;
    }

	//端口号检查
	if(!is_udp_SOCKET_PORT_VALID(port))
	{
#ifdef udp_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: port is not valid " << port << " \n\n"<< std::endl;
#endif
	}
	
    m_serv_addr.sin_family = AF_INET;
    m_serv_addr.sin_port = htons(port);

    return true;
}

bool udp_socket::udp_socket_client_cfg(const std::string &ip, int port,struct sockaddr_in &config_addr) 
{
	//获取IP地址
	auto IP = ip.data();
    bzero(&config_addr, sizeof(config_addr));
    if (inet_pton(AF_INET, IP, &config_addr.sin_addr.s_addr) < 0) 
	{
#ifdef udp_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: udp ip address converse fail\n\n"<< std::endl;
#endif

		return false;
    }

	//端口号检查
	if(!is_udp_SOCKET_PORT_VALID(port))
	{
#ifdef udp_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: port is not valid " << port << " \n\n"<< std::endl;
#endif
	}
	
    config_addr.sin_family = AF_INET;
    config_addr.sin_port = htons(port);

    return true;
}


/**@brief     设置udp 服务器 IP 地址reuse特性，软件异常停止后可以第一时间恢复该地址的使用
* @param[in]  bool option --- reuse特性开启操作
* @ref  	    true  开启地址 reuse
* @ref          false 禁止地址 reuse
* @return     函数执行结果
* - false     server设置失败
* - true      server设置成功
*/
bool udp_socket::udp_socket_set_reuseaddr(bool option) 
{
	int set_opt = option ? 1 : 0;

	if (setsockopt(m_udp_server_sockfd, SOL_SOCKET, SO_REUSEADDR, (void*)&set_opt, sizeof(set_opt)) < 0) 
	{
#ifdef udp_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: udp set reuse addr err \n\n"<< std::endl;
#endif

		return false;

	}
	
    return true;
}



/**@brief     设置udp 服务器 IP 端口reuse特性，软件异常停止后可以第一时间恢复该端口的使用
* @param[in]  bool option --- reuse特性开启操作
* @ref  	    true  开启端口 reuse
* @ref          false 禁止端口 reuse
* @return     函数执行结果
* - false     server设置失败
* - true      server设置成功
*/
bool udp_socket::udp_socket_set_reuseport(bool option) 
{
	int set_opt = option ? 1 : 0;

	if (setsockopt(m_udp_server_sockfd, SOL_SOCKET, SO_REUSEPORT, (void*)&set_opt, sizeof(set_opt)) < 0) 
	{
#ifdef udp_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: udp set reuse oprt err \n\n"<< std::endl;
#endif

		return false;

	}
	
    return true;
}



/**@brief     获取udp 服务器端口号
* @param[in]  NULl
* @return     udp 服务器端口号
*/
unsigned int udp_socket::udp_socket_get_port() const 
{

	auto port =  m_serv_addr.sin_port;;
	return static_cast<unsigned int>(port);

}


/**@brief     close 套接字
* @param[in]  int fd
* @return     函数执行结果
* - false     失败
* - true      成功
*/
bool udp_socket::udp_socket_close(int fd )
{
    return close(fd);
}



/**@brief     设置描述符为非阻塞形式
* @param[in]  int server_fd  ---  待操作描述符
* @return      函数执行结果
* - false     失败
* - true      成功
*/
bool udp_socket::udp_socket_set_nonblocking(int sock_fd)
{
	int curr_option = -1;
	int new_option = -1;

	curr_option = fcntl(sock_fd, F_GETFL);

	if(curr_option<0)
	{
#ifdef udp_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: read current option fail \n\n"<< std::endl;
#endif
        return false;
    }

	new_option = curr_option | O_NONBLOCK;

	if(fcntl(sock_fd,F_SETFL,new_option)<0)
	{
		
#ifdef udp_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: rewrite new option fail \n\n"<< std::endl;
#endif
		
        return false;
	}
	

    return true;
}



