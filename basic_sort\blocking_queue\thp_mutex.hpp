﻿



#ifndef __THREADPOOL_THP_MUTEX_HPP__
#define __THREADPOOL_THP_MUTEX_HPP__

#include <pthread.h>
#include <memory>

class thp_mutex 
{
public:

	/*!
	* @brief 构造函数
	*/
	thp_mutex();

	/*!
	* @brief 拷贝构造函数，设为delete，阻止拷贝
	*/
	thp_mutex(const thp_mutex&) = delete;

	/*!
	* @brief 赋值操作，设为delete，阻止赋值
	* @return thp_mutex& 
	*/
	thp_mutex& operator=(const thp_mutex&) = delete;

	/*!
	* @brief 析构函数
	*/
	~thp_mutex();

	/*!
	* @brief 互斥量加锁
	* @return 成功与否
	*/
	bool lock();

	/*!
	* @brief 互斥量解锁
	* @return 成功与否
	*/
	bool unlock();

	/*!
	* @brief 获取底层互斥量id的指针
	* @return 底层互斥量id的指针
	*/
	pthread_mutex_t* get_thp_mutex();

private:

	/// 底层互斥量id
	pthread_mutex_t m_mutex;

};

/*!
* @brief 自动加锁互斥量类 \class
* 底层数据类型为Mutex*，在构造函数中就进行加锁，
* 在析构函数中进行解锁
*/

class auto_lock_mutex 
{
public:

	/*!
	* @brief 构造函数
	* @param [in] mutex_ptr 用于初始化底层互斥量的互斥量指针
	*/
	explicit auto_lock_mutex(thp_mutex* mutex_ptr);

	/*!
	* @brief 拷贝构造函数，设为delete，阻止拷贝
	*/
	auto_lock_mutex(const auto_lock_mutex&) = delete;

	/*!
	* @brief 赋值操作，设为delete，阻止赋值
	* @return auto_lock_mutex& 
	*/
	auto_lock_mutex& operator=(const auto_lock_mutex&) = delete;

	/*!
	* @brief 析构函数
	*/
	~auto_lock_mutex();

    private:

        /// 底层互斥量类指针
        thp_mutex* m_mutex_ptr;

};
	



#endif
