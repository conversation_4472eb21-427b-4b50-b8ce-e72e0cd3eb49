cmake_minimum_required(VERSION 3.5)

# GIT_HASH 变量
# in case Git is not available, we default to "unknown"
set(GIT_HASH "unknown")
 
#获取git提交记录的最新一条日志
# find Git and if available set GIT_HASH variable
find_package(Git QUIET)
if(GIT_FOUND)
  execute_process(
    COMMAND ${GIT_EXECUTABLE} log -1 --pretty=format:%h
    OUTPUT_VARIABLE GIT_HASH
    OUTPUT_STRIP_TRAILING_WHITESPACE
    ERROR_QUIET
    WORKING_DIRECTORY
      ${CMAKE_CURRENT_SOURCE_DIR}
    )
else()
	message(STATUS "Git not found")
endif()

#获取时间
string(TIMESTAMP COMPILE_TIME %Y%m%d_%H%M)
#set(build_time    ${COMPILE_TIME})



message(STATUS "Git hash is ${GIT_HASH}")


SET(CROSS_COMPILE ON)

SET(CMAKE_SYSTEM_NAME Linux)

if(CROSS_COMPILE)
	SET(CMAKE_C_COMPILER "/usr/bin/arm-linux-gnueabihf-gcc")
	SET(CMAKE_CXX_COMPILER "/usr/bin/arm-linux-gnueabihf-g++")
	link_directories("../share/libs/arm/lib")
	include_directories("../share/libs/arm/include")
else()
	SET(CMAKE_C_COMPILER "/usr/bin/gcc")
	SET(CMAKE_CXX_COMPILER "/usr/bin/g++")
	link_directories("../share/libs/x86/lib")
	include_directories("../share/libs/x86/include")
	SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -m64")
endif()

SET(CMAKE_BUILD_TYPE Debug)

project(container_interface LANGUAGES CXX C)

SET(CMAKE_CXX_STANDARD 11)

SET(CMAKE_CXX_STANDARD_REQUIRED ON)

SET(CMAKE_CXX_FLAGS_DEBUG "$ENV{CXXFLAGS} -O0 -Wall -g2 -g")
SET(CMAKE_CXX_FLAGS_RELEASE "$ENV{CXXFLAGS} -O3 -Wall")

SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -pthread")

add_definitions(-Wall)

include_directories("../")

include_directories("../share/pb/nanopb" ".")

link_libraries(spdlog zmq)

add_subdirectory("../share/pb/nanopb" nanopb_binary_dir)
add_subdirectory("../share/pb/idl" idl_binary_dir)
#add_subdirectory("../share/callstack" callstack_binary_dir)
add_subdirectory("../share/lwshell" lwshell_binary_dir)

add_subdirectory(diagnose)
add_subdirectory(net)

add_subdirectory(setting)

add_executable(container_interface main.cpp can.cpp containers.cpp controller.cpp)

target_link_libraries(container_interface tcp_socket nanopb diagnose setting idl lwshell)
