#include "controller.hpp"
#include "containers.hpp"
#include "data_base/map_loader.hpp"
#include <thread>
#include <unistd.h>

#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"
#include "share/pb/idl/data_map.pb.h"
#include "share/pb/idl/data_request.pb.h"

#include "containers.hpp"
#include "exception/dev_except.hpp"

#include <zmq.h>
#include <cppzmq/zmq.hpp>

int controller::init(zmq::context_t &ctx)  //上报信息初始化 发送绑框/解绑
{
	pub_sock = new zmq::socket_t {ctx, zmq::socket_type::pub};
	pub_sock -> bind(TOPIC_CONTAINER_INFO);//地址？？

    pub_seal_sock = new zmq::socket_t (ctx, zmq::socket_type::pub);
    pub_seal_sock -> bind(TOPIC_CONTAINER_SEAL_STATE);     //接收到的格口消息
 
	sub_sock = new zmq::socket_t {ctx, zmq::socket_type::sub};
	sub_sock -> bind(TOPIC_CONTAINER_ACT);//地址？
	sub_sock -> set(zmq::sockopt::subscribe, ""); /*不过滤任何消息*/

	sub_seal_cmd_sock = new zmq::socket_t {ctx, zmq::socket_type::sub};
	sub_seal_cmd_sock -> bind(TOPIC_CONTAINER_SEAL_CMD);//地址？
	sub_seal_cmd_sock -> set(zmq::sockopt::subscribe, ""); /*不过滤任何消息*/

    sub_seal_cmd_send = new zmq::socket_t {ctx, zmq::socket_type::sub};
	sub_seal_cmd_send -> bind(TOPIC_CONTAINER_SEAL_SEND);//地址？
	sub_seal_cmd_send -> set(zmq::sockopt::subscribe, ""); /*不过滤任何消息*/
    

    int tcp_keep_alive = 1;
    int tcp_kpal_idle = 120;
    int tcp_kpal_cnt = 10;
    int tcp_kpal_intvl = 1;
    pub_container_full = new zmq::socket_t {ctx, zmq::socket_type::pub};
    zmq_setsockopt(pub_container_full, ZMQ_TCP_KEEPALIVE, &tcp_keep_alive, sizeof(tcp_keep_alive));
    zmq_setsockopt(pub_container_full, ZMQ_TCP_KEEPALIVE_IDLE, &tcp_kpal_idle, sizeof(tcp_kpal_idle));
    zmq_setsockopt(pub_container_full, ZMQ_TCP_KEEPALIVE_CNT, &tcp_kpal_cnt, sizeof(tcp_kpal_cnt));
    zmq_setsockopt(pub_container_full, ZMQ_TCP_KEEPALIVE_INTVL, &tcp_kpal_intvl, sizeof(tcp_kpal_intvl));

	pub_container_full -> bind(TOPIC_PLANE_SLOT_STATE);

	err_info_publisher = new zmq::socket_t(ctx, zmq::socket_type::pub);
	err_info_publisher->bind(TOPIC_EXCEPTION_CONTAINER);

    requester = new zmq::socket_t {ctx, zmq::socket_type::req};

    create_containers();

    init_net_to_can();

	return 0;
}

int controller::reset()
{
    std::this_thread::sleep_for(std::chrono::milliseconds(50));

    epoll_stop_flag = true;
    close(epoll_fd);

    net_recv_to_can->join();
    SPDLOG_DEBUG("wait epoll thread stop");

    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    for (auto &gp: container_groups)
        gp.can_intf.close_net();

    init_net_to_can();

    std::this_thread::sleep_for(std::chrono::milliseconds(20));

    //重启线程
    epoll_stop_flag = false;
    delete net_recv_to_can;
    net_recv_to_can = new std::thread(&controller::net_to_can_thread_recv, this);

    SPDLOG_DEBUG("can recv thread restart");

    return 0;
}

int controller::get_containers_data(data_map_containers_info &container_data)
{
	uint8_t req_msg[32];
	pb_ostream_t stream_out;
	data_request request;

    requester->connect(SERVICE_DATA_ACCESS);
	strncpy(request.key, DATA_KEY_CONTAINER_INFO, sizeof(request.key));
	request.type = data_request_cmd_READ;
	stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
	if (!pb_encode(&stream_out, data_request_fields, &request))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
		return -1;
	}
	else
		requester->send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

    zmq::message_t reply;
    pb_istream_t stream_in;
    requester->recv(reply, zmq::recv_flags::none);
    stream_in = pb_istream_from_buffer((const uint8_t *)reply.data(), reply.size());
    if (!pb_decode(&stream_in, data_map_containers_info_fields, &container_data))
    {
        SPDLOG_ERROR("pb decode error: {}", stream_in.errmsg);
        return -1;
    }

	requester->disconnect(SERVICE_DATA_ACCESS);

	return 0;
}

int controller::create_containers()
{
    data_map_containers_info container_data;
    get_containers_data(container_data);

    int containers_count = container_data.container_count;
    std::vector<data_map_container_info> container_vec;

    for (int i = 0; i < containers_count; i++)
    {
        auto &con = container_data.container[i];
        if ((con.has_rfid || con.has_satration) && con.can_addr != 0)
            container_vec.emplace_back(con);
    }

    auto f = [&](data_map_container_info con1, data_map_container_info con2) {return (con1.id < con2.id);};
    std::sort(container_vec.begin(), container_vec.end(), f);

    std::vector<container_intf> group_containers_temp;
    for (auto con_iter = container_vec.cbegin(); con_iter != container_vec.cend(); con_iter++)
    {
        group_containers_temp.emplace_back(con_iter->can_addr, con_iter->id);
        if ((con_iter + 1) == container_vec.cend() || (con_iter + 1)->can_addr == 1)
        {
            container_group gp;
            gp.id = container_groups.size() + 1;
            gp.msg_list.data.clear();                       //candev初始化完成后设置
            gp.containers.assign(group_containers_temp.begin(), group_containers_temp.end());
            gp.msg_list.mutex_ptr = std::make_shared<std::mutex> ();
            gp.msg_list.data_cv = std::make_shared<std::condition_variable> ();
            container_groups.emplace_back(gp);
            SPDLOG_DEBUG("add container to group {}:", gp.id);
            for (auto &con: group_containers_temp)
                SPDLOG_DEBUG("\t id:{}, canid:{}",con.get_container_state().box_id, con.get_canid());
            group_containers_temp.clear();
        }
    }

    auto container_count = 0;
    for (auto &gp: container_groups)
        container_count += gp.containers.size();

    SPDLOG_INFO("create {} container done", container_count);
    return container_count;
}

int controller::epoll_init()
{
    epoll_event ev;

    epoll_fd = epoll_create1(0);
    if (epoll_fd == -1)
    {
        SPDLOG_ERROR("Failed to create epoll instance.");
        return -1;
    }

    int gp_count = 1;
    for (auto &gp: container_groups)
    {
        ev.events = EPOLLIN;
        ev.data.fd = gp.can_intf.get_fd();
        if (epoll_ctl(epoll_fd, EPOLL_CTL_ADD, gp.can_intf.get_fd(), &ev))
        {
            SPDLOG_ERROR("failed to add net to can {} to epoll", gp_count++);
            return -1;
        }
    }

    SPDLOG_DEBUG("net to can epoll init done");
    return gp_count;
}

int controller::init_net_to_can()
{
    std::string server_ip = setting::get_instance()->get_setting().server_ip;
    int port_offset = 0;
    for (auto &gp: container_groups)
    {
        gp.can_intf.net_to_can_init(server_ip, SERVICE_PORT_BASE + (++port_offset));
        for (auto &con: gp.containers)
            con.set_can_dev(gp.can_intf);
        SPDLOG_DEBUG("init net_to_can, fd:{}", gp.can_intf.get_fd());
    }

    epoll_init();

    can_init_done = true;

    return 0;
}

int controller::send_container_state(box_info_multiple &boxes_state)
{
    std::lock_guard<std::mutex> lk(box_state_mutex);
    uint8_t pub_msg[box_info_multiple_size];
	pb_ostream_t stream_out;

	stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, box_info_multiple_fields, &boxes_state))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
		return -1;
	}
	pub_sock->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);

    return 0;
}

int controller::send_container_full(uint32_t container_id)
{
    std::lock_guard<std::mutex> lk(saturation_state_mutex);
    uint8_t pub_msg[slot_state_size];
	pb_ostream_t stream_out;

    slot_state state;
    state.id = container_id;
    state.st = state_FULL;

	stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, slot_state_fields, &state))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
		return -1;
	}
	pub_container_full->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);

    return 0;
}

int controller::send_container_raster_trigger(uint32_t container_id)
{
    std::lock_guard<std::mutex> lk(saturation_state_mutex);
    uint8_t pub_msg[slot_state_size];
	pb_ostream_t stream_out;

    slot_state state;
    state.id = container_id;
    state.st = state_RASTER_TRIGGERED;

	stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, slot_state_fields, &state))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
		return -1;
	}
	pub_container_full->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);

    return 0;
}

int controller::send_container_empty(uint32_t container_id)
{
    std::lock_guard<std::mutex> lk(saturation_state_mutex);
    uint8_t pub_msg[slot_state_size];
	pb_ostream_t stream_out;

    slot_state state;
    state.id = container_id;
    state.st = state_NORMAL;

	stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, slot_state_fields, &state))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
		return -1;
	}
	pub_container_full->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);

    return 0;
}

int controller::send_container_seal_state(const container_seal_state_single &state)
{
    std::lock_guard<std::mutex> lk(seal_state_mutex);
    uint8_t pub_msg[container_seal_state_single_size];
	pb_ostream_t stream_out;

	stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, container_seal_state_single_fields, &state))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
		return -1;
	}
    SPDLOG_DEBUG("send container seal state {}-{}", state.container_id, state.seal_state);

	pub_seal_sock->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);

    return 0;
}

int controller::listen_service_id(led_info &led_st)
{
	zmq::message_t msg;
	pb_istream_t stream_in;

	if (sub_sock->recv(msg, zmq::recv_flags::none))
	{
		stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
		if (!pb_decode(&stream_in, led_info_fields, &led_st))
		{
			SPDLOG_ERROR("pb decode error: {}", stream_in.errmsg);
		}
        else
        {
            SPDLOG_DEBUG("container {} recv color control: {}", led_st.id, led_st.color);
		    return 1;
        }
	}

    return 0;
}

int controller::listen_service_seal_cmd(container_seal_cmd &cmd)
{
	zmq::message_t msg;
	pb_istream_t stream_in;

	if (sub_seal_cmd_sock->recv(msg, zmq::recv_flags::none))
	{
		stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
		if (!pb_decode(&stream_in, container_seal_cmd_fields, &cmd))
		{
			SPDLOG_ERROR("pb decode error: {}", stream_in.errmsg);
		}
        else
        {
            SPDLOG_DEBUG("container {} recv seal", cmd.container_id);
		    return 1;
        }
	}

    return 0;
}
int controller::listen_thingtalk_seal_cmd(container_seal_state_single &cmd)
{
	zmq::message_t msg;
	pb_istream_t stream_in;

	if (sub_seal_cmd_send->recv(msg, zmq::recv_flags::none))
	{
		stream_in = pb_istream_from_buffer((const uint8_t *)msg.data(), msg.size());
		if (!pb_decode(&stream_in, container_seal_state_single_fields, &cmd))
		{
			SPDLOG_ERROR("pb decode error: {}", stream_in.errmsg);
		}
        else
        {
            SPDLOG_DEBUG("container {} recv thingtalk seal state {}", cmd.container_id,cmd.seal_state);
		    return 1;
        }
	}

    return 0;
}
void controller::data_parse(container_group &gp)
{
    while (true)
    {
        std::unique_lock<std::mutex> lk(*(gp.msg_list.mutex_ptr));
        gp.msg_list.data_cv->wait(lk, [&] {return !(gp.msg_list.data.empty()); });
        while (!gp.msg_list.data.empty())
        {
            bool data_legal = false;
            auto &msg = gp.msg_list.data.front();
            for (auto &con: gp.containers)
            {
                if (con.get_canid() == msg.can_id - 256)
                {
                    con.listen_recv_msg(msg.data);
                    gp.msg_list.data.pop_front();
                    data_legal = true;
                    break;
                }
            }
            if (!data_legal)
            {
                SPDLOG_WARN("group {} recv illegal data: {:x} {:x} {:x} {:x} {:x} {:x} {:x} {:x}", gp.id,
                    msg.data[0], msg.data[1], msg.data[2], msg.data[3], msg.data[4], msg.data[5], msg.data[6], msg.data[7]);
                gp.msg_list.data.pop_front();
            }
        }
    }
}

void controller::state_query_manage(container_group &gp)
{
    for (auto &con : gp.containers)
    {
        con.control_query();
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }

    for (auto &con : gp.containers)
    {
        con.control_query();
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }

    SPDLOG_DEBUG("group {} query 2 times done", gp.id);

    while (!sys_init_done)
        std::this_thread::sleep_for(std::chrono::milliseconds(50));    

    while (true)
    {
        if (debug == true)
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(20));
            continue;
        }

        for (auto &con : gp.containers)
        {
            if(con.overtime.query_execution_time()>setting::get_instance()->get_setting().query_time)    //格口相隔1S查询一次
            {
                int ret = con.control_query();
                if (!ret)
                {
                    recovering = true;
                    std::unique_lock<std::mutex> lk(reset_mutex);       //只会有一个线程执行reset
                    std::lock_guard<std::mutex> lck(led_control_queue_lock);
                   // std::lock_guard<std::mutex> lcck(led_control_list_lock);
                    SPDLOG_DEBUG("container {} can send failed, execute reset,add lock");
                    std::lock_guard<std::mutex> llk(seal_control_queue_lock);
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));            //等待所有线程执行到此处
                    if (!recovering)
                        break;

                    SPDLOG_DEBUG("container {} can send failed, execute reset", con.get_container_state().box_id);

                    reset();
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                    SPDLOG_DEBUG("reset done");
                    recovering = false;
                    break;
                }
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }
    }
}

//初始化寻卡策略：初始化后发送寻卡两次后停止寻卡并且不单独发送寻卡状态，成功获取到寻卡状态的格口置位初始化成功，
//等待三秒后获取所有格口寻卡状态，对于初始化未成功的格口置位解绑，发送状态，重新开启寻卡
void controller::state_query_thread_manage()
{
	SPDLOG_INFO("query event thread run...");

    for (auto &gp: container_groups)
    {
        new std::thread(&controller::state_query_manage, this, std::ref(gp));
    }

	std::chrono::high_resolution_clock::time_point start = std::chrono::high_resolution_clock::now();		//程序开始计时

    while(true)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        std::chrono::high_resolution_clock::time_point end = std::chrono::high_resolution_clock::now();
        std::chrono::milliseconds interval = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        if (interval.count() > 3000)
            break;
    }

    containers_state_report();
    sys_init_done = true;
}

int controller::containers_state_report()
{
    box_info_multiple boxes_state;
    boxes_state.boxes_count = 0;
    int containers_count = 0, success_count = 0;

    for (auto &gp: container_groups)
    {
        for (auto &con: gp.containers)
        {
            containers_count++;
            if (con.init_finish && con.get_container_state().box_st != box_state_ERROR)
            {
                if (con.get_container_state().box_st == box_state_BIND)
                    SPDLOG_DEBUG("No.{} container update state done, st:{}, RFID:{}",
                        con.get_container_state().box_id, con.get_container_state().box_st, con.get_container_state().RFID);
                else if (con.get_container_state().box_st == box_state_UNBIND)
                    SPDLOG_DEBUG("No.{} container update state done, st:{}", con.get_container_state().box_id, con.get_container_state().box_st);

                boxes_state.boxes[boxes_state.boxes_count++] = con.get_container_state();
                success_count++;
            }
        }
    }

    for (auto &gp: container_groups)
    {
        for (auto &con: gp.containers)
        {
            if (!con.init_finish)
            {
                SPDLOG_DEBUG("container {} init query no recv", con.get_container_state().box_id);
                boxes_state.boxes[boxes_state.boxes_count].box_id = con.get_container_state().box_id;
                boxes_state.boxes[boxes_state.boxes_count].box_st = box_state_UNBIND;
                boxes_state.boxes_count++;
            }
        }
    }

    send_container_state(boxes_state);

    SPDLOG_DEBUG("recv query from {} containers done, {} successful", containers_count, success_count);

    if (setting::get_instance()->get_setting().version == "TJ_envelope")
        for (auto &gp: container_groups)
            for (auto &con: gp.containers)
                if (con.get_seal_state() == container_seal_state_UNKNOWN)
                {
                    SPDLOG_DEBUG("container {} seal state unknow, report disable", con.get_container_state().box_id);
                    container_seal_state_single st = {con.get_container_state().box_id, container_seal_state_UNKNOWN};
                    controller::get_instance()->send_container_seal_state(st);
                }

    return success_count;
}

/**
 * 创建tcp socket server，启动接收
*/
void controller::net_to_can_thread_recv()
{
    SPDLOG_DEBUG("net_to_can_thread run");

    int max_events_count = container_groups.size();
    epoll_event events[max_events_count];
    int events_count;

    while (true)
    {
        events_count = epoll_wait(epoll_fd, events, max_events_count, 50);
        if (epoll_stop_flag)
        {
            SPDLOG_DEBUG("epoll stop");
            break;
        }

        if (events_count == 0)
            continue;

        if (events_count == -1)
        {
            SPDLOG_ERROR("failed to wait for epoll event");
            break;
        }

        bool fd_exist = false;
        for (int i = 0; i < events_count; ++i)
        {
            // 处理已连接的客户端数据
            int client_fd = events[i].data.fd;
            fd_exist = false;
            for (auto &gp: container_groups)
            {
                if (gp.can_intf.get_fd() == client_fd)
                {
                    net_to_can_recv(client_fd, gp.msg_list);
                    gp.msg_list.data_cv->notify_one();
                    fd_exist = true;
                    break;
                }
            }
            if (!fd_exist)
                SPDLOG_ERROR("can not find can with fd:{}", client_fd);
        }
    }
}

/**
 * 网转can模块收到数据解析后存储到can_frame链表中 
 */
int controller::net_to_can_recv(int fd, can_msg_list &msg)
{
    can_frame can_recv_msg;
    int frame_len = 13;
    int ret = 0;
    int pos = 0;
    uint8_t recv_buf[frame_len*200] = {0};

    memset(recv_buf,0,sizeof(recv_buf));

    ret = recv(fd, recv_buf, sizeof(recv_buf), 0);
    if(ret < frame_len)
        return -1;

    for(pos = 0; pos <= ret - frame_len;)
    {
        if(((recv_buf[pos] == 0x08) && (recv_buf[pos + 1] == 0x00) && (recv_buf[pos + 2] == 0x00)))
        {
            can_recv_msg.can_id  = recv_buf[4 + pos] | (recv_buf[3 + pos] << 8) | (recv_buf[2 + pos] << 16) | (recv_buf[1 + pos] << 24);
            can_recv_msg.data[0] = recv_buf[5 + pos];
            can_recv_msg.data[1] = recv_buf[6 + pos];
            can_recv_msg.data[2] = recv_buf[7 + pos];
            can_recv_msg.data[3] = recv_buf[8 + pos];
            can_recv_msg.data[4] = recv_buf[9 + pos];
            can_recv_msg.data[5] = recv_buf[10 + pos];
            can_recv_msg.data[6] = recv_buf[11 + pos];
            can_recv_msg.data[7] = recv_buf[12 + pos];

            pos += frame_len;

            if(can_recv_msg.can_id > 0x500)
            {
                SPDLOG_DEBUG("net_to_can recv can_id error,discard the frame data");
                continue;
            }

            //SPDLOG_DEBUG("net recv CAN frame: id:{:x}: data: {:x} {:x} {:x} {:x} {:x} {:x} {:x} {:x} ",
            //    can_recv_msg.can_id, can_recv_msg.data[0], can_recv_msg.data[1], can_recv_msg.data[2], can_recv_msg.data[3],
            //    can_recv_msg.data[4], can_recv_msg.data[5], can_recv_msg.data[6], can_recv_msg.data[7]);

            std::lock_guard<std::mutex> lk(*(msg.mutex_ptr));
            msg.data.emplace_back(can_recv_msg);
        }
        else
            pos++;
    }

    return 0;
}

void controller::data_thread_parse()
{
    SPDLOG_INFO("data parse thread run...");

    for (auto &gp: container_groups)
    {
        new std::thread(&controller::data_parse, this, std::ref(gp));
    }
}

void controller::listen_service_led_control_thread()
{
    SPDLOG_INFO("listen service led control thread run...");
    while (true)
    {
        led_info led_state;
        if (listen_service_id(led_state))
        {
            std::lock_guard<std::mutex> lock(led_control_queue_lock);
            led_control_queue.emplace(led_state);
        }
        else
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}
void controller::led_control_list_del(uint32_t id)         
{
    std::unique_lock<std::mutex> lck(led_control_list_lock);
    lck.unlock();
    for (auto con = led_control_list.begin(); con != led_control_list.end();)
    {
        if (con->info.id == id)
        {
            lck.lock();
            SPDLOG_DEBUG("container {} led control del from list", con->info.id);
            con = led_control_list.erase(con);
            lck.unlock();
        }
        else
            ++con;
    }
}
void controller::led_control_list_add(led_info led_st)         
{
    std::unique_lock<std::mutex> lck(led_control_list_lock);
    lck.unlock();
    for (auto &con: led_control_list)           
    {       
        if (con.info.id == led_st.id)
        {
            lck.lock();
            con.info = led_st;
            con.time.start();
            con.count = 0;
            SPDLOG_DEBUG("container {} led control add list:{}", con.info.id, con.info.color);
            lck.unlock();
            return;
        }
    }
    led_repet_control led_state;
    led_state.time.start();
    led_state.info = led_st;
    lck.lock();    
    led_control_list.emplace_back(led_state);
    SPDLOG_DEBUG("container {} get led control add list:{}", led_st.id, led_st.color);
    lck.unlock();
}
void controller::led_control_issue()            //todo
{
    while (true)
    {
        std::unique_lock<std::mutex> lock(led_control_queue_lock);    
        if (!led_control_queue.empty())
        {
            led_info led_state = led_control_queue.front();
            lock.unlock();
            SPDLOG_DEBUG("container {} get led control:{}", led_state.id, led_state.color);
            container_intf *con = get_container(led_state.id);
            if (con != nullptr)
            {
                led_control_list_add(led_state);
                con->control_led(led_state);               
                std::this_thread::sleep_for(std::chrono::milliseconds(50));
            }
            else
            {
                SPDLOG_DEBUG("recv wrong led control, container id:{}", led_state.id);
            }
            lock.lock();
            led_control_queue.pop();
            lock.unlock();
        }
        else
        {
//#define TEST
#ifdef TEST
            static int color = 0;
            led_info led_state;
            if (color == 1)
                color = 0;
            else
                color = 1;
            led_state.color = color;
            led_state.flash_freq = 0;
            for (auto &con: containers)
            {
                con.control_led(led_state);
                std::this_thread::sleep_for(std::chrono::milliseconds(200));
            }
#else
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
#endif
        }

    }
}
void controller::led_control_issue_repet()
{
    SPDLOG_DEBUG("container led repet run");
    while (true)
    {
       // SPDLOG_DEBUG("container led repet run ...");
        
        if (!led_control_list.empty())   //针对模块不响应  多次重发
        {
        // lck.lock();
           // SPDLOG_DEBUG("container led repet list not empty");
           std::unique_lock<std::mutex> lck(led_control_list_lock);
           /*
            for (auto &led: led_control_list)
            {
              //  SPDLOG_DEBUG("container {} repet led control:{}", led.id, led.color);
                container_intf *con = get_container(led.info.id);
                if (con != nullptr)
                {
                    if((led.time.led_repet_execution_time()>setting::get_instance()->get_setting().led_repet_control_time)&&
                    (led.count < setting::get_instance()->get_setting().led_repet_control_count))
                    {
                        con->control_led(led.info);
                        led.time.start();
                        led.count ++;
                        SPDLOG_DEBUG("container {} repet led control:{}", led.info.id, led.info.color);
                    }else
                        

                  //  std::this_thread::sleep_for(std::chrono::milliseconds(10));
                }
                else
                {
                    SPDLOG_DEBUG("led control list wrong led control, container id:{}", led.info.id);
                }
            }
            */
        for (auto it = led_control_list.begin(); it != led_control_list.end(); ) 
        {
            container_intf *con = get_container(it->info.id);
            if (con != nullptr) {
            if ((it->time.led_repet_execution_time() > setting::get_instance()->get_setting().led_repet_control_time) &&
            (it->count < setting::get_instance()->get_setting().led_repet_control_count)) {
                con->control_led(it->info);
                it->time.start();
                it->count++;
                SPDLOG_DEBUG("container {} repet led control:{}", it->info.id, it->info.color);
                ++it; // 继续下一个元素
            } else {
                it = led_control_list.erase(it); // 删除当前元素，并更新迭代器
            }
            } else {
                SPDLOG_DEBUG("led control list wrong led control, container id:{}", it->info.id);
            }
        }
          lck.unlock();
        }
        else{
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }
}
void controller::listen_service_seal_control_thread()
{
    SPDLOG_INFO("listen service seal control thread run...");
    while (true)
    {
        container_seal_cmd state;
        if (listen_service_seal_cmd(state))
        {
            std::lock_guard<std::mutex> lock(seal_control_queue_lock);
            seal_control_queue.emplace(state);
        }
        else
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}
void controller::listen_thingtalk_seal_control_thread()
{
    SPDLOG_INFO("listen thingtalk seal control thread run...");
    while (true)
    {
        container_seal_state_single state;

        if (listen_thingtalk_seal_cmd(state))
        {
            std::lock_guard<std::mutex> lock(seal_cmd_queue_lock);
            seal_cmd_queue.emplace(state);
        }
        else
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}
void controller::container_seal_cmd_issue()
{
    while (true)
    {
        
        std::unique_lock<std::mutex> lock(seal_control_queue_lock);
        if (!seal_control_queue.empty())
        {
            auto cmd = seal_control_queue.front();
            lock.unlock();
            //SPDLOG_DEBUG("container {} get led control:{}", cmd.container_id);
            container_intf *con = get_container(cmd.container_id);
            container_seal_state seal_state;
            if (con != nullptr)
            {
                seal_state = container_seal_state_CONTAIN;
                con->control_contain(seal_state);
                controller::get_instance()->send_container_seal_state({con->get_container_state().box_id, seal_state});
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
            else
            {
                SPDLOG_DEBUG("recv wrong led control, container id:{}", cmd.container_id);
            }
            lock.lock();
            seal_control_queue.pop();
            lock.unlock();
        }
        std::unique_lock<std::mutex> lk(seal_cmd_queue_lock);
        if (!seal_cmd_queue.empty())
        {
            auto cmd = seal_cmd_queue.front();
            lk.unlock();
            SPDLOG_DEBUG("container {} issue thingtalk seal control:{}", cmd.container_id,cmd.seal_state);
            container_intf *con = get_container(cmd.container_id);
            if (con != nullptr)
            {
                con->control_contain(cmd.seal_state);
                controller::get_instance()->send_container_seal_state({con->get_container_state().box_id, cmd.seal_state});
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
            else
            {
                SPDLOG_DEBUG("recv wrong thingtalk seal control, container id:{}", cmd.container_id);
            }
            lk.lock();
            seal_cmd_queue.pop();
            lk.unlock();
        }
//        else
 //       {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
 //       }
    }
}

int controller::run(void)
{
    net_recv_to_can = new std::thread(&controller::net_to_can_thread_recv, this);

    while (!can_init_done)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    container_query = new std::thread(&controller::state_query_thread_manage, this);

    parse_recv_data = new std::thread(&controller::data_thread_parse, this);

    container_led_control = new std::thread(&controller::led_control_issue, this);
    container_led_control_repet = new std::thread(&controller::led_control_issue_repet, this);

    listen_led_control = new std::thread(&controller::listen_service_led_control_thread, this);

    container_seal_control = new std::thread(&controller::container_seal_cmd_issue, this);

    listen_seal_control = new std::thread(&controller::listen_service_seal_control_thread, this);

    listen_thingtalk_seal_control = new std::thread(&controller::listen_thingtalk_seal_control_thread, this);

	return 0;
}

int controller::set_baudrate(int bdrate)
{
    int success_times = 0;
    for (auto &con : containers)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(8));
        if (con.set_baudrate(bdrate))
            success_times++;
    }
    SPDLOG_DEBUG("send baudrate setting done, {} success", success_times);

    return 0;
}

int controller::set_requery_tms(int times)
{
    int success_times = 0;
    for (auto &con : containers)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(8));
        if (con.set_requery_tms(times))
            success_times++;
    }
    SPDLOG_DEBUG("send requery times setting done, {} success", success_times);

    return 0;
}

int controller::fault_inquiry(int id)
{
    int success_times = 0;
    if (id == 0)
    {
        for (auto &con : containers)
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(8));
            if (con.fault_inquiry())
                success_times++;
        }
        SPDLOG_DEBUG("inquire fault to all containers times done, {} success", success_times);
    }
    else
    {
        for (auto &con : containers)
        {
            if (static_cast<unsigned int>(id) == con.get_container_state().box_id)
                if (con.fault_inquiry())
                    SPDLOG_DEBUG("inquire fault to container {} success", con.get_container_state().box_id);
        }
    }

    return 0;
}

int controller::exception_report(const event_exception &except)
{
	uint8_t pub_msg[event_exception_size];
	pb_ostream_t stream_out;

    stream_out = pb_ostream_from_buffer(pub_msg, sizeof(pub_msg));
	if(!pb_encode(&stream_out, event_exception_fields, &except))
	{
		SPDLOG_ERROR("pb encode error: {}", stream_out.errmsg);
		return -1;
	}

	auto ret = err_info_publisher->send(zmq::buffer(pub_msg, stream_out.bytes_written), zmq::send_flags::none);
	if(ret.has_value())
		return ret.value();
	else return -1;
}

int controller::report_rfid_access_fail_error(int id)
{
	event_exception e;
	e.which_evt_except = event_exception_except_tag; 
	e.evt_except.except = dev_except::rfid_access_fail(id);

	if (exceptions.except_occur(e.evt_except.except) > 0)
	{
		e.evt_except.except.state = exception_state_STATE_OCCURED;
		return exception_report(e);
	}

	return 0;
}

int controller::report_rfid_access_fail_recover(int id)
{
	event_exception e;
	e.which_evt_except = event_exception_except_tag; 
	e.evt_except.except = dev_except::rfid_access_fail(id);

	if (exceptions.except_reset(e.evt_except.except) > 0)
	{
		e.evt_except.except.state = exception_state_STATE_RESET;
		return exception_report(e);
	}

	return 0;
}

int controller::report_can_send_fail_error(int id)
{
	event_exception e;
	e.which_evt_except = event_exception_except_tag; 
	e.evt_except.except = dev_except::can_send_fail(id);

	if (exceptions.except_occur(e.evt_except.except) > 0)
	{
		e.evt_except.except.state = exception_state_STATE_OCCURED;
		return exception_report(e);
	}

	return 0;
}

int controller::report_can_send_fail_recover(int id)
{
	event_exception e;
	e.which_evt_except = event_exception_except_tag; 
	e.evt_except.except = dev_except::can_send_fail(id);

	if (exceptions.except_reset(e.evt_except.except) > 0)
	{
		e.evt_except.except.state = exception_state_STATE_RESET;
		return exception_report(e);
	}

	return 0;
}

int controller::report_rfid_unrecognized_error(int id)
{
	event_exception e;
	e.which_evt_except = event_exception_except_tag; 
	e.evt_except.except = dev_except::rfid_unrecognized(id);

	if (exceptions.except_occur(e.evt_except.except) > 0)
	{
		e.evt_except.except.state = exception_state_STATE_OCCURED;
		return exception_report(e);
	}

	return 0;
}

int controller::report_rfid_unrecognized_recover(int id)
{
	event_exception e;
	e.which_evt_except = event_exception_except_tag; 
	e.evt_except.except = dev_except::rfid_unrecognized(id);

	if (exceptions.except_reset(e.evt_except.except) > 0)
	{
		e.evt_except.except.state = exception_state_STATE_RESET;
		return exception_report(e);
	}

	return 0;
}

int controller::report_rfid_module_error_error(int id)
{
	event_exception e;
	e.which_evt_except = event_exception_except_tag; 
	e.evt_except.except = dev_except::rfid_module_error(id);

	if (exceptions.except_occur(e.evt_except.except) > 0)
	{
		e.evt_except.except.state = exception_state_STATE_OCCURED;
		return exception_report(e);
	}

	return 0;
}

int controller::report_rfid_module_error_recover(int id)
{
	event_exception e;
	e.which_evt_except = event_exception_except_tag; 
	e.evt_except.except = dev_except::rfid_module_error(id);

	if (exceptions.except_reset(e.evt_except.except) > 0)
	{
		e.evt_except.except.state = exception_state_STATE_RESET;
		return exception_report(e);
	}

	return 0;
}
