#ifndef __CONTAINER_SETTING_H__
#define __CONTAINER_SETTING_H__

#include <spdlog/spdlog.h>
#include "share/nlohmann_json/json.hpp"

class setting
{
    using json = nlohmann::json;

public:

    static setting *get_instance(void)
	{
		static setting instance;
		return &instance;
	}

    struct setting_data
    {
        std::string rfid_prefix;
        std::string rfid_format;
        int rfid_value_offset;
        std::string version;        //兼容现场例如颜色的特殊改动
        int rfid_lower_limit;       //rfid标签值下限
        int rfid_upper_limit;       //rfid标签值上限
        bool has_rfid_limit;
        std::string server_ip;
        int unbind_interval;
        int query_time;
        int led_repet_control_time;
        int led_repet_control_count;
    };

    const setting_data & get_setting(void) const
    {
        return settings;
    }

    int load_setting(const char *file_name);

private:

    setting_data settings;
    int load_container_setting(const char *file_name);
};

#endif