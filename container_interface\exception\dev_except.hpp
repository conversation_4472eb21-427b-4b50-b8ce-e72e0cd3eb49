#include <cstdint>
#include <string>

#include <spdlog/spdlog.h>

#include "share/pb/idl/exception.pb.h"
#include "share/exception_code.hpp"

namespace dev_except
{
	inline except_info rfid_access_fail(int n)
	{
		except_info e = {exception_src_SLOT, exception_level_ERROR, SLOT_MODULE_ACCESS_FAILED, 0, "", (uint32_t)n, exception_state_STATE_OCCURED};

		std::string text = "格口[" + std::to_string(n) + "]RFID模块访问失败:";
		strncpy(e.description, text.c_str(), sizeof(e.description));

		return e;
	}

	inline except_info can_send_fail(int n)
	{
		except_info e = {exception_src_SLOT, exception_level_WARNNING, SLOT_CAN_SEND_FAILED, 0, "", (uint32_t)n, exception_state_STATE_OCCURED};

		std::string text = "格口[" + std::to_string(n) + "]CAN发送失败:";
		strncpy(e.description, text.c_str(), sizeof(e.description));

		return e;
	}
	
	inline except_info rfid_unrecognized(int n)
	{
		except_info e = {exception_src_SLOT, exception_level_WARNNING, SLOT_RFID_UNRECOGNIZED, 0, "", (uint32_t)n, exception_state_STATE_OCCURED};

		std::string text = "格口[" + std::to_string(n) + "]RFID标签识别失败:";
		strncpy(e.description, text.c_str(), sizeof(e.description));

		return e;
	}

	inline except_info rfid_module_error(int n)
	{
		except_info e = {exception_src_SLOT, exception_level_ERROR, SLOT_RFID_MODULE_ERROR, 0, "", (uint32_t)n, exception_state_STATE_OCCURED};

		std::string text = "格口[" + std::to_string(n) + "]RFID模块主动报错:";
		strncpy(e.description, text.c_str(), sizeof(e.description));

		return e;
	}

}