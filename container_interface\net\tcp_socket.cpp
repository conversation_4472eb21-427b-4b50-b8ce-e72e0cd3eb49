﻿

/**@file  tcp_socket.cpp
* @brief       基于TCP的socket操作软件二次封装
* @details     NULL
* <AUTHOR>
* @date        2021-07-01
* @version     v1.2.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.2.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/06/11  <td>1.0.1    <td>lizhy     <td>初始版本，支持TCP服务器建立              </tr>
* <tr><td>2021/06/22  <td>1.1.0    <td>lizhy     <td>
* -# 对socket API进行二次封装，使用class的成员函数实现socket API功能
* <tr><td>2021/07/01  <td>1.2.0    <td>lizhy     <td>
* -# 添加quick ack功能设计 
* <tr><td>2021/07/23  <td>1.2.1    <td>lizhy     <td>
* -# 增加了作为client的初始化，reuse和TCP_NODELAY 处理
* </table>
*
**********************************************************************************
*/


#include "tcp_socket.hpp"


#include <sys/types.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <netdb.h>

#include <fcntl.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <errno.h>
#include <iostream>
#include <stdexcept>




/**@brief  tcp_socket class构造函数
* @param[in]  NULL
* @return     NULL
*/
tcp_socket::tcp_socket() 
:m_server_sockfd(-1)
,m_default_processor(16)
,m_default_listener(20)
{
	return ;
}


/**@brief  tcp_socket class析构函数，调用时关闭tcp服务器的文件描述符
* @param[in]  NULL
* @return     NULL
*/
tcp_socket::~tcp_socket() 
{
    if (m_server_sockfd >= 0) 
	{
        close(m_server_sockfd);  
    }
}


/**@brief     TCP 通信中 Server 构造
* @param[in]  maxWaiter --- 支持的最大客户端数量，默认16
* @return     函数执行结果
* - false     server创建失败
* - true      server创建成功
*/
bool tcp_socket::tcp_socket_init(int maxWaiter) 
{
    m_server_sockfd = socket(PF_INET, SOCK_STREAM, 0);
	
#ifdef TCP_SOCKET_DEBUG
	OUTPUT_LOG;
#endif
	
    if (m_server_sockfd < 0) 
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: socket creat fail\n\n"<< std::endl;
#endif
        return false ;
    }
	
    m_default_processor = maxWaiter;

	return true;
	
}


/**@brief     TCP 通信中 Server 构造
* @param[in]  NULL
* @return     函数执行结果
* - false     server创建失败
* - true      server创建成功
*/
bool tcp_socket::tcp_socket_init(void) 
{
    m_server_sockfd = socket(PF_INET, SOCK_STREAM, 0);
	
#ifdef TCP_SOCKET_DEBUG
	OUTPUT_LOG;
	std::cout << "log>>: socket creat \n\n"<< std::endl;
#endif

    if (m_server_sockfd < 0) 
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: socket creat fail\n\n"<< std::endl;
#endif
        return false ;
    }
	
	return true;	
}




/**@brief     TCP 通信中 Server 地址及端口绑定
* @param[in]  NULL
* @return     函数执行结果
* - false     server绑定失败
* - true      server绑定成功
*/
bool tcp_socket::tcp_socket_bind(void) 
{
#ifdef TCP_SOCKET_DEBUG
	OUTPUT_LOG;
#endif
	if (bind(m_server_sockfd, (struct sockaddr *)(&m_serv_addr), sizeof(m_serv_addr)) < 0) 
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: socket bink fail\n\n"<< std::endl;
#endif

        return false;
    }
	
    return true;
}



/**@brief     向TCP Server分配IP地址及端口号
* @param[in]  const std::string &ip --- 服务器IP地址
* @param[in]  int port --- 服务器端口号
* @return     函数执行结果
* - false     server设置失败
* - true      server设置成功
*/
bool tcp_socket::tcp_socket_server_cfg(const std::string &ip, int port) 
{
	//获取IP地址
	auto IP = ip.data();
    bzero(&m_serv_addr, sizeof(m_serv_addr));
    if (inet_pton(AF_INET, IP, &m_serv_addr.sin_addr.s_addr) < 0) 
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: tcp ip address converse fail\n\n"<< std::endl;
#endif

		return false;
    }

	//端口号检查
	if(!is_TCP_SOCKET_PORT_VALID(port))
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: port is not valid " << port << " \n\n"<< std::endl;
#endif
	}
	
    m_serv_addr.sin_family = AF_INET;
    m_serv_addr.sin_port = htons(port);

    return true;
}


/**@brief     设置TCP 服务器 TCP_NODELAY特性
* @param[in]  bool option --- TCP_NODELAY开启操作
* @ref  	    true  设置TCP NODELAY，禁能nagle算法
* @ref          false 使能nagle算法
* @return     函数执行结果
* - false     server设置失败
* - true      server设置成功
*/
bool tcp_socket::tcp_socket_set_tcp_no_delay(bool option) 
{
	int set_opt = option ? 1 : 0;

	if (setsockopt(m_server_sockfd, IPPROTO_TCP, TCP_NODELAY, (void*)&set_opt, sizeof(set_opt)) < 0) 
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: tcp set no delay err \n\n"<< std::endl;
#endif

		return false;

	}
	
    return true;
}



/**@brief     设置TCP 服务器 IP 地址reuse特性，软件异常停止后可以第一时间恢复该地址的使用
* @param[in]  bool option --- reuse特性开启操作
* @ref  	    true  开启地址 reuse
* @ref          false 禁止地址 reuse
* @return     函数执行结果
* - false     server设置失败
* - true      server设置成功
*/
bool tcp_socket::tcp_socket_set_reuseaddr(bool option) 
{
	int set_opt = option ? 1 : 0;

	if (setsockopt(m_server_sockfd, SOL_SOCKET, SO_REUSEADDR, (void*)&set_opt, sizeof(set_opt)) < 0) 
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: tcp set reuse addr err \n\n"<< std::endl;
#endif

		return false;

	}
	
    return true;
}



/**@brief     设置TCP 服务器 IP 端口reuse特性，软件异常停止后可以第一时间恢复该端口的使用
* @param[in]  bool option --- reuse特性开启操作
* @ref  	    true  开启端口 reuse
* @ref          false 禁止端口 reuse
* @return     函数执行结果
* - false     server设置失败
* - true      server设置成功
*/
bool tcp_socket::tcp_socket_set_reuseport(bool option) 
{
	int set_opt = option ? 1 : 0;

	if (setsockopt(m_server_sockfd, SOL_SOCKET, SO_REUSEPORT, (void*)&set_opt, sizeof(set_opt)) < 0) 
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: tcp set reuse oprt err \n\n"<< std::endl;
#endif

		return false;

	}
	
    return true;
}

/**@brief     设置TCP 服务器 KeepAlive特性
* @param[in]  bool option --- keepalive特性开启操作
* @ref  	    true  开启TCP keepalive
* @ref          false 关闭TCP keepalive
* @return     函数执行结果
* - false     server设置失败
* - true      server设置成功
*/
bool tcp_socket::tcp_socket_set_keep_alive(bool option) 
{
	int set_opt = option ? 1 : 0;

	if (setsockopt(m_server_sockfd, SOL_SOCKET, SO_KEEPALIVE, (void*)&set_opt, sizeof(set_opt)) < 0) 
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: tcp set keep alive err \n\n"<< std::endl;
#endif

		return false;

	}
	
    return true;
}


/**@brief     将服务器套接字转换为被动套接字，状态转换为LISTEN，等待client连接
* @param[in]  int listener --- 该套接字排队的最大连接数量
* @return     函数执行结果
* - false     server设置失败
* - true      server设置成功
*/
bool tcp_socket::tcp_socket_set_listen_on(int listener)
{

    if (listen(m_server_sockfd, listener) < 0) 
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: tcp set listen " << listener << " fail \n\n"<< std::endl;
#endif
        return false;
    }
    return true;
}

/**@brief     将服务器套接字转换为被动套接字，状态转换为LISTEN，等待client连接
* @param[in]  NULL
* @return     函数执行结果
* - false     server设置失败
* - true      server设置成功
*/
bool tcp_socket::tcp_socket_set_listen_on(void)
{
	///< 使用默认的最大连接数量
    if (listen(m_server_sockfd, m_default_listener) < 0) 
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: tcp set listen " << m_default_listener << " fail \n\n"<< std::endl;
#endif
        return false;
    }
    return true;
}


/**@brief     设置TCP 通信 quick ACK特性
* @param[in]  NULl
* @return     函数执行结果
* - false     server设置失败
* - true      server设置成功
*/
bool tcp_socket::tcp_socket_set_quick_ack(void)
{
	int option;

	if (setsockopt(m_server_sockfd, IPPROTO_TCP, TCP_QUICKACK, &option, sizeof(option)) < 0) 
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: tcp set quick ack " << m_server_sockfd << " fail \n\n"<< std::endl;
#endif
		return false;
	}

	return true;
	
}



/**@brief     获取TCP 服务器端口号
* @param[in]  NULl
* @return     TCP 服务器端口号
*/
unsigned int tcp_socket::tcp_socket_get_port() const 
{

	auto port = m_serv_addr.sin_port;
	return static_cast<unsigned int>(port);

}



/**@brief     设置 套接字 与指定IP地址/端口的连接
* @param[in]  NULl
* @return     函数执行结果
* - false     server设置失败
* - true      server设置成功
*/
bool tcp_socket::tcp_socket_connect_server(void)
{
    if (connect(m_client_sockfd, (struct sockaddr *) &m_serv_addr, sizeof(m_serv_addr)) < 0) 
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: tcp connect server fail! error "<< errno << "\n\n"<< std::endl;
#endif
		if( 106!=errno )
		{
			return false;
		}
    }
    return true;
}




/**@brief     响应客户端连接
* @param[out]  struct sockaddr_in *clnt_addr  --- 客户端 IP地址
* @return     client 描述符
*/
int tcp_socket::tcp_socket_accept_client( struct sockaddr_in *clnt_addr )
{
	int clnt_sock_fd = -1;
	socklen_t size = sizeof(struct sockaddr_in);

	clnt_sock_fd=accept(m_server_sockfd, (struct sockaddr*) (clnt_addr) , &size);
	
    if (clnt_sock_fd < 0) 
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: accept client fail\n\n"<< std::endl;
#endif
        return -1;
    }

	m_client_sockfd = clnt_sock_fd;
	
    return clnt_sock_fd;
}


/**@brief     响应客户端连接
* @param[in]  int server_fd  ---  TCP 服务器 描述符
* @param[out] struct sockaddr_in *clnt_addr  --- 客户端 IP地址
* @return     client 描述符
*/
int tcp_socket::tcp_socket_accept_client(int server_fd,  struct sockaddr_in *clnt_addr )
{
	int clnt_sock_fd = -1;
	socklen_t size = sizeof(struct sockaddr_in);

	clnt_sock_fd=accept(server_fd, (struct sockaddr*) (clnt_addr) , &size);
	
    if (clnt_sock_fd < 0) 
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: accept client fail\n\n"<< std::endl;
#endif
        return -1;
    }
	
    return clnt_sock_fd;
}


/**@brief     close 套接字
* @param[in]  int fd
* @return     函数执行结果
* - false     失败
* - true      成功
*/
bool tcp_socket::tcp_socket_close(int fd )
{
    return close(fd);
}



/**@brief     设置描述符为非阻塞形式
* @param[in]  int server_fd  ---  待操作描述符
* @return      函数执行结果
* - false     失败
* - true      成功
*/
bool tcp_socket::tcp_socket_set_nonblocking(int sock_fd)
{
	int curr_option = -1;
	int new_option = -1;

	curr_option = fcntl(sock_fd, F_GETFL);

	if(curr_option<0)
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: read current option fail \n\n"<< std::endl;
#endif
        return false;
    }

	new_option = curr_option | O_NONBLOCK;

	if(fcntl(sock_fd,F_SETFL,new_option)<0)
	{
		
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: rewrite new option fail \n\n"<< std::endl;
#endif
		
        return false;
	}
	

    return true;
}




bool tcp_socket::tcp_clnt_socket_init(void) 
{
    m_client_sockfd = socket(PF_INET, SOCK_STREAM, 0);
	
#ifdef TCP_SOCKET_DEBUG
	OUTPUT_LOG;
#endif
	
    if (m_client_sockfd < 0) 
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: socket creat fail\n\n"<< std::endl;
#endif
        return false ;
    }
	
	return true;	
}


bool tcp_socket::tcp_clnt_socket_set_reuseaddr(bool option) 
{
	int set_opt = option ? 1 : 0;

	if (setsockopt(m_client_sockfd, SOL_SOCKET, SO_REUSEADDR, (void*)&set_opt, sizeof(set_opt)) < 0) 
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: tcp set reuse addr err \n\n"<< std::endl;
#endif

		return false;

	}
	
    return true;
}


bool tcp_socket::tcp_clnt_socket_set_reuseport(bool option) 
{
	int set_opt = option ? 1 : 0;

	if (setsockopt(m_client_sockfd, SOL_SOCKET, SO_REUSEPORT, (void*)&set_opt, sizeof(set_opt)) < 0) 
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: tcp set reuse oprt err \n\n"<< std::endl;
#endif

		return false;

	}
	
    return true;
}

bool tcp_socket::tcp_clnt_socket_set_tcp_no_delay(bool option) 
{
	int set_opt = option ? 1 : 0;

	if (setsockopt(m_client_sockfd, IPPROTO_TCP, TCP_NODELAY, (void*)&set_opt, sizeof(set_opt)) < 0) 
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: tcp set no delay err \n\n"<< std::endl;
#endif

		return false;

	}
	
    return true;
}


/**@brief     设置TCP 通信 quick ACK特性
* @param[in]  NULl
* @return     函数执行结果
* - false     server设置失败
* - true      server设置成功
*/
bool tcp_socket::tcp_clnt_socket_set_quick_ack(void)
{
	int option;

	if (setsockopt(m_client_sockfd, IPPROTO_TCP, TCP_QUICKACK, &option, sizeof(option)) < 0) 
	{
#ifdef TCP_SOCKET_DEBUG
		OUTPUT_ERR;
		std::cout << "error>>: tcp set quick ack " << m_server_sockfd << " fail \n\n"<< std::endl;
#endif
		return false;
	}

	return true;
	
}


