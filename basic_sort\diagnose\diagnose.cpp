#include <thread>         // std::thread, std::thread::id, std::this_thread::get_id

#include "diagnose.hpp"
#include "tcp_console.hpp"


static void console_thread(void);

int diagnose_init(supply_package *p_m_supply0,supply_package *p_m_supply1)
{
	diag_sort_init(p_m_supply0,p_m_supply1);
	return 0; 
}

int diagnose_run(void)
{
	new std::thread(&console_thread);

	return 0;
}

static void console_thread(void)
{
	tcp_console_run(3016);
}
