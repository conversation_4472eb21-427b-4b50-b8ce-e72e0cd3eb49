#include "can.hpp"
#include <assert.h>
#include <stdio.h>
#include <string.h>
#include <ctime>

#include <spdlog/spdlog.h>

#include <unistd.h>
#include <errno.h>

int can_interface::net_to_can_init(std::string server_ip, int server_port)
{
    struct sockaddr_in clnt_addr;

    socket.tcp_socket_init();
	socket.tcp_socket_server_cfg(server_ip, server_port);
	socket.tcp_socket_set_reuseaddr(true);
	socket.tcp_socket_set_tcp_no_delay(true);		
	socket.tcp_socket_bind();
	socket.tcp_socket_set_listen_on();
    socket.tcp_socket_accept_client(&clnt_addr);

    fd = socket.tcp_clnt_socket_get_fd();// tcp_socket_get_fd();
    SPDLOG_DEBUG("net_to_can client fd: {}", fd);

    //忽略sigpipe信号
    signal(SIGPIPE, this->pipesig_handler);

	return 0;
}

#if 0
int can_interface::init_can(int iDevNum, int iMode, int iBitRate, int iFd)
{
	int iRet = 0;
	int iFlags = 0;
	
	sockaddr_can stSockAddr;
	struct ifreq stIfReq;
	char ifName[16];
	int iLoopBack      = 0; /* 0 = disabled, 1 = enabled (default) */
	int iRecvOwnMsgs = 0; /* 0 = disabled (default), 1 = enabled */
	char pCMD[CMD_LEN];

	clnt_fd = iFd;
#ifdef SURESORT_VERTICAL_VERSION
	sprintf(ifName, "%s%d", "can", iDevNum);

	// 重新关闭和开启CAN接口, 并设置CAN接口的比特率
	memset(pCMD, 0, CMD_LEN);
	snprintf(pCMD, CMD_LEN, "echo jd@ugv | sudo -S ip link set can%d down", iDevNum);
	SPDLOG_INFO("execute cmd :{}", pCMD);
	system(pCMD);

	memset(pCMD, 0, CMD_LEN);
	snprintf(pCMD, CMD_LEN, "echo jd@ugv | sudo -S ip link set can%d up type can bitrate %d", iDevNum, iBitRate);
	SPDLOG_INFO("execute cmd :{}", pCMD);
	system(pCMD);
#endif
	// 创建can socket
	fd = socket(PF_CAN, SOCK_RAW, CAN_RAW);
	if(fd < 0) {
		SPDLOG_ERROR("socket error:{}", errno);
		return -1;
	}

	strcpy(stIfReq.ifr_name, ifName);
	ioctl(fd, SIOCGIFINDEX, &stIfReq);

	stSockAddr.can_family  = PF_CAN;
	stSockAddr.can_ifindex = stIfReq.ifr_ifindex; 

	iRet = bind(fd, (struct sockaddr *)&stSockAddr, sizeof(stSockAddr));
	if(iRet < 0) {
		SPDLOG_ERROR("bind error:{}", errno);
		goto error_0;
	}

	// set socket option with O_NONBLOCK or not
	iFlags = fcntl(fd, F_GETFL, 0);
	if (iFlags == -1) {
		SPDLOG_ERROR("can{} fcntl F_GETFL fail", iDevNum);
		iRet = -1;
		goto error_0;
	}
	if (NONBLOCK == iMode) {
		iFlags |= O_NONBLOCK;
	} else {
		iFlags &= ~O_NONBLOCK;
	}	
	if (fcntl(fd, F_SETFL, iFlags) == -1)
	{
		SPDLOG_ERROR("can{} set block fail", iDevNum, errno);
		goto error_0;
	}

	// set socket option with CAN_RAW_LOOPBACK\CAN_RAW_RECV_OWN_MSGS\CAN_RAW_FILTER
	setsockopt(fd, SOL_CAN_RAW, CAN_RAW_LOOPBACK, &iLoopBack, sizeof(iLoopBack));
	setsockopt(fd, SOL_CAN_RAW, CAN_RAW_RECV_OWN_MSGS, &iRecvOwnMsgs, sizeof(iRecvOwnMsgs));

	return 0;

error_0:
	if (fd > 0) {
		close(fd);
		fd = 0;
	}
	return iRet;
}
#endif

int can_interface::set_can_filter(void *pfilter, int size)
{
	int iRet = 0;

	if (pfilter == NULL)
	{
		SPDLOG_ERROR("filter NULL");
		return -1;
	}

	iRet = setsockopt(fd, SOL_CAN_RAW, CAN_RAW_FILTER, pfilter, size);
	return iRet;
}

/**
 * can数据转换为网络数据
 */
int can_interface::can_to_net(can_frame *can_frame_msg, char *net_msg)
{
	if(can_frame_msg == nullptr || net_msg == nullptr)
		return -1;

	net_msg[0] = 0x08;
	net_msg[1] = (can_frame_msg->can_id >> 24) & 0xff;
	net_msg[2] = (can_frame_msg->can_id >> 16) & 0xff;
	net_msg[3] = (can_frame_msg->can_id >> 8) & 0xff;
	net_msg[4] = can_frame_msg->can_id & 0xff;
	net_msg[5] = can_frame_msg->data[0];
	net_msg[6] = can_frame_msg->data[1];
	net_msg[7] = can_frame_msg->data[2];
	net_msg[8] = can_frame_msg->data[3];
	net_msg[9] = can_frame_msg->data[4];
	net_msg[10] = can_frame_msg->data[5];
	net_msg[11] = can_frame_msg->data[6];
	net_msg[12] = can_frame_msg->data[7];
		
	return 0;
}

/**
 * 发送网络数据包
 */
int can_interface::can_to_net_send(can_frame *can_frame_msg)
{
	char net_msg[13] = {0};
	can_to_net(can_frame_msg, net_msg);//增加数据格式转换
	
	int ret;
    //SPDLOG_DEBUG("net fd{} send CAN frame: 0x{:x} 0x{:x} 0x{:x} 0x{:x} 0x{:x} 0x{:x} 0x{:x} 0x{:x} 0x{:x} 0x{:x} 0x{:x} 0x{:x} 0x{:x}",
	//	fd, net_msg[0], net_msg[1], net_msg[2], net_msg[3], net_msg[4], net_msg[5], net_msg[6], net_msg[7], net_msg[8],
	//	net_msg[9], net_msg[10], net_msg[11], net_msg[12]);

	ret = send(fd, net_msg, sizeof(net_msg), MSG_DONTWAIT);
	if(ret == -1)
	{
		SPDLOG_DEBUG("net send failed");
		return -1;
	}
	return ret;
}

int can_interface::close_net()
{
	close(socket.tcp_socket_get_fd());
	int ret = close(fd);
	SPDLOG_DEBUG("close clnt fd: {}", ret);
	return 0;
}

int can_interface::can_send(can_frame *can_frame_msg)
{
	int iNum = 0;

	if (can_frame_msg == NULL)
	{
		SPDLOG_ERROR("send buffer is null");
		return -1;
	}

	iNum = write(fd, can_frame_msg, sizeof(struct can_frame));
    int recv_fail_times = 0;

    while (recv_fail_times != MAX_RETRY_TIMES)
    {
        if (iNum > 0)
        {
            return 1;
        }
        else        //重发
        {
			SPDLOG_ERROR("send error, errno is:{}", errno);
            recv_fail_times++;
            usleep(100000);
			iNum = write(fd, can_frame_msg, sizeof(struct can_frame));
        }
    }

	return -1;
}

int can_interface::can_recv(struct can_frame *can_frame_msg, uint32_t waiting_usec)
{
	int iRet = 0, flag = 0;

	if (can_frame_msg == NULL)
	{
		SPDLOG_ERROR("recv buffer is null");
		return -1;
	}

#if 1
	struct timeval tv;
	fd_set read_fd;
	FD_ZERO(&read_fd);
	FD_SET(fd, &read_fd);
	tv.tv_sec = 0;

	tv.tv_usec = waiting_usec;
	flag = select(fd + 1, &read_fd, NULL, NULL, &tv);
#endif

	if (flag > 0)
	{
		iRet = read(fd, can_frame_msg, sizeof(struct can_frame));
	}
	else if (flag == -1)  //错误
	{
		SPDLOG_ERROR("fail to select errno:{}", errno);
	}

	return iRet;
}
