﻿
/**@file  	   plc_protocol.cpp
* @brief       PLC通信协议编解码处理
* @details     NULL
* <AUTHOR>
* @date        2021-11-26
* @version     v1.2.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.2.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/07/10  <td>1.0.0    <td>lizhy     <td>初始版本                         </tr>
* <tr><td>2021/11/25  <td>1.2.0    <td>lizhy     <td>
* -# 新增部分指令,适配PLC通信协议(1.5.2)版本
* </table>
*
**********************************************************************************
*/

#include "../plc_agent_debug.h"

#include "plc_proto_preprocess.hpp"
#include "plc_protocol.hpp"

#include <vector>
#include <iostream>
#include <string>
#include <stdint.h>
#include <string.h>
#include <arpa/inet.h>
#include <time.h>

using namespace std;


/**@brief     PLC通信协议中，时间戳生成函数
* @param[in]  NULL
* @return     生成的时间戳字符串
*/
string plc_protocol_generate_time_stamp(void)
{
	string time_temp;
	
	struct tm *tp; 
  	time_t t = time(NULL); 
  	tp = localtime(&t);

	time_temp += int_to_string_format_fixed_length(tp->tm_year+1900, 4);
	time_temp += int_to_string_format_fixed_length(tp->tm_mon+1, 2);
	time_temp += int_to_string_format_fixed_length(tp->tm_mday, 2);
	time_temp += int_to_string_format_fixed_length(tp->tm_hour, 2);
	time_temp += int_to_string_format_fixed_length(tp->tm_min, 2);
	time_temp += int_to_string_format_fixed_length(tp->tm_sec, 2);

	return time_temp;
}




/**@brief     PLC下发消息编码函数
* @param[out]  PLC_COMM_DOWNLOAD_MSG *msg      --- 编码输出的字符串分解结构体
* @param[in]  PLC_MSG_TYPE msg_type  --- 消息类型
* @return     NULL
*/
void plc_protocol_codec_downlink(PLC_COMM_DOWNLOAD_MSG *msg          , PLC_MSG_TYPE msg_type)
{
	uint16_t data_len_temp = 0x00;
	
	string value_spearatoe_temp = PLC_COMM_PROTOCOL_VALUE_SEPARATOE;

	msg->msg_type = PLC_PROTOCOL_SESSION_MSG_TYPE + value_spearatoe_temp + msg->msg_type + PLC_COMM_PROTOCOL_SESSION_SEPARATOE;
	data_len_temp += msg->msg_type.length();

	msg->sequence = PLC_PROTOCOL_SESSION_SEQUENCE + value_spearatoe_temp + msg->sequence + PLC_COMM_PROTOCOL_SESSION_SEPARATOE;
	data_len_temp += msg->sequence.length();

	msg->dev_id = PLC_PROTOCOL_SESSION_DEVICE_ID + value_spearatoe_temp + msg->dev_id + PLC_COMM_PROTOCOL_SESSION_SEPARATOE;
	data_len_temp += msg->dev_id.length();

	switch(msg_type)
	{
		case PLC_MSG_CTRL_LED:
			msg->down_payload_1 = PLC_PROTOCOL_SESSION_REBIN_LOCATION + value_spearatoe_temp + msg->down_payload_1 + PLC_COMM_PROTOCOL_SESSION_SEPARATOE;
			data_len_temp += msg->down_payload_1.length();
			msg->down_payload_2 = PLC_PROTOCOL_SESSION_LED_COLOUR + value_spearatoe_temp + msg->down_payload_2 ;
			data_len_temp += msg->down_payload_2.length();
			break;

		case PLC_MSG_CTRL_SWITCH:
			msg->down_payload_1 = PLC_PROTOCOL_SESSION_RAIL_NO + value_spearatoe_temp + msg->down_payload_1 + PLC_COMM_PROTOCOL_SESSION_SEPARATOE;
			data_len_temp += msg->down_payload_1.length();
			msg->down_payload_2 = PLC_PROTOCOL_SESSION_RAIL_ACTION + value_spearatoe_temp + msg->down_payload_2;
			data_len_temp += msg->down_payload_2.length();
			break;
			
		case PLC_MSG_CTRL_FEEDER:
			msg->down_payload_1 = PLC_PROTOCOL_SESSION_CONVEYOR_NO + value_spearatoe_temp + msg->down_payload_1 + PLC_COMM_PROTOCOL_SESSION_SEPARATOE;
			data_len_temp += msg->down_payload_1.length();
			msg->down_payload_2 = PLC_PROTOCOL_SESSION_DIRE + value_spearatoe_temp + msg->down_payload_2;
			data_len_temp += msg->down_payload_2.length();
			break;

		case PLC_MSG_CTRL_MAIN_LED:
			msg->down_payload_1 = PLC_PROTOCOL_SESSION_MAIN_LED_MASK + value_spearatoe_temp + msg->down_payload_1 + PLC_COMM_PROTOCOL_SESSION_SEPARATOE;
			data_len_temp += msg->down_payload_1.length();
			msg->down_payload_2 = PLC_PROTOCOL_SESSION_MAIN_LED_CMD + value_spearatoe_temp + msg->down_payload_2 + PLC_COMM_PROTOCOL_SESSION_SEPARATOE;
			data_len_temp += msg->down_payload_2.length();
			msg->down_payload_3 = PLC_PROTOCOL_SESSION_MAIN_LED_FREQ + value_spearatoe_temp + msg->down_payload_3;
			data_len_temp += msg->down_payload_3.length();
			break;

		case PLC_MSG_DEV_MODE:
			msg->down_payload_1 = PLC_PROTOCOL_SESSION_MODE + value_spearatoe_temp + msg->down_payload_1 + PLC_COMM_PROTOCOL_SESSION_SEPARATOE;;
			data_len_temp += msg->down_payload_1.length();
			msg->down_payload_2 = PLC_PROTOCOL_SESSION_MODE_SWITCH_TYPE + value_spearatoe_temp + msg->down_payload_2;
			data_len_temp += msg->down_payload_2.length();
			break;

		case PLC_MSG_CLEAN_GOODS:
			msg->down_payload_1 = PLC_PROTOCOL_SESSION_CLEAN_GOODS + value_spearatoe_temp + msg->down_payload_1;
			data_len_temp += msg->down_payload_1.length();
			break;
		
		case PLC_MSG_SCHEDU_STATUS:
			msg->down_payload_1 = PLC_PROTOCOL_SESSION_SCHEDULER_STATUS + value_spearatoe_temp + msg->down_payload_1 ;
			data_len_temp += msg->down_payload_1.length();
			break;
	}

	//msg->time_stamp = PLC_PROTOCOL_SESSION_TIME_STAMP + value_spearatoe_temp + msg->time_stamp;
	//data_len_temp += msg->time_stamp.length();
	
}




/**@brief     PLC下发ACK编码函数
* @param[out]  PLC_COMM_DOWNLOAD_MSG *msg      --- 编码输出的字符串分解结构体
* @param[in]  PLC_MSG_TYPE msg_type  --- 消息类型
* @return     NULL
*/
void plc_protocol_codec_ack(PLC_COMM_DOWNLOAD_MSG *msg          , PLC_MSG_TYPE msg_type)
{
	uint16_t data_len_temp = 0x00;
	
	string value_spearatoe_temp = PLC_COMM_PROTOCOL_VALUE_SEPARATOE;

	msg->msg_type = PLC_PROTOCOL_SESSION_MSG_TYPE + value_spearatoe_temp + msg->msg_type + PLC_COMM_PROTOCOL_SESSION_SEPARATOE;
	data_len_temp += msg->msg_type.length();

	msg->sequence = PLC_PROTOCOL_SESSION_SEQUENCE + value_spearatoe_temp + msg->sequence + PLC_COMM_PROTOCOL_SESSION_SEPARATOE;
	data_len_temp += msg->sequence.length();

	msg->dev_id = PLC_PROTOCOL_SESSION_DEVICE_ID + value_spearatoe_temp + msg->dev_id + PLC_COMM_PROTOCOL_SESSION_SEPARATOE;
	data_len_temp += msg->dev_id.length();

	msg->down_payload_1 = PLC_PROTOCOL_SESSION_ACK_MSG_TYPE + value_spearatoe_temp + msg->down_payload_1 ;
	data_len_temp += msg->down_payload_1.length();

	msg->down_payload_2 = {};
	//msg->time_stamp = {};
	
}



/**@brief     PLC下发ACK编码函数
* @param[in]  PLC_COMM_DOWNLOAD_MSG *msg_input   --- 内部字段整合完成，待拼接的MSG结构体
* @return     编码生成的字符串，用于最终网络下发
*/
string plc_protocol_data_generate(PLC_COMM_DOWNLOAD_MSG *msg_input)
{
	string temp = {};
	temp += PLC_COMM_PROTOCOL_START;
	temp += msg_input->msg_type;
	temp += msg_input->sequence;
	temp += msg_input->dev_id;
	temp += msg_input->down_payload_1;
	temp += msg_input->down_payload_2;
	temp += msg_input->down_payload_3;
	//temp += msg_input->time_stamp;
	temp += PLC_COMM_PROTOCOL_STOP;

	return temp;
}


/**@brief     PLC商法消息的解码函数，用于提取有效字符串，然后按照通信协议进行字符串分割
* @param[in]  char *msg  --- 网络接收数据，字符串类型
* @param[in]  uint16_t msg_len  --- 网络接收字符串长度
* @param[in]  vector<string> *str_vec  --- 初次字符串分割后的vector，每一个成员均为PLC通信协议中的一个字段
* @return	  字符串解码结果	
* - true	  原始数据有效，解码vector可用
* - false	  原始数据无效，解码vector不可用
*/
bool plc_protocol_decodec(char *msg, uint16_t msg_len, vector<string> *str_vec )
{
	bool result;
	uint16_t start = 0;
	uint16_t len;
	
	string msg_per_proces;
	char msg_temp[256];

	//判断{}完整性，剔除{}字符，获取通信帧有效数据内容
	result = plc_net_data_process((uint8_t *)msg, msg_len, &start, &len);
	printf("start = %d\r\n", start);
	printf("len = %d\r\n", len);
	printf("result = %d\r\n", result);
	if(result)
	{
		memcpy(msg_temp,&msg[start+1], len );
		msg_temp[len] = 0x00;
		msg_per_proces = msg_temp;
		//第一次分割，按照;将所有的字段分割开
		string_split(msg_per_proces, str_vec, PLC_COMM_PROTOCOL_SESSION_SEPARATOE);
	}

	return result;
}


