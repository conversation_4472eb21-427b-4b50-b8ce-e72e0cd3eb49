﻿
/**@file  	   plc_angent.hpp
* @brief       PLC设备管理软件的业务顶层代码
* @details     NULL
* <AUTHOR>
* @date        2021-07-15
* @version     v1.1.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.1.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/07/10  <td>1.0.0    <td>lizhy     <td>初始版本                         </tr>
* <tr><td>2021/07/15  <td>1.1.0    <td>lizhy     <td>
* -# 重新设计switch的open任务和close任务
* -# switch设备号为负数的设备任务更改还需要进一步设计
* <tr><td>2021/08/09  <td>1.2.0    <td>lizhy     <td>
* -# 添加SPDLOG 日志设计
* -# switch action 缓存队列thread周期修改为10ms
* </table>
*
**********************************************************************************
*/



#ifndef __plc_agent_HPP__
#define __plc_agent_HPP__

#include "net/tcp_socket.hpp"
#include "net/udp_socket.hpp"
#include "net/epoll_poller.hpp"

#include "blocking_queue/blocking_queue.hpp"

#include "plc_manage/plc_manage.hpp"
#include "plc_agent_config.hpp"
#include "share/pb/idl/plane_switch_state.pb.h"
#include "share/pb/idl/plane_switch_action.pb.h"
#include "share/pb/idl/plane_slot.pb.h"
#include "share/pb/idl/feeder_goods.pb.h"
#include "share/pb/idl/feeder_state.pb.h"
#include "share/pb/idl/exception.pb.h"
#include "share/pb/idl/abnormal_slot_state.pb.h"

#include "feeder/gtb_servo_dev.hpp"

#include "scheduler_msg/scheduler_msg.hpp"

#include "plc_agent_config.hpp"
#include "slot_manager/slot_manager.hpp"

#include <string>
#include <netinet/ip.h>

#include <iostream>

#include <vector>
#include <map>
#include <sys/epoll.h>
#include <functional>
#include <memory>
#include <mutex>
#include <linux/can.h>
#include <linux/can/raw.h>
#include <regex>



using namespace std;

#define FEEDER_NUM_MAX  2

#define FEEDER_COUNT    FEEDER_NUM_MAX

#define THREE_BELT
#define TWO_BELT
#define THREE_BELT_OLD
#define THREE_BELT_SPEED_H   (1.2)  // new belt


#define MILLION 								( 1000000 )
#define PLC_HEART_BEAT_TIME_OUT					( 60*1000*1000  ) 

#define TICK_TIME_PRE  10.0   
#define TICK_TIME_PRE  10.0
#define PI             (3.15926)

#ifdef  THREE_BELT       // 3段皮带版本

  #ifdef  THREE_BELT_OLD   // 3段皮带旧版本 

// 826 0.8m/s
//#define BELT1_SPEED 12390
//#define BELT2_SPEED 12390
//#define BELT3_SPEED 12390
//#define BELT4_SPEED 12390

#define BELT1_SPEED (m_feeder_info.belt1_spd)
#define BELT2_SPEED (m_feeder_info.belt2_spd)
#define BELT3_SPEED (m_feeder_info.belt3_spd)
#define BELT4_SPEED (m_feeder_info.belt4_spd)

//#define SKU_CODE_LEN (m_feeder_info.sku_code_len)
#define SKU_CODE_LEN (256)

  #else
// 3段皮带 新参数
#define BELT1_SPEED 12390
#define BELT2_SPEED (11000 * THREE_BELT_SPEED_H)
#define BELT3_SPEED (6000 * THREE_BELT_SPEED_H)
#define BELT4_SPEED (6000 * THREE_BELT_SPEED_H)

  #endif

#else
#define BELT1_SPEED (13690 * 1.1)
#define BELT2_SPEED (7610 * 1.1)
#define BELT3_SPEED (4940 * 1.2)
#define BELT4_SPEED (4940 * 1.2)
#endif

#define GOODS_TASK_ID_HEAD  "TS"

typedef enum _warning_code
{
	BELT3_SESOR_NOT_EXP_UP = 1,
	BELT4_SESOR_NOT_EXP_UP = 2,
	BELT3_SESOR_UP_NOT_EXP = 3,
	BELT4_SESOR_UP_NOT_EXP = 4,
	TASK_ID_NOT_SAME = 5,
	LENGTH_OVER_LIMIT = 6,
	HINGHT_OVER_LIMIT =7,
	SUPPLY_CMD_FAULT = 8,
	SUPPLY_NO_DOWN = 9,
	SERVER_CODE_FAULT = 10,
	GOODS_NO_READ = 11,
	GOODS_VOER_WEIGHT = 12,
	BELT4_HAS_GOODS_NO_CODE =13,
	DEV_CANIO_HEARTBEAT = 14,

	BASIC_SENSOER_LOW_TIM_OUT = 15,
	BASIC_SENSOER_UP_TIM_OUT = 16,
	BASIC_SENSOER_COM_TIM_OUT = 17,
	BASIC_NOT_RECV_TASK_TIM_OUT = 18,

}warning_code;



typedef enum _belt_enum_num
{
	BELT0,
	BELT1,
	BELT2,
	BELT3,
	BELT4,
	BELT_NUM
}belt_enum_num;

typedef enum _supply_enum_num
{
	FEEDER_ID_1,
	FEEDER_ID_2,
	FEEDER_NUM
}supply_enum_num;

typedef enum _supply_enum_cmd
{
    STOP,
	START,
	RECO,
	PAUSE,
	EMGC,
	REVE,

}supply_enum_cmd;

typedef enum _supply_enum_mod
{
	PEND,
	AUTO,
	MAN,
}supply_enum_mod;

typedef struct _net_msg
{
	int fd;
	uint16_t data_len;
	uint8_t msg_data[256];
}net_msg;
// 供包台-皮带货物
typedef struct _belt
{
   // 光幕传感器
   uint16_t sensor;
   // 光幕传感器触发计数
   uint16_t sensor_cnt;
   // 光幕传感器上升沿事件
   volatile uint16_t sensor_up;
   volatile uint16_t sensor_dn_cnt;

   // 光幕传感器触发标志
   uint16_t sensor_flag;
   // 皮带使能
   uint16_t belt_ena;
   // 皮带状态
   uint16_t belt_state;
   // 皮带长度
   volatile uint16_t belt_len;
   // 皮带起始位置
   volatile int32_t belt_start;
   // 皮带起始位置bak
   volatile int32_t belt_start2;
   // 皮带结束位置
   volatile int32_t belt_end;
   // 皮带位置
   uint32_t belt_pos;
   int32_t  belt_pos_safe;
   volatile uint8_t  belt_mux;
   // 皮带速度
   int16_t belt_spd;
   // 皮带转动距离dis
   volatile float belt_dis;
   // 用于计时
   uint16_t uCountEnd;

   int16_t code_count;
   float gear_ratio;
   float diameter;

   uint8_t scan_id;
   // 货物
   uint8_t goods = 0;
   // 货物正通过
   uint8_t goods_over;
   // 货物长度
   float goods_len;
   float goods_len_count;
   float belt_dis_count;
   // 货物在皮带时间
   uint16_t goods_time;
   // 
   char goods_code_cache[SKU_CODE_LEN];
   // 货物编码
   char goods_code[SKU_CODE_LEN];
   // 货物编码
   char goods_code_buff[5][SKU_CODE_LEN];

   uint32_t task_id;

   uint16_t sort_ready;
   // 用于取消任务的统计变量
   uint16_t cancel_num;
   uint16_t cancel_count;

   // 商品码、任务号
   sorting_task_msg task_msg = {0};

   uint16_t pub_enable;
   // 用于计时
   uint16_t ucount = 0;
   
   uint32_t supply_success = 0;
   int16_t speed_old = -1;
   // 皮带伺服状态 0：正常，1：异常，2： 未知
   uint16_t servo_state = 2;

   uint16_t weight = 0;
   volume vol;

   //std::map<int, int> sensor_state;
   uint16_t sensor_state[2];
}belt;
// 供包台-按键
typedef struct _supply_key
{
    // 按键
	uint16_t start;
	// 停止
	uint16_t stop;
    // 恢复
	uint16_t recover;
	// 暂停
	uint16_t pause;
	// 急停
	uint16_t emergency;
	// 反转
	uint16_t reverse;
    // 限高
	uint16_t limit_height;

	uint8_t key_poll_old = 0;

	uint32_t key_id_old = 0; 

    key_evt_type evt_type_old = key_evt_type_KEY_PRESSED;
}key;
// 供包台-指令
typedef struct _supply_command
{
	// 指令
	uint16_t cmd;
	// 按键
	uint16_t start;
	// 停止
	uint16_t stop;
    // 恢复
	uint16_t recover;
	// 暂停
	uint16_t pause;
	// 急停
	uint16_t emergency;
	// 反转
	uint16_t reverse;
	// 运行模式
	uint16_t run_mod;

}command;
typedef struct _goods_code_track
{
	uint16_t ucount = 0;
    char last_code[SKU_CODE_LEN] ={0}; 
	uint16_t uTime = 0;
	uint16_t sensor_up_bak = 0;
	uint32_t push_count = 0;
	uint32_t supply_cmd = 0;

	uint16_t flicker[4] = {0};
} goods_track;

typedef struct _sort_info
{
	uint16_t sensor_state;
	uint16_t ready_state;
	uint16_t task_num;
} dev_sort_info;

typedef struct _action_msg
{
	uint16_t run_dir = 3;
	uint16_t old_run_dir;
	uint16_t run_dir_config ;

	uint32_t run_time_ctrl;
	can_frame send_ctrl_frame;
	can_frame send_query_frame;

} sort_action_msg;

typedef struct _goods_taskid_msg
{
	uint32_t task_id;
	std::string str;
} goods_taskid_msg;


typedef struct _supply_package
{
	belt st_belt[5];
	key st_key; 
	command st_cmd;
	e_wkstate m_state = e_wkstate_INIT;
	uint16_t fault;
	// 1:编码器异常；2：货物传感器异常；3，超长包检测异常,4,autoscan 断开
	uint16_t warning;
	uint16_t warning_last;
	uint16_t sort_ready;
	uint16_t weight_meter;
	uint16_t vsion_scan;
	uint16_t vsion_scan_failure;
	uint16_t vsion_scan_failure2;
	uint16_t iCanSocketFd0;

	uint16_t scan_id;
	char goods_code[SKU_CODE_LEN];
	uint16_t weight;
	volume vol;

	uint16_t sound_ctrl = 0;

    goods_track goods_track_var;
    // 供包机id
	uint8_t reverse_flag = 0;
	uint8_t id = 0;;
	uint16_t ucount = 0;
	struct sockaddr_in client_ip[5];
	uint16_t dev_heartbeat = 0;
    // 为粗分增加条码缓冲队列
	sorting_task_msg m_sort_req_task = {0};
	blocking_queue<sorting_task_msg> m_sort_task_queue;
	blocking_queue<sorting_task_msg> m_sort_send_queue;
	blocking_queue<goods_taskid_msg> m_goods_msg_queue;

	// 分播控制
	dev_sort_info sort_info[2];
	sort_action_msg  action_msg;
	int16_t start[5] = {0};
	std::chrono::time_point<std::chrono::high_resolution_clock>  start_time[5];

	int16_t m_req_task_num = 0;
}supply_package;

/**
* @brief 继承字 epoll_poller，实现PLC Agent的业务顶层代码
*/
class plc_agent
:protected epoll_poller/*,public supply_manage */
,public std::enable_shared_from_this<plc_agent>
{
	
public:

	/**@brief  plc_agent class构造函数
	* @param[in]  NULL
	* @return	  NULL
	*/
	explicit plc_agent(zmq::context_t &context);


	/**@brief  plc_agent class析构函数
	* @param[in]  NULL
	* @return	  NULL
	*/
	~plc_agent();
	
	/**@brief  plc_agent class初始化函数，实现网络服务器/epoll监听/ZMQ管理及设备管理的初始化设置
	* @param[in]  NULL
	* @return	  NULL
	*/
	void plc_agent_init(server_info server, log_cfg log_config, int plc_mode,feeder_info *p_feeder_info);


	/**@brief  plc_agent class 运行函数，创建class内线程，创建类内成员线程
	* @param[in]  NULL
	* @return	  NULL
	*/
	void plc_agent_run();


	/**@brief  plc_agent class 网络节点新接入事件的处理
	* @param[in]  int fd		---   新接入的连接，文件描述符
	* @return	  NULL
	*/
	void epoll_newConnection(int fd) override;

	
	/**@brief  plc_agent class 已接入节点的消息到达事件处理
	* @param[in]  int fd		---   新接入的连接，文件描述符
	* @return	  NULL
	*/
	void epoll_existConnection(int fd) override;

	
	/**@brief  plc_agent class 主线程操作
	* @param[in]  int timeout	   ---	 epoll时间监听的超时时间设置
	* @return	  NULL
	*/
	int epoll_main_loop(int timeout) override;

	
	/**@brief  plc_agent class 心跳检测线程
	* @param[in]  NULL
	* @return	  NULL
	*/
	void plc_agent_heart_beat_thread_exe();


	/**@brief  plc_agent class 网络消息接收处理函数
	* @param[in]  int fd		---   网络通信的文件描述符
	* @return	  NULL
	*/
	void dev_ctrl_func(int fd);

	
	/**@brief  plc_agent class PLC所有switch信息的初始化操作
	* @param[in]  NULL
	* @return	  NULL
	*/	
	void plc_agent_switch_info_init(void);

	
	/**@brief  plc_agent class 定时上报PLC所有switch状态
	* @param[in]  NULL
	* @return	  NULL
	*/
	void plc_agent_timed_upload_switch_state();

	
	/**@brief  plc_agent class 单独上报某switch设备状态
	* @param[in]  int id					 ---  switch ID 
	* @param[in]  switch_state_def state	 ---  switch 状态
	* @return	  NULL
	*/
	void plc_agent_update_switch_state(int id, switch_state_def state);

	
	/**@brief  plc_agent class 更新总表内对应switch号的位置信息
	* @param[in]  int id					 ---  switch ID 
	* @param[in]  switch_position_def pos	 ---  switch 变轨刀板位置
	* @return	  NULL
	*/
	void plc_agent_update_switch_postion(int id, switch_position_def pos);


	/**@brief  plc_agent class 通过ZMQ上报各种消息及状态
	* @param[in]  NULL
	* @return	  NULL
	*/
	void plc_agent_dev_upload_msg_manage(void);
	

	/**@brief  plc_agent class PLC管理指令下发线程，由此实现PLC指令的缓存和定时下发
	* @param[in]  NULL
	* @return	  NULL
	*/
	void plc_agent_dev_download_msg_thread(void);

	void plc_agent_inner_msg_manage(void);

	void plc_agent_timed_upload_slot_state(void);

	void plc_agent_abnormal_slot_info_init(void);

	void plc_agent_abnormal_slot_info_update(slot_state state_input);
	// 供包台控制线程
    void package_supply_manage(void);
	// udp recv 线程
    void package_supply_udp_recv_msg(void);
	// udp send 线程
	void package_supply_udp_send_msg(void);
	// uart recv 线程
    void package_supply_uart_recv(void);
#if 1
	int supply_init();
	int supply_ctrl(supply_package *p_supply);
	int16_t supply_vsion_scan(supply_package *p_supply);
	// 供包机包裹码值跟踪
	int16_t supply_goods_code(supply_package *p_supply);
	// 供包台传感器信号预处理
	int16_t supply_sensor_filer(supply_package *p_supply);
	// 供包台反向运行控制
	int16_t supply_reverse_ctrl(supply_package *p_supply);
	// 第1段供包机控制
	int16_t supply_belt1_ctrl(supply_package *p_supply);
	// 第2段供包机控制
	int16_t supply_belt2_ctrl(supply_package *p_supply);
	// 第3段供包机控制
	int16_t supply_belt3_ctrl(supply_package *p_supply);
	// 第4段供包机控制
	int16_t supply_belt4_ctrl(supply_package *p_supply);

	int16_t supply_belt4_unormal_ctrl(supply_package *p_supply);

	int16_t supply_belt4_unormal2_ctrl(supply_package *p_supply);

	int16_t supply_belt_ctrl(supply_package *p_supply);
	// 扫码段控制
	int16_t supply_belt3_normal_ctrl(supply_package *p_supply);

	int16_t supply_belt3_unormal_ctrl(supply_package *p_supply);

	int16_t supply_belt3_unormal2_ctrl(supply_package *p_supply);
	// 第2段-1段
	int16_t supply_belt21_ctrl(supply_package *p_supply);
	// 皮带速度控制
	int supply_belt_speed_set(supply_package *p_supply);
	// 皮带位置获取指令
	uint32_t supply_belt_position_cmd(supply_package *p_supply);

	int supply_vsion_set(supply_package *p_supply);

	int supply_servo_can_init(uint8_t uCanid);

	int supply_canio_start(uint8_t uCanid);

	int supply_canio_set(uint8_t uCanid, bool uData, uint8_t pin);

	int supply_servo_speed_set(int iCanSocketFd0, uint8_t uCanid, int32_t uSpeed);

	int supply_servo_position_get(uint8_t uCanid);

	//void goods_info_push(supply_package *p_supply,char *pCode,uint8_t uid,uint8_t upos);
	void goods_info_push(supply_package *p_supply, char *pCode, uint8_t uid, uint8_t upos,uint32_t task_id);

	void sort_info_push(supply_package *p_supply);

	void key_info_push(supply_package *p_supply);

	void feeder_state_info_push(supply_package *p_supply);

	void supply_nocode_clear(supply_package *pst_supply);

	//void package_supply_var_init(supply_package *pst_supply);
	void supply_belt_code_get(supply_package *p_supply);

	void supply_task_cancel(supply_package *p_supply);

    std::string replenish_num_with_zero(int num, int sum_length);

	std::string creat_task_id();
	// 字符串分割及提取
	void string_split(const string& str, const string& split, vector<string>& res);
    
	int creat_rand_num(int min, int max)
    {
        return (rand() % (max - min + 1)) + min;
    }

	scheduler_manager* get_schel_msg(void)
	{
		return &m_scheduler_msg;

	}
#endif 
    void package_supply_var_init(supply_package *p_supply,uint16_t id);
	
	bool servo_ctrl(supply_package *p_supply);

	bool servo_ctrl2(supply_package *p_supply);

	int net_to_can_recv(int fd);

	int can_to_net(can_frame *can_frame_msg, char *net_msg);
	// 供包机接收到的请求分播信息
	sorting_task_msg m_sort_task_recv;
	bool m_package_over_length;
	int16_t m_package_length_max;


private:

    std::mutex list_lock;
	std::mutex belt4_lock;

	std::mutex scan_lock;

	tcp_socket m_tcp_server_socket;
	udp_socket m_udp_server_socket;
	
	std::thread *heart_beat_thread;	//心跳检测线程

	//blocking_queue<msg_queue> m_dev_heart_beat;

	std::thread *timed_upload_switch_state;	//网络数据发送线程
	std::thread *timed_upload_slot_state;	//网络数据发送线程
    // 供包台数据结构
	//supply_package st_supply;
	supply_package m_supply[FEEDER_NUM_MAX];
    int m_supply_fd[FEEDER_NUM_MAX] = {0,0};
	
	
	//supply_manage m_supply[FEEDER_NUM];
	/// 等待队列
	blocking_queue<net_msg> m_net_msg_queue;

	blocking_queue<int> m_switch_act_queue;
	blocking_queue<int> m_belt4_pos;
	blocking_queue<int> m_belt3_pos;

	blocking_queue<struct can_frame> m_can_data;
	blocking_queue<struct can_frame> m_can_send_msg_queue;

	plc_dev_manager m_dev_manager;

	switch_state_multiple m_switch_info;
	abnormal_slot_list m_abnormal_slot_info;

	scheduler_manager m_scheduler_msg;

	std::thread *plc_task_thread;	

	std::thread *plc_inner_msg_thread;	//心跳检测线程
	std::thread *package_supply_manage_thread;
	// can 接收
	std::thread *can0_recv_thread;
	// can init 
	std::thread *can0_init_thread;
	// uart 接收
    std::thread *uart_recv_thread;
	int  m_auto_scan_state;
	int16_t m_CanSocketFd0 = -1;
    std::atomic<int16_t> m_can_recv_state;
	feeder_info m_feeder_info;

	gtb_servo_dev m_servo_dev[FEEDER_NUM_MAX];
	gtb_servo_dev servo_dev[FEEDER_NUM_MAX];

	//std::unordered_map<int, gtb_servo_dev> servo_dev;
	uint64_t sys_time = 0; 

	struct sockaddr client_udp_addr;
	uint32_t client_udp_port;

	slot_manager m_dev_slot;
	slot_manager m_dev_slot1;

};//





#endif
