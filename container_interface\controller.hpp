#pragma once

#ifndef __CONTAINERS_INTERFACE_CONTROLLER_H__
#define __CONTAINERS_INTERFACE_CONTROLLER_H__

//#include <vector>
//#include <unordered_map>
#include "containers.hpp"
#include "can.hpp"
#include <thread>
#include <mutex>
#include <memory>
#include <functional>
#include <list>
#include <condition_variable>
#include <queue>
#include <ctime>
#include <spdlog/spdlog.h>
//#include <queue>
#include <condition_variable>

#include <zmq.h>
#include <cppzmq/zmq.hpp>

#ifdef __linux__
#include <linux/can.h>
#include <poll.h>

#endif

#include "share/pb/idl/container_cmd.pb.h"
#include "share/pb/idl/data_map.pb.h"
#include "share/pb/idl/plane_slot.pb.h"
#include "share/global_def.h"
#include "exception/exception_list.hpp"
#include "net/tcp_socket.hpp"

#define CAN0     0
#define CAN1     1
#define can_recv_waiting_usec 0

#define SERVICE_PORT_BASE       8000

class controller
{
public:

    static controller* get_instance(void)
    {
        static controller instance;
        return &instance;
    }

    int set_baudrate(int bdrate);
    int set_requery_tms(int times);
    int fault_inquiry(int id);
    int set_contain(const uint32_t &id)
    {
        container_seal_cmd cmd = {id};
        std::lock_guard<std::mutex> lk(seal_control_queue_lock);
        seal_control_queue.emplace(cmd);
        return 0;
    }

    int init(zmq::context_t &ctx);
    int run();

    void set_debug()
    {
        debug = !debug;
        if (debug)
            SPDLOG_DEBUG("set debug");
        else
            SPDLOG_DEBUG("set working");
    }

    struct can_dev          //todo::删除
    {
        can_interface can_intf;

        std::list<can_frame> can_recv_msg_list;
    };

    bool is_sys_init_done()
    {
        return sys_init_done;
    }

    int send_container_state(box_info_multiple &boxes_state);
    int send_container_full(uint32_t container_id);
    int send_container_raster_trigger(uint32_t container_id);
    int send_container_empty(uint32_t container_id);

    int send_container_seal_state(const container_seal_state_single &state);

//异常相关
    int report_rfid_access_fail_error(int id);
    int report_rfid_access_fail_recover(int id);

    int report_can_send_fail_error(int id);
    int report_can_send_fail_recover(int id);

    int report_rfid_unrecognized_error(int id);
    int report_rfid_unrecognized_recover(int id);

    int report_rfid_module_error_error(int id);
    int report_rfid_module_error_recover(int id);
    void led_control_list_del(uint32_t id);

private:

	int exception_report(const event_exception &except);

	zmq::socket_t *pub_sock;  //绑框/解绑发生 service是subscriber
    zmq::socket_t *pub_seal_sock;//封箱状态
	zmq::socket_t *sub_sock;  //led控制命令发生 service是publisher
    zmq::socket_t *sub_seal_cmd_sock;//封箱指令
    zmq::socket_t *sub_seal_cmd_send;//物控节点封箱指令
    zmq::socket_t *requester;   //请求地图数据用
    zmq::socket_t *pub_container_full;       //满箱上报用
    zmq::socket_t *err_info_publisher;

    std::thread *container_query;	//监听和更新状态
    std::thread *can_recv;
    std::thread *parse_recv_data;
    std::thread *container_led_control;     //控制灯状态
    std::thread *container_led_control_repet;     //控制灯状态
    std::thread *listen_led_control;     //监听物控下发的改变灯颜色
    std::thread *container_seal_control;     //控制集包
    std::thread *listen_seal_control;     //监听集包指令
    std::thread *listen_thingtalk_seal_control;     //监听集包指令
    std::thread *net_recv_to_can;     //网转can    

    can_dev can_0;
    can_dev can_1;

    tcp_socket m_tcp_server_socket_0;
    tcp_socket m_tcp_server_socket_1;

    std::list<container_intf> containers;
    struct can_msg_list
    {
        std::list<can_frame> data;
        std::shared_ptr<std::mutex> mutex_ptr;
        std::shared_ptr<std::condition_variable> data_cv;
    };

    struct container_group
    {
        int id;
        std::vector<container_intf> containers;
        can_interface can_intf;
        can_msg_list msg_list;
    };
    std::vector<container_group> container_groups;
/*
    container_intf *get_container(int id)
    {
        for (auto &con: containers)
        {
            if (con.get_container_state().box_id == static_cast<unsigned int>(id))
            {
                return &con;
            }
        }
        return nullptr;
    }
*/
    container_intf *get_container(int id)
    {
        for (auto &gp: container_groups)
        {
            for (auto &con: gp.containers)
            {
                if (con.get_container_state().box_id == static_cast<unsigned int>(id))
                {
                    return &con;
                }
            }
        }
        return nullptr;
    }

    int get_containers_data(data_map_containers_info &container_data);
    int create_containers();
    int init_net_to_can();
    int containers_state_report();

    int listen_service_id(led_info &led_st);
    int listen_service_seal_cmd(container_seal_cmd &cmd);
    int listen_thingtalk_seal_cmd(container_seal_state_single &cmd);

    void data_parse(container_group &gp);
    void state_query_thread_manage();
    void state_query_manage(container_group &gp);
    void data_thread_parse();

    void net_to_can_thread_recv();
    int net_to_can_recv(int fd, can_msg_list &msg);

    //灯控命令监听与下发
    void listen_service_led_control_thread();
    std::mutex led_control_queue_lock;
    std::queue<led_info> led_control_queue;
    std::mutex led_control_list_lock;

    struct repet_time
    {
        std::chrono::high_resolution_clock::time_point led_repet_ctrl_time;
        void start()
        {
            led_repet_ctrl_time = std::chrono::high_resolution_clock::now();
        }
        int  led_repet_execution_time()
        {
            std::chrono::high_resolution_clock::time_point end_time = std::chrono::high_resolution_clock::now();
		    std::chrono::milliseconds interval = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - led_repet_ctrl_time);

    		return interval.count();
        }
    };
    struct led_repet_control
    {
        led_info info;
        repet_time time;
        uint8_t count;
    };
    std::list<led_repet_control> led_control_list;
    void led_control_issue();

    void led_control_list_add(led_info led_st);
    void led_control_issue_repet();

    void listen_service_seal_control_thread();
    void listen_thingtalk_seal_control_thread();
    std::mutex seal_control_queue_lock;
    std::queue<container_seal_cmd> seal_control_queue;
    std::mutex seal_cmd_queue_lock;
    std::queue<container_seal_state_single> seal_cmd_queue;
    void container_seal_cmd_issue();

    int epoll_init();
    int epoll_fd;

    //异常相关

    bool debug = false;
    bool can_init_done = false;     //需要can初始化完成后再初始化格口
    bool sys_init_done = false;
    exception_list exceptions;

    std::mutex list_lock;

    std::mutex exception_mutex;
    std::mutex saturation_state_mutex;
    std::mutex box_state_mutex;
    std::mutex seal_state_mutex;

    //用于can发送失败时的恢复
    std::mutex reset_mutex;
    int reset();            //重新初始化，不丢失现有内存数据
    bool recovering = false;
    bool epoll_stop_flag = false;


    controller() {}								//隐藏构造函数
    controller(const controller &) {}				//隐藏拷贝构造函数
    controller &operator=(const controller &a) { return *this; } //隐藏赋值操作符
    ~controller() {}	
};

#endif